# WebOns Refactoring Summary: Command Pattern with Self-Executing Functions

## Overview

The refactored WebOns API demonstrates a modern Dart approach where **each command class contains its own execution logic**, eliminating the need for large switch statements and centralizing command-specific behavior.

## Key Architecture Changes

### 1. **Self-Executing Command Pattern**

Instead of a massive switch statement, each command knows how to execute itself:

```dart
sealed class WebOnCommand {
  const WebOnCommand();
  
  // Each command implements its own execution logic
  Future<Map<String, dynamic>> execute(WebOnApiContext context);
  
  factory WebOnCommand.fromString(String functionName) {
    return switch (functionName) {
      "nomoSignEvmTransaction" => const SignEvmTransactionCommand(),
      "nomoGetBalance" => const GetBalanceCommand(),
      _ => UnknownCommand(functionName),
    };
  }
}
```

### 2. **Command Implementation with Embedded Logic**

Each command class contains its specific implementation:

```dart
final class SignEvmTransactionCommand extends WebOnCommand {
  const SignEvmTransactionCommand();
  
  @override
  Future<Map<String, dynamic>> execute(WebOnApiContext context) async {
    // Permission checking
    _checkPermission(context.manifest, NomoPermission.SIGN_EVM_TRANSACTION);
    
    // Direct function call
    return await nomoSignEvmTransaction(
      context.args, 
      context.context, 
      context.manifest
    );
  }
}

final class GetBalanceCommand extends WebOnCommand {
  const GetBalanceCommand();
  
  @override
  Future<Map<String, dynamic>> execute(WebOnApiContext context) async {
    return await nomoGetBalance(context.args);
  }
}
```

### 3. **Simplified API Handler**

The main API handler becomes extremely simple:

```dart
class RefactoredWebOnApi {
  static Future<Map<String, dynamic>> handleCommand({
    required String functionName,
    required NomoManifest manifest,
    required Map<String, dynamic> args,
    required BuildContext context,
  }) async {
    final command = WebOnCommand.fromString(functionName);
    final apiContext = (
      manifest: manifest,
      args: args,
      context: context,
    );

    // Direct command execution - each command knows how to execute itself
    return await command.execute(apiContext);
  }
}
```

## Benefits of This Approach

### **1. Single Responsibility Principle**
- Each command class has one responsibility: executing its specific function
- Permission checking is embedded where needed
- No central dispatcher with mixed concerns

### **2. Open/Closed Principle**
- Adding new commands requires only creating a new command class
- No modification of existing switch statements
- Easy to extend without breaking existing code

### **3. Type Safety**
- Compile-time checking ensures all commands are handled
- Pattern matching provides exhaustive case coverage
- Records provide type-safe parameter passing

### **4. Maintainability**
- Command logic is co-located with the command definition
- Easy to find and modify specific command behavior
- Clear separation of concerns

### **5. Testability**
- Each command can be unit tested independently
- Mock contexts can be easily created
- Isolated testing of permission checking

## Implementation Examples

### **Permission-Sensitive Commands**
```dart
final class SendAssetsCommand extends WebOnCommand {
  const SendAssetsCommand();
  
  @override
  Future<Map<String, dynamic>> execute(WebOnApiContext context) async {
    // Permission check embedded in command
    _checkPermission(context.manifest, NomoPermission.SEND_ASSETS);
    return await nomoSendAssets(context.args, context.context, context.manifest);
  }
}
```

### **Simple Commands**
```dart
final class GetPlatformInfoCommand extends WebOnCommand {
  const GetPlatformInfoCommand();
  
  @override
  Future<Map<String, dynamic>> execute(WebOnApiContext context) async {
    return await nomoGetPlatformInfo();
  }
}
```

### **Error Handling Commands**
```dart
final class UnknownCommand extends WebOnCommand {
  final String functionName;
  const UnknownCommand(this.functionName);
  
  @override
  Future<Map<String, dynamic>> execute(WebOnApiContext context) async {
    throw WebOnError('Unknown function: $functionName');
  }
}
```

## Migration Strategy

### **Phase 1: Create Command Classes**
1. Define the sealed `WebOnCommand` base class
2. Create command classes for each existing API function
3. Implement the `execute` method in each command

### **Phase 2: Replace Switch Statement**
1. Replace the large if-else chain with pattern matching
2. Use the factory constructor for command creation
3. Call `command.execute()` directly

### **Phase 3: Optimize and Clean**
1. Remove unused helper functions
2. Consolidate permission checking
3. Add comprehensive tests for each command

## Code Comparison

### **Before (Current)**
```dart
Future<Map<String, dynamic>> webOnApi({...}) async {
  if (functionName == "nomoSignEvmTransaction") {
    checkNomoPermission(manifest, NomoPermission.SIGN_EVM_TRANSACTION);
    return await nomoSignEvmTransaction(args, context, manifest);
  } else if (functionName == "nomoGetBalance") {
    return await nomoGetBalance(args);
  } else if (functionName == "nomoGetEvmAddress") {
    return await nomoGetEvmAddress();
  }
  // ... 50+ more else-if statements
}
```

### **After (Refactored)**
```dart
Future<Map<String, dynamic>> webOnApi({...}) async {
  final command = WebOnCommand.fromString(functionName);
  final context = (manifest: manifest, args: args, context: context);
  return await command.execute(context);
}
```

## Key Advantages

1. **Reduced Complexity**: Main API function goes from 200+ lines to ~10 lines
2. **Better Organization**: Each command's logic is self-contained
3. **Easier Testing**: Individual commands can be tested in isolation
4. **Type Safety**: Compile-time guarantees with sealed classes and pattern matching
5. **Maintainability**: Adding/modifying commands doesn't affect other commands
6. **Performance**: No performance overhead - same function calls, better organization

This refactoring transforms a monolithic API dispatcher into a clean, maintainable, and extensible command system using modern Dart language features.
