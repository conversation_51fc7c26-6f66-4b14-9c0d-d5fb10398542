import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:nomo_app/features/scanner/pages/qrcode_webon_dialog.dart';
import 'package:nomo_app/features/webons/api/api.dart';
import 'package:nomo_app/main.dart';
import 'package:image/image.dart' as img;

double? _parseJSNumber(Map<String, dynamic> args, String key) {
  final arg = args[key];
  if (arg == null) {
    return null;
  } else if (arg is int) {
    return arg.toDouble();
  } else if (arg is double) {
    return arg;
  } else {
    throw WebOnError(
      "Unexpected type of argument '$key' - expected a number but got a ${arg.runtimeType.toString()}",
    );
  }
}

Future<Map<String, dynamic>> nomoTakePicture(Map<String, dynamic> args) async {
  final double? maxHeight = _parseJSNumber(args, "maxHeight");
  final double? maxWidth = _parseJSNumber(args, "maxWidth");
  final int? imageQuality = _parseJSNumber(args, "imageQuality")?.toInt();
  final bool mirrorImage = args["mirrorImage"] ?? false;

  final xfile = await ImagePicker().pickImage(
    source: ImageSource.camera,
    maxHeight: maxHeight,
    maxWidth: maxWidth,
    imageQuality: imageQuality,
    requestFullMetadata: true,
  );
  if (xfile == null) {
    throw WebOnError("the user did not take a picture - maybe the user cancelled the action");
  }

  final captured = img.decodeImage(await xfile.readAsBytes());
  final image = img.bakeOrientation(captured!);
  if (mirrorImage) {
    img.flipHorizontal(image);
  }
  final orientation = image.exif.imageIfd.orientation;
  final imageBytes = Uint8List.fromList(img.encodeJpg(image));
  final imageBase64 = base64.encode(imageBytes);
  final file = File(xfile.path);
  await file.writeAsBytes(imageBytes);
  return {"path": file.path, "imageBase64": imageBase64, "orientation": orientation};
}

Future<Map<String, dynamic>> nomoPickFromGallery(Map<String, dynamic> args) async {
  final double? maxHeight = _parseJSNumber(args, "maxHeight");
  final double? maxWidth = _parseJSNumber(args, "maxWidth");
  final int? imageQuality = _parseJSNumber(args, "imageQuality")?.toInt();

  final file = await ImagePicker().pickImage(
    source: ImageSource.gallery,
    maxHeight: maxHeight,
    maxWidth: maxWidth,
    imageQuality: imageQuality,
  );
  if (file == null) {
    throw WebOnError("no image picked - maybe the user cancelled the action");
  }
  final imageBytes = await file.readAsBytes();
  final imageBase64 = base64.encode(imageBytes);
  return {"path": file.path, "imageBase64": imageBase64};
}

Future<Map<String, dynamic>> nomoPickFiles(Map<String, dynamic> args) async {
  final String dialogTitle = args["dialogTitle"];
  final String rawFileType = args["fileType"];
  final FileType fileType = FileType.values.byName(rawFileType);
  final bool allowMultiple = args["allowMultiple"] ?? false;
  final bool allowCompression = args["allowCompression"] ?? true;
  final List<dynamic>? allowedExtensionsRaw = args["allowedExtensions"] ?? null;
  final List<String>? allowedExtensions = allowedExtensionsRaw?.whereType<String>().toList();

  final result = await FilePicker.platform.pickFiles(
    dialogTitle: dialogTitle,
    type: fileType,
    allowMultiple: allowMultiple,
    allowCompression: allowCompression,
    allowedExtensions: allowedExtensions,
    withData: true,
    withReadStream: false,
  );
  final rawResult = result?.files;
  if (rawResult == null || rawResult.isEmpty == true) {
    throw WebOnError("no files picked - maybe the user cancelled the action");
  }
  final List<Map<String, dynamic>> files = rawResult.map((f) {
    final bytes = f.bytes;
    if (bytes == null) {
      throw WebOnError("no bytes found for file '${f.name}'");
    }
    final bytesBase64 = base64.encode(bytes);
    return {
      "path": f.path,
      "name": f.name,
      "size": f.size,
      "bytesBase64": bytesBase64,
      "identifier": f.identifier,
    };
  }).toList();

  return {"files": files};
}

Future<Map<String, dynamic>> nomoQrScan(BuildContext context) async {
  final Completer<Map<String, dynamic>> completer = Completer<Map<String, dynamic>>();

  showDialog(
    context: context,
    builder: (BuildContext context) {
      return QrCodeWebOnDialog();
    },
  ).then((_) {
    final qrCode = $ref.read(scannedCodesProvider);
    if (qrCode.isEmpty) {
      completer.completeError(
        WebOnError("no qrCode scanned - probably the user cancelled the dialog?"),
      );
    } else {
      completer.complete({"qrCode": qrCode.join(",")});
    }
  });

  return completer.future;
}
