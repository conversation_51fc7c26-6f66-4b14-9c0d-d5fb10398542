// Example of refactored WebOn API using modern Dart features
import 'package:flutter/material.dart';
import 'package:nomo_app/features/webons/manifest/nomo_manifest.dart';
import 'package:nomo_app/features/webons/manifest/nomo_permissions.dart';
// Import the actual API functions (these would be imported from the real modules)
// For this example, we'll create placeholder functions

// Placeholder API functions (these would be imported from actual modules)
void nomoNativeLog(Map<String, dynamic> args) {
  print('Log: ${args['message']}');
}

Future<Map<String, dynamic>> nomoGetPlatformInfo() async {
  return {'platform': 'example', 'version': '1.0.0'};
}

Map<String, dynamic> nomoNavigateToWallet(Map<String, dynamic> args) {
  return {'navigated': true};
}

Future<Map<String, dynamic>> nomoGetMessengerAddress() async {
  return {'address': '0x123...', 'pubKey': 'abc123'};
}

Future<Map<String, dynamic>> nomoSignEvmTransaction(
  Map<String, dynamic> args,
  BuildContext context,
  NomoManifest manifest,
) async {
  return {'signature': '0xabc123...', 'txHash': '0xdef456...'};
}

Future<Map<String, dynamic>> nomoGetBalance(Map<String, dynamic> args) async {
  return {'balance': '100.0', 'symbol': 'ETH'};
}

Future<Map<String, dynamic>> nomoGetEvmAddress() async {
  return {'address': '0x123...'};
}

Future<Map<String, dynamic>> nomoGetWalletAddresses() async {
  return {
    'addresses': {'ETH': '0x123...', 'BTC': 'bc1q...'},
  };
}

Future<Map<String, dynamic>> nomoQrScan(BuildContext context) async {
  return {'scannedData': 'example_qr_data'};
}

Future<Map<String, dynamic>> nomoSendAssets(
  Map<String, dynamic> args,
  BuildContext context,
  NomoManifest manifest,
) async {
  return {'txHash': '0x789...', 'success': true};
}

// 1. SEALED CLASSES FOR API COMMANDS WITH EXECUTION
sealed class WebOnCommand {
  const WebOnCommand();

  // Factory constructor using pattern matching
  factory WebOnCommand.fromString(String functionName) {
    return switch (functionName) {
      "nomoNativeLog" => const LogCommand(),
      "nomoGetPlatformInfo" => const GetPlatformInfoCommand(),
      "nomoNavigateToWallet" => const NavigateToWalletCommand(),
      "nomoGetMessengerAddress" => const GetMessengerAddressCommand(),
      "nomoSignEvmTransaction" => const SignEvmTransactionCommand(),
      "nomoGetBalance" => const GetBalanceCommand(),
      "nomoGetEvmAddress" => const GetEvmAddressCommand(),
      "nomoGetWalletAddresses" => const GetWalletAddressesCommand(),
      "nomoQrScan" => const QrScanCommand(),
      "nomoSendAssets" => const SendAssetsCommand(),
      _ => UnknownCommand(functionName),
    };
  }

  // Abstract method for execution - each command knows how to execute itself
  Future<Map<String, dynamic>> execute(WebOnApiContext context);
}

// Command implementations with execution logic
final class LogCommand extends WebOnCommand {
  const LogCommand();

  @override
  Future<Map<String, dynamic>> execute(WebOnApiContext context) async {
    nomoNativeLog(context.args);
    return {};
  }
}

final class GetPlatformInfoCommand extends WebOnCommand {
  const GetPlatformInfoCommand();

  @override
  Future<Map<String, dynamic>> execute(WebOnApiContext context) async {
    return await nomoGetPlatformInfo();
  }
}

final class NavigateToWalletCommand extends WebOnCommand {
  const NavigateToWalletCommand();

  @override
  Future<Map<String, dynamic>> execute(WebOnApiContext context) async {
    return nomoNavigateToWallet(context.args);
  }
}

final class GetMessengerAddressCommand extends WebOnCommand {
  const GetMessengerAddressCommand();

  @override
  Future<Map<String, dynamic>> execute(WebOnApiContext context) async {
    // Check permissions first
    _checkPermission(context.manifest, NomoPermission.SEND_MESSAGE);
    return await nomoGetMessengerAddress();
  }
}

final class SignEvmTransactionCommand extends WebOnCommand {
  const SignEvmTransactionCommand();

  @override
  Future<Map<String, dynamic>> execute(WebOnApiContext context) async {
    // Check permissions first
    _checkPermission(context.manifest, NomoPermission.SIGN_EVM_TRANSACTION);
    return await nomoSignEvmTransaction(context.args, context.context, context.manifest);
  }
}

final class GetBalanceCommand extends WebOnCommand {
  const GetBalanceCommand();

  @override
  Future<Map<String, dynamic>> execute(WebOnApiContext context) async {
    return await nomoGetBalance(context.args);
  }
}

final class GetEvmAddressCommand extends WebOnCommand {
  const GetEvmAddressCommand();

  @override
  Future<Map<String, dynamic>> execute(WebOnApiContext context) async {
    return await nomoGetEvmAddress();
  }
}

final class GetWalletAddressesCommand extends WebOnCommand {
  const GetWalletAddressesCommand();

  @override
  Future<Map<String, dynamic>> execute(WebOnApiContext context) async {
    return await nomoGetWalletAddresses();
  }
}

final class QrScanCommand extends WebOnCommand {
  const QrScanCommand();

  @override
  Future<Map<String, dynamic>> execute(WebOnApiContext context) async {
    // Check permissions first
    _checkPermission(context.manifest, NomoPermission.CAMERA);
    return await nomoQrScan(context.context);
  }
}

final class SendAssetsCommand extends WebOnCommand {
  const SendAssetsCommand();

  @override
  Future<Map<String, dynamic>> execute(WebOnApiContext context) async {
    // Check permissions first
    _checkPermission(context.manifest, NomoPermission.SEND_ASSETS);
    return await nomoSendAssets(context.args, context.context, context.manifest);
  }
}

final class UnknownCommand extends WebOnCommand {
  final String functionName;
  const UnknownCommand(this.functionName);

  @override
  Future<Map<String, dynamic>> execute(WebOnApiContext context) async {
    throw WebOnError('Unknown function: $functionName');
  }
}

// Helper function for permission checking
void _checkPermission(NomoManifest manifest, NomoPermission permission) {
  final permissionString = "nomo.permission.${permission.name}";
  final hasPermission = manifest.permissions.contains(permissionString);
  if (!hasPermission) {
    throw WebOnError("${permissionString} is missing in manifest");
  }
}

// 2. ENHANCED RECORDS FOR API CONTEXT WITH WEBVIEW ISOLATION
typedef WebOnApiContext = ({
  NomoManifest manifest,
  Map<String, dynamic> args,
  BuildContext context,
  String webViewInstanceId, // Unique identifier for the WebView instance
  WebViewState? webViewState, // Optional state for WebView-specific operations
});

// WebView instance management
typedef WebViewState = ({
  String instanceId,
  String currentUrl,
  bool isLoading,
  Map<String, dynamic> metadata,
});

// Context provider for WebView instances
class WebViewContextProvider {
  static final Map<String, BuildContext> _contexts = {};
  static final Map<String, WebViewState> _states = {};

  static void registerWebView({
    required String instanceId,
    required BuildContext context,
    required String initialUrl,
  }) {
    _contexts[instanceId] = context;
    _states[instanceId] = (
      instanceId: instanceId,
      currentUrl: initialUrl,
      isLoading: false,
      metadata: {},
    );
  }

  static void unregisterWebView(String instanceId) {
    _contexts.remove(instanceId);
    _states.remove(instanceId);
  }

  static BuildContext? getContext(String instanceId) {
    return _contexts[instanceId];
  }

  static WebViewState? getState(String instanceId) {
    return _states[instanceId];
  }

  static void updateState(String instanceId, WebViewState newState) {
    _states[instanceId] = newState;
  }

  static List<String> get activeInstances => _contexts.keys.toList();
}

// 3. RESULT TYPE WITH PATTERN MATCHING
sealed class WebOnResult<T> {
  const WebOnResult();
}

final class WebOnSuccess<T> extends WebOnResult<T> {
  final T data;
  const WebOnSuccess(this.data);
}

final class WebOnError<T> extends WebOnResult<T> {
  final String message;
  final Exception? exception;
  const WebOnError(this.message, [this.exception]);
}

// 4. ENHANCED API HANDLER WITH WEBVIEW INSTANCE ISOLATION
class RefactoredWebOnApi {
  static Future<Map<String, dynamic>> handleCommand({
    required String functionName,
    required NomoManifest manifest,
    required Map<String, dynamic> args,
    required BuildContext context,
    required String webViewInstanceId, // Now required for proper isolation
  }) async {
    final command = WebOnCommand.fromString(functionName);

    // Get WebView-specific context and state
    final webViewContext = WebViewContextProvider.getContext(webViewInstanceId);
    final webViewState = WebViewContextProvider.getState(webViewInstanceId);

    // Use WebView-specific context if available, fallback to provided context
    final effectiveContext = webViewContext ?? context;

    final apiContext = (
      manifest: manifest,
      args: args,
      context: effectiveContext,
      webViewInstanceId: webViewInstanceId,
      webViewState: webViewState,
    );

    // Direct command execution - each command knows how to execute itself
    try {
      return await command.execute(apiContext);
    } catch (e) {
      throw WebOnError('Command execution failed: $e');
    }
  }

  // Convenience method for commands that need WebView-specific operations
  static Future<Map<String, dynamic>> handleWebViewCommand({
    required String functionName,
    required NomoManifest manifest,
    required Map<String, dynamic> args,
    required String webViewInstanceId,
  }) async {
    final webViewContext = WebViewContextProvider.getContext(webViewInstanceId);
    if (webViewContext == null) {
      throw WebOnError('WebView instance $webViewInstanceId not found or context unavailable');
    }

    return handleCommand(
      functionName: functionName,
      manifest: manifest,
      args: args,
      context: webViewContext,
      webViewInstanceId: webViewInstanceId,
    );
  }
}

// 5. EXTENSION METHODS FOR BETTER API
extension WebOnManifestExtensions on NomoManifest {
  bool hasPermission(NomoPermission permission) {
    final permissionString = "nomo.permission.${permission.name}";
    return permissions.contains(permissionString);
  }

  // Using records for multiple return values
  ({String id, String name, String version}) get identity => (
    id: webon_id,
    name: webon_name,
    version: webon_version,
  );
}

// 6. ENHANCED ERROR HANDLING WITH SEALED CLASSES
sealed class WebOnApiError implements Exception {
  const WebOnApiError();
}

final class PermissionDeniedError extends WebOnApiError {
  final NomoPermission permission;
  const PermissionDeniedError(this.permission);

  @override
  String toString() => 'Permission denied: ${permission.name}';
}

final class InvalidArgumentError extends WebOnApiError {
  final String argument;
  final String reason;
  const InvalidArgumentError(this.argument, this.reason);

  @override
  String toString() => 'Invalid argument $argument: $reason';
}

final class NetworkError extends WebOnApiError {
  final String url;
  final int? statusCode;
  const NetworkError(this.url, [this.statusCode]);

  @override
  String toString() => 'Network error for $url${statusCode != null ? ' (${statusCode})' : ''}';
}

// 7. WEBVIEW-AWARE COMMANDS
final class NavigateWebViewCommand extends WebOnCommand {
  const NavigateWebViewCommand();

  @override
  Future<Map<String, dynamic>> execute(WebOnApiContext context) async {
    final url = context.args['url'] as String?;
    if (url == null) {
      throw WebOnError('URL is required for navigation');
    }

    // Use WebView-specific context for navigation
    final webViewState = context.webViewState;
    if (webViewState != null) {
      // Update WebView state
      WebViewContextProvider.updateState(
        context.webViewInstanceId,
        webViewState.copyWith(currentUrl: url, isLoading: true),
      );
    }

    return {'navigated': true, 'url': url, 'instanceId': context.webViewInstanceId};
  }
}

final class GetWebViewStateCommand extends WebOnCommand {
  const GetWebViewStateCommand();

  @override
  Future<Map<String, dynamic>> execute(WebOnApiContext context) async {
    final state = context.webViewState;
    if (state == null) {
      throw WebOnError('WebView state not available for instance ${context.webViewInstanceId}');
    }

    return {
      'instanceId': state.instanceId,
      'currentUrl': state.currentUrl,
      'isLoading': state.isLoading,
      'metadata': state.metadata,
    };
  }
}

// Extension for WebViewState to support copying with changes
extension WebViewStateExtension on WebViewState {
  WebViewState copyWith({
    String? currentUrl,
    bool? isLoading,
    Map<String, dynamic>? metadata,
  }) {
    return (
      instanceId: instanceId,
      currentUrl: currentUrl ?? this.currentUrl,
      isLoading: isLoading ?? this.isLoading,
      metadata: metadata ?? this.metadata,
    );
  }
}

// 8. USAGE EXAMPLES WITH MULTIPLE WEBVIEW INSTANCES
class WebOnApiUsageExample {
  static Future<void> demonstrateMultiWebViewUsage() async {
    final manifest = NomoManifest(
      nomo_manifest_version: '1.0.0',
      webon_id: 'example.webon',
      webon_name: 'Example WebOn',
      webon_version: '1.0.0',
      dependencies: [],
      permissions: ['nomo.permission.SEND_MESSAGE'],
      webon_url: 'https://example.com',
    );

    // Simulate multiple WebView instances
    const webView1Id = 'webview_instance_1';
    const webView2Id = 'webview_instance_2';

    // In real usage, these would be called when WebViews are created
    // WebViewContextProvider.registerWebView(
    //   instanceId: webView1Id,
    //   context: actualContext1,
    //   initialUrl: 'https://example1.com',
    // );

    try {
      // API call for specific WebView instance
      final result1 = await RefactoredWebOnApi.handleWebViewCommand(
        functionName: 'nomoGetMessengerAddress',
        manifest: manifest,
        args: {},
        webViewInstanceId: webView1Id,
      );
      print('WebView 1 Result: $result1');

      // API call for different WebView instance
      final result2 = await RefactoredWebOnApi.handleWebViewCommand(
        functionName: 'nomoGetBalance',
        manifest: manifest,
        args: {'symbol': 'ETH'},
        webViewInstanceId: webView2Id,
      );
      print('WebView 2 Result: $result2');
    } catch (e) {
      print('API Error: $e');
    }
  }

  static void demonstrateWebViewLifecycle(BuildContext context1, BuildContext context2) {
    // Register multiple WebView instances
    WebViewContextProvider.registerWebView(
      instanceId: 'main_webview',
      context: context1,
      initialUrl: 'https://main.example.com',
    );

    WebViewContextProvider.registerWebView(
      instanceId: 'popup_webview',
      context: context2,
      initialUrl: 'https://popup.example.com',
    );

    print('Active WebView instances: ${WebViewContextProvider.activeInstances}');

    // Clean up when WebViews are disposed
    // WebViewContextProvider.unregisterWebView('popup_webview');
  }
}
