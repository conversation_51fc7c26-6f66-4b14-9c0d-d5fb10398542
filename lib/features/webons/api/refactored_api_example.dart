// Example of refactored WebOn API using modern Dart features
import 'package:flutter/material.dart';
import 'package:nomo_app/features/webons/manifest/nomo_manifest.dart';
import 'package:nomo_app/features/webons/manifest/nomo_permissions.dart';

// 1. SEALED CLASSES FOR API COMMANDS
sealed class WebOnCommand {
  const WebOnCommand();

  // Factory constructor using pattern matching
  factory WebOnCommand.fromString(String functionName) {
    return switch (functionName) {
      "nomoNativeLog" => const LogCommand(),
      "nomoGetPlatformInfo" => const GetPlatformInfoCommand(),
      "nomoNavigateToWallet" => const NavigateToWalletCommand(),
      "nomoGetMessengerAddress" => const GetMessengerAddressCommand(),
      "nomoSignEvmTransaction" => const SignEvmTransactionCommand(),
      "nomoGetBalance" => const GetBalanceCommand(),
      _ => UnknownCommand(functionName),
    };
  }
}

// Command implementations
final class LogCommand extends WebOnCommand {
  const LogCommand();
}

final class GetPlatformInfoCommand extends WebOnCommand {
  const GetPlatformInfoCommand();
}

final class NavigateToWalletCommand extends WebOnCommand {
  const NavigateToWalletCommand();
}

final class GetMessengerAddressCommand extends WebOnCommand {
  const GetMessengerAddressCommand();
}

final class SignEvmTransactionCommand extends WebOnCommand {
  const SignEvmTransactionCommand();
}

final class GetBalanceCommand extends WebOnCommand {
  const GetBalanceCommand();
}

final class UnknownCommand extends WebOnCommand {
  final String functionName;
  const UnknownCommand(this.functionName);
}

// 2. RECORDS FOR API CONTEXT
typedef WebOnApiContext = ({
  NomoManifest manifest,
  Map<String, dynamic> args,
  BuildContext context,
});

// 3. RESULT TYPE WITH PATTERN MATCHING
sealed class WebOnResult<T> {
  const WebOnResult();
}

final class WebOnSuccess<T> extends WebOnResult<T> {
  final T data;
  const WebOnSuccess(this.data);
}

final class WebOnError<T> extends WebOnResult<T> {
  final String message;
  final Exception? exception;
  const WebOnError(this.message, [this.exception]);
}

// 4. ENHANCED API HANDLER WITH PATTERN MATCHING
class RefactoredWebOnApi {
  static Future<Map<String, dynamic>> handleCommand({
    required String functionName,
    required NomoManifest manifest,
    required Map<String, dynamic> args,
    required BuildContext context,
  }) async {
    final command = WebOnCommand.fromString(functionName);
    final apiContext = (
      manifest: manifest,
      args: args,
      context: context,
    );

    final result = await _executeCommand(command, apiContext);

    return switch (result) {
      WebOnSuccess(data: final data) => data,
      WebOnError(message: final msg, exception: final ex) => throw Exception(
        'WebOn API Error: $msg${ex != null ? ' - $ex' : ''}',
      ),
    };
  }

  static Future<WebOnResult<Map<String, dynamic>>> _executeCommand(
    WebOnCommand command,
    WebOnApiContext context,
  ) async {
    try {
      return switch (command) {
        LogCommand() => _handleLog(context),
        GetPlatformInfoCommand() => await _handleGetPlatformInfo(context),
        NavigateToWalletCommand() => _handleNavigateToWallet(context),
        GetMessengerAddressCommand() => await _handleGetMessengerAddress(context),
        SignEvmTransactionCommand() => await _handleSignEvmTransaction(context),
        GetBalanceCommand() => await _handleGetBalance(context),
        UnknownCommand(functionName: final name) => WebOnError('Unknown function: $name'),
      };
    } catch (e) {
      return WebOnError('Command execution failed', e as Exception?);
    }
  }

  // Command handlers with permission checking
  static WebOnResult<Map<String, dynamic>> _handleLog(WebOnApiContext context) {
    // Implementation here
    return const WebOnSuccess({});
  }

  static Future<WebOnResult<Map<String, dynamic>>> _handleGetPlatformInfo(
    WebOnApiContext context,
  ) async {
    // Implementation here
    return const WebOnSuccess({'platform': 'example'});
  }

  static WebOnResult<Map<String, dynamic>> _handleNavigateToWallet(
    WebOnApiContext context,
  ) {
    // Implementation here
    return const WebOnSuccess({});
  }

  static Future<WebOnResult<Map<String, dynamic>>> _handleGetMessengerAddress(
    WebOnApiContext context,
  ) async {
    // Check permissions first
    if (!_hasPermission(context.manifest, NomoPermission.SEND_MESSAGE)) {
      return const WebOnError('Missing SEND_MESSAGE permission');
    }
    // Implementation here
    return const WebOnSuccess({'address': 'example'});
  }

  static Future<WebOnResult<Map<String, dynamic>>> _handleSignEvmTransaction(
    WebOnApiContext context,
  ) async {
    // Check permissions first
    if (!_hasPermission(context.manifest, NomoPermission.SIGN_EVM_TRANSACTION)) {
      return const WebOnError('Missing SIGN_EVM_TRANSACTION permission');
    }
    // Implementation here
    return const WebOnSuccess({'signature': 'example'});
  }

  static Future<WebOnResult<Map<String, dynamic>>> _handleGetBalance(
    WebOnApiContext context,
  ) async {
    // Implementation here
    return const WebOnSuccess({'balance': '0'});
  }

  static bool _hasPermission(NomoManifest manifest, NomoPermission permission) {
    // Implementation here
    return true; // Placeholder
  }
}

// 5. EXTENSION METHODS FOR BETTER API
extension WebOnManifestExtensions on NomoManifest {
  bool hasPermission(NomoPermission permission) {
    final permissionString = "nomo.permission.${permission.name}";
    return permissions.contains(permissionString);
  }

  // Using records for multiple return values
  ({String id, String name, String version}) get identity => (
    id: webon_id,
    name: webon_name,
    version: webon_version,
  );
}

// 6. ENHANCED ERROR HANDLING WITH SEALED CLASSES
sealed class WebOnApiError implements Exception {
  const WebOnApiError();
}

final class PermissionDeniedError extends WebOnApiError {
  final NomoPermission permission;
  const PermissionDeniedError(this.permission);

  @override
  String toString() => 'Permission denied: ${permission.name}';
}

final class InvalidArgumentError extends WebOnApiError {
  final String argument;
  final String reason;
  const InvalidArgumentError(this.argument, this.reason);

  @override
  String toString() => 'Invalid argument $argument: $reason';
}

final class NetworkError extends WebOnApiError {
  final String url;
  final int? statusCode;
  const NetworkError(this.url, [this.statusCode]);

  @override
  String toString() => 'Network error for $url${statusCode != null ? ' (${statusCode})' : ''}';
}

// 7. ASYNC RESULT HANDLING WITH PATTERN MATCHING
class AsyncWebOnHandler {
  static Future<T> handleAsync<T>(
    Future<WebOnResult<T>> Function() operation,
  ) async {
    final result = await operation();
    return switch (result) {
      WebOnSuccess(data: final data) => data,
      WebOnError(message: final msg, exception: final ex) => throw Exception(
        'Operation failed: $msg${ex != null ? ' - $ex' : ''}',
      ),
    };
  }
}
