// Enhanced WebView Controller with InheritedWidget Context Management
import 'package:flutter/material.dart';
import 'package:nomo_app/features/webons/manifest/nomo_manifest.dart';
import 'package:nomo_app/features/webons/api/refactored_api_example.dart';

// Enhanced WebView Controller that manages its own context
abstract class EnhancedWebViewController {
  String get instanceId;
  BuildContext? get context;
  WebViewContextData? get contextData;

  Future<void> initialize({
    required BuildContext context,
    required String initialUrl,
    NomoManifest? manifest,
  });

  Future<void> dispose();

  // WebView-aware API execution
  Future<Map<String, dynamic>> executeWebOnApi({
    required String functionName,
    required NomoManifest manifest,
    required Map<String, dynamic> args,
  });
}

// Mobile implementation with context management
class EnhancedMobileWebViewController extends EnhancedWebViewController {
  final String _instanceId;
  BuildContext? _context;
  WebViewContextData? _contextData;
  bool _isDisposed = false;

  EnhancedMobileWebViewController(this._instanceId);

  @override
  String get instanceId => _instanceId;

  @override
  BuildContext? get context => _context;

  @override
  WebViewContextData? get contextData => _contextData;

  @override
  Future<void> initialize({
    required BuildContext context,
    required String initialUrl,
    NomoManifest? manifest,
  }) async {
    if (_isDisposed) {
      throw StateError('Cannot initialize disposed WebView controller');
    }

    _context = context;

    // Create WebView context data
    _contextData = WebViewContextData(
      instanceId: _instanceId,
      currentUrl: initialUrl,
      isLoading: false,
    );

    print('WebView $_instanceId initialized with URL: $initialUrl');
  }

  @override
  Future<void> dispose() async {
    if (_isDisposed) return;

    _isDisposed = true;

    _context = null;
    _contextData = null;

    print('WebView $_instanceId disposed');
  }

  @override
  Future<Map<String, dynamic>> executeWebOnApi({
    required String functionName,
    required NomoManifest manifest,
    required Map<String, dynamic> args,
  }) async {
    if (_isDisposed) {
      throw StateError('Cannot execute API on disposed WebView');
    }

    if (_context == null) {
      throw StateError('WebView not initialized');
    }

    // Use the stateless API handler
    return await RefactoredWebOnApi.handleCommand(
      functionName: functionName,
      manifest: manifest,
      args: args,
      context: _context!,
    );
  }

  // WebView-specific operations
  Future<void> navigateToUrl(String url) async {
    if (_isDisposed || _context == null) return;

    // Update context data
    if (_contextData != null) {
      _contextData = _contextData!.copyWith(currentUrl: url, isLoading: true);
    }

    print('WebView $_instanceId navigating to: $url');
  }

  Future<void> onPageFinished(String url) async {
    if (_isDisposed) return;

    // Update context data when page finishes loading
    if (_contextData != null) {
      _contextData = _contextData!.copyWith(currentUrl: url, isLoading: false);
    }

    print('WebView $_instanceId finished loading: $url');
  }
}

// Desktop implementation (similar pattern)
class EnhancedDesktopWebViewController extends EnhancedWebViewController {
  final String _instanceId;
  BuildContext? _context;
  WebViewContextData? _contextData;
  bool _isDisposed = false;

  EnhancedDesktopWebViewController(this._instanceId);

  @override
  String get instanceId => _instanceId;

  @override
  BuildContext? get context => _context;

  @override
  WebViewContextData? get contextData => _contextData;

  @override
  Future<void> initialize({
    required BuildContext context,
    required String initialUrl,
    NomoManifest? manifest,
  }) async {
    if (_isDisposed) {
      throw StateError('Cannot initialize disposed WebView controller');
    }

    _context = context;

    // Create WebView context data
    _contextData = WebViewContextData(
      instanceId: _instanceId,
      currentUrl: initialUrl,
      isLoading: false,
    );

    print('Desktop WebView $_instanceId initialized with URL: $initialUrl');
  }

  @override
  Future<void> dispose() async {
    if (_isDisposed) return;

    _isDisposed = true;

    _context = null;
    _contextData = null;

    print('Desktop WebView $_instanceId disposed');
  }

  @override
  Future<Map<String, dynamic>> executeWebOnApi({
    required String functionName,
    required NomoManifest manifest,
    required Map<String, dynamic> args,
  }) async {
    if (_isDisposed) {
      throw StateError('Cannot execute API on disposed WebView');
    }

    if (_context == null) {
      throw StateError('WebView not initialized');
    }

    // Use the stateless API handler
    return await RefactoredWebOnApi.handleCommand(
      functionName: functionName,
      manifest: manifest,
      args: args,
      context: _context!,
    );
  }
}

// Factory for creating WebView controllers
class EnhancedWebViewControllerFactory {
  static int _instanceCounter = 0;

  static EnhancedWebViewController createController({
    String? customInstanceId,
    bool isMobile = true,
  }) {
    final instanceId = customInstanceId ?? 'webview_${++_instanceCounter}';

    if (isMobile) {
      return EnhancedMobileWebViewController(instanceId);
    } else {
      return EnhancedDesktopWebViewController(instanceId);
    }
  }
}

// Widget that uses the enhanced controller
class EnhancedWebViewWidget extends StatefulWidget {
  final String initialUrl;
  final NomoManifest manifest;
  final String? instanceId;

  const EnhancedWebViewWidget({
    Key? key,
    required this.initialUrl,
    required this.manifest,
    this.instanceId,
  }) : super(key: key);

  @override
  State<EnhancedWebViewWidget> createState() => _EnhancedWebViewWidgetState();
}

class _EnhancedWebViewWidgetState extends State<EnhancedWebViewWidget> {
  late final EnhancedWebViewController _controller;

  @override
  void initState() {
    super.initState();

    _controller = EnhancedWebViewControllerFactory.createController(
      customInstanceId: widget.instanceId,
      isMobile: true, // Determine based on platform
    );
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Initialize controller with context
    _controller.initialize(
      context: context,
      initialUrl: widget.initialUrl,
      manifest: widget.manifest,
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // WebView status bar
        Container(
          padding: const EdgeInsets.all(8.0),
          color: Colors.blue.shade100,
          child: Row(
            children: [
              Text('WebView ID: ${_controller.instanceId}'),
              const Spacer(),
              Text('URL: ${_controller.contextData?.currentUrl ?? 'Loading...'}'),
              if (_controller.contextData?.isLoading == true)
                const Padding(
                  padding: EdgeInsets.only(left: 8.0),
                  child: SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                ),
            ],
          ),
        ),

        // Actual WebView would go here
        Expanded(
          child: Container(
            color: Colors.grey.shade200,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text('WebView Instance: ${_controller.instanceId}'),
                  Text('URL: ${widget.initialUrl}'),
                  const SizedBox(height: 20),
                  ElevatedButton(
                    onPressed: _testApiCall,
                    child: const Text('Test API Call'),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _testApiCall() async {
    try {
      final result = await _controller.executeWebOnApi(
        functionName: 'nomoGetPlatformInfo',
        manifest: widget.manifest,
        args: {},
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('API Result: $result'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('API Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
