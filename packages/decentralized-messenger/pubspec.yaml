name: decentralized_messenger
description: Chat with your friends.
publish_to: none
# On version bump also increase the build number for F-Droid
version: 1.17.2+3526

environment:
  sdk: ">=3.5.0 <4.0.0"

dependencies:
  nomo_app:
    path: ../../
  walletkit_dart:
    path: ../../packages/walletkit-dart
  nomo_ui_kit:
    0.0.35
    # path: ../../packages/nomo-ui-kit
  bip39:
    path: ../../packages/walletkit-dart/packages/bip39
  matrix:
    path: ../../packages/matrix-dart-sdk

  camerawesome: ^2.1.0
  swipe_to: ^1.0.6
  adaptive_dialog: ^1.9.0-0
  animations: ^2.0.8
  async: ^2.11.0
  badges: ^3.1.2
  blurhash_dart: ^1.2.1
  callkeep: ^0.3.2 # for calls # not gradle 8+ conform # uses depricated Android API, this will break after flutter 3.27
  chewie: ^1.7.1
  collection: ^1.18.0
  cupertino_icons: any
  desktop_drop: ^0.4.4
  desktop_notifications: ^0.6.3
  device_info_plus: ^11.1.0
  emoji_picker_flutter: ^3.1.0
  emojis: ^0.9.9
  #fcm_shared_isolate: ^0.1.0
  file_picker:
    path: ../../packages/flutter_file_picker
  flutter:
    sdk: flutter
  flutter_blurhash: ^0.8.2
  flutter_foreground_task: 6.4.0 # for calls
  flutter_highlighter: ^0.1.1
  flutter_html: ^3.0.0-beta.2
  flutter_html_table: ^3.0.0-beta.2
  flutter_linkify: ^6.0.0
  flutter_localizations:
    sdk: flutter
  flutter_olm: ^2.0.0
  flutter_openssl_crypto: ^0.3.0
  flutter_secure_storage: ^9.2.2
  flutter_webrtc: ^0.11.2 # uses depricated Android API, this will break after flutter 3.27
  future_loading_dialog: ^0.3.0
  geolocator: ^11.0.0
  go_router: ^13.1.0
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  hooks_riverpod: ^2.4.9
  html: ^0.15.4
  http: ^1.2.1
  image_picker: ^1.0.0
  intl: any
  js: ^0.6.7
  just_audio: ^0.9.42
  keyboard_shortcuts: ^0.1.4
  linkify: ^5.0.0
  image: ^4.3.0 # alternative to native_imaging: ^0.1.1
  package_info_plus: ^8.0.0
  path_provider: ^2.0.9
  permission_handler: ^11.0.1
  # provider: ^6.0.2
  qr_flutter: 4.0.0
  receive_sharing_intent: ^1.8.1
  record: ^5.1.1 # Upgrade to 5 currently breaks playing on iOS
  riverpod: ^2.4.9
  scroll_to_index: ^3.0.1
  share_plus: ^7.2.1
  shared_preferences: ^2.2.0 # Pinned because https://github.com/flutter/flutter/issues/118401
  sqflite: ^2.4.0
  sqflite_common_ffi: ^2.3.4
  sqflite_sqlcipher: ^3.1.0
  tor_detector_web: ^1.1.0
  universal_html: ^2.2.4
  url_launcher: ^6.2.1
  video_compress: ^3.1.3
  video_player: ^2.9.2
  wakelock_plus: ^1.2.2
  webrtc_interface: ^1.0.13
  web3dart: ^2.6.1
  flutter_svg: ^2.0.10+1
  font_awesome_flutter: ^10.7.0
  flutter_map: ^5.0.0
  latlong2: ^0.9.1
  flutter_hooks: ^0.19.0
  flutter_pdfview: ^1.3.4

dev_dependencies:
  flutter_lints: ^3.0.0
  flutter_test:
    sdk: flutter
  import_sorter: ^4.6.0
  msix: ^3.6.2
  translations_cleaner: ^0.0.5
  integration_test:
    sdk: flutter

flutter:
  generate: true
  uses-material-design: true
  assets:
    - assets/
    - assets/js/
    - assets/js/package/

dependency_overrides:
  # Until https://github.com/mogol/flutter_secure_storage/issues/616 is fixed
  flutter_secure_storage_linux: 1.1.3
  # waiting for null safety
  # Upstream pull request: https://github.com/AntoineMarcel/keyboard_shortcuts/pull/13
  keyboard_shortcuts:
    git:
      url: https://github.com/TheOneWithTheBraid/keyboard_shortcuts.git
      ref: null-safety
  # blocked upgrade of package_info_plus for null safety
  # https://github.com/creativecreatorormaybenot/wakelock/pull/203
  wakelock_windows:
    git:
      url: https://github.com/chandrabezzo/wakelock.git
      ref: main
      path: wakelock_windows/
