# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  _fe_analyzer_shared:
    dependency: transitive
    description:
      name: _fe_analyzer_shared
      sha256: f256b0c0ba6c7577c15e2e4e114755640a875e885099367bf6e012b19314c834
      url: "https://pub.dev"
    source: hosted
    version: "72.0.0"
  _flutterfire_internals:
    dependency: transitive
    description:
      name: _flutterfire_internals
      sha256: "5534e701a2c505fed1f0799e652dd6ae23bd4d2c4cf797220e5ced5764a7c1c2"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.44"
  _macros:
    dependency: transitive
    description: dart
    source: sdk
    version: "0.3.2"
  adaptive_dialog:
    dependency: "direct main"
    description:
      name: adaptive_dialog
      sha256: "910debe8766eff4b378ed5164bb470debb87c53a3bdf6adee03c79f64fbf7348"
      url: "https://pub.dev"
    source: hosted
    version: "1.10.1"
  analyzer:
    dependency: transitive
    description:
      name: analyzer
      sha256: b652861553cd3990d8ed361f7979dc6d7053a9ac8843fa73820ab68ce5410139
      url: "https://pub.dev"
    source: hosted
    version: "6.7.0"
  animated_text_kit:
    dependency: transitive
    description:
      name: animated_text_kit
      sha256: "37392a5376c9a1a503b02463c38bc0342ef814ddbb8f9977bc90f2a84b22fa92"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.2"
  animations:
    dependency: "direct main"
    description:
      name: animations
      sha256: d3d6dcfb218225bbe68e87ccf6378bbb2e32a94900722c5f81611dad089911cb
      url: "https://pub.dev"
    source: hosted
    version: "2.0.11"
  ansicolor:
    dependency: transitive
    description:
      name: ansicolor
      sha256: "50e982d500bc863e1d703448afdbf9e5a72eb48840a4f766fa361ffd6877055f"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  app_links:
    dependency: transitive
    description:
      name: app_links
      sha256: ad1a6d598e7e39b46a34f746f9a8b011ee147e4c275d407fa457e7a62f84dd99
      url: "https://pub.dev"
    source: hosted
    version: "6.3.2"
  app_links_linux:
    dependency: transitive
    description:
      name: app_links_linux
      sha256: f5f7173a78609f3dfd4c2ff2c95bd559ab43c80a87dc6a095921d96c05688c81
      url: "https://pub.dev"
    source: hosted
    version: "1.0.3"
  app_links_platform_interface:
    dependency: transitive
    description:
      name: app_links_platform_interface
      sha256: "05f5379577c513b534a29ddea68176a4d4802c46180ee8e2e966257158772a3f"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.2"
  app_links_web:
    dependency: transitive
    description:
      name: app_links_web
      sha256: af060ed76183f9e2b87510a9480e56a5352b6c249778d07bd2c95fc35632a555
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  appkit_ui_element_colors:
    dependency: transitive
    description:
      name: appkit_ui_element_colors
      sha256: c3e50f900aae314d339de489535736238627071457c4a4a2dbbb1545b4f04f22
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  archive:
    dependency: transitive
    description:
      name: archive
      sha256: cb6a278ef2dbb298455e1a713bda08524a175630ec643a242c399c932a0a1f7d
      url: "https://pub.dev"
    source: hosted
    version: "3.6.1"
  args:
    dependency: transitive
    description:
      name: args
      sha256: bf9f5caeea8d8fe6721a9c358dd8a5c1947b27f1cfaa18b39c301273594919e6
      url: "https://pub.dev"
    source: hosted
    version: "2.6.0"
  asn1lib:
    dependency: transitive
    description:
      name: asn1lib
      sha256: "4bae5ae63e6d6dd17c4aac8086f3dec26c0236f6a0f03416c6c19d830c367cf5"
      url: "https://pub.dev"
    source: hosted
    version: "1.5.8"
  async:
    dependency: "direct main"
    description:
      name: async
      sha256: "947bfcf187f74dbc5e146c9eb9c0f10c9f8b30743e341481c1e2ed3ecc18c20c"
      url: "https://pub.dev"
    source: hosted
    version: "2.11.0"
  audio_session:
    dependency: transitive
    description:
      name: audio_session
      sha256: "343e83bc7809fbda2591a49e525d6b63213ade10c76f15813be9aed6657b3261"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.21"
  badges:
    dependency: "direct main"
    description:
      name: badges
      sha256: a7b6bbd60dce418df0db3058b53f9d083c22cdb5132a052145dc267494df0b84
      url: "https://pub.dev"
    source: hosted
    version: "3.1.2"
  base32:
    dependency: transitive
    description:
      name: base32
      sha256: ddad4ebfedf93d4500818ed8e61443b734ffe7cf8a45c668c9b34ef6adde02e2
      url: "https://pub.dev"
    source: hosted
    version: "2.1.3"
  base58check:
    dependency: transitive
    description:
      name: base58check
      sha256: "6c300dfc33e598d2fe26319e13f6243fea81eaf8204cb4c6b69ef20a625319a5"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  bip32:
    dependency: transitive
    description:
      name: bip32
      sha256: "54787cd7a111e9d37394aabbf53d1fc5e2e0e0af2cd01c459147a97c0e3f8a97"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  bip39:
    dependency: "direct main"
    description:
      path: "../walletkit-dart/packages/bip39"
      relative: true
    source: path
    version: "1.0.6"
  blurhash_dart:
    dependency: "direct main"
    description:
      name: blurhash_dart
      sha256: "43955b6c2e30a7d440028d1af0fa185852f3534b795cc6eb81fbf397b464409f"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.1"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      sha256: "6cfb5af12253eaf2b368f07bacc5a80d1301a071c73360d746b7f2e32d762c66"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  bs58check:
    dependency: transitive
    description:
      name: bs58check
      sha256: c4a164d42b25c2f6bc88a8beccb9fc7d01440f3c60ba23663a20a70faf484ea9
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  buffer:
    dependency: transitive
    description:
      name: buffer
      sha256: "389da2ec2c16283c8787e0adaede82b1842102f8c8aae2f49003a766c5c6b3d1"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.3"
  build:
    dependency: transitive
    description:
      name: build
      sha256: "80184af8b6cb3e5c1c4ec6d8544d27711700bc3e6d2efad04238c7b5290889f0"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  cached_network_image:
    dependency: transitive
    description:
      name: cached_network_image
      sha256: "7c1183e361e5c8b0a0f21a28401eecdbde252441106a9816400dd4c2b2424916"
      url: "https://pub.dev"
    source: hosted
    version: "3.4.1"
  cached_network_image_platform_interface:
    dependency: transitive
    description:
      name: cached_network_image_platform_interface
      sha256: "35814b016e37fbdc91f7ae18c8caf49ba5c88501813f73ce8a07027a395e2829"
      url: "https://pub.dev"
    source: hosted
    version: "4.1.1"
  cached_network_image_web:
    dependency: transitive
    description:
      name: cached_network_image_web
      sha256: "980842f4e8e2535b8dbd3d5ca0b1f0ba66bf61d14cc3a17a9b4788a3685ba062"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.1"
  callkeep:
    dependency: "direct main"
    description:
      name: callkeep
      sha256: "9e86e9632a603a61f7045c179ea5ca0ee4da0a49fc5f80c2fe09fb422b96d3c6"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.3"
  camerawesome:
    dependency: "direct main"
    description:
      name: camerawesome
      sha256: "3619d5605fb14ab72c815532c1d9f635512c75df07b5a742b60a9a4b03b6081e"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  canonical_json:
    dependency: transitive
    description:
      name: canonical_json
      sha256: d6be1dd66b420c6ac9f42e3693e09edf4ff6edfee26cb4c28c1c019fdb8c0c15
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  carousel_slider:
    dependency: transitive
    description:
      name: carousel_slider
      sha256: "7b006ec356205054af5beaef62e2221160ea36b90fb70a35e4deacd49d0349ae"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  characters:
    dependency: transitive
    description:
      name: characters
      sha256: "04a925763edad70e8443c99234dc3328f442e811f1d8fd1a72f1c8ad0f69a605"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  charcode:
    dependency: transitive
    description:
      name: charcode
      sha256: fb98c0f6d12c920a02ee2d998da788bca066ca5f148492b7085ee23372b12306
      url: "https://pub.dev"
    source: hosted
    version: "1.3.1"
  chewie:
    dependency: "direct main"
    description:
      name: chewie
      sha256: "335df378c025588aef400c704bd71f0daea479d4cd57c471c88c056c1144e7cd"
      url: "https://pub.dev"
    source: hosted
    version: "1.8.5"
  cli_util:
    dependency: transitive
    description:
      name: cli_util
      sha256: ff6785f7e9e3c38ac98b2fb035701789de90154024a75b6cb926445e83197d1c
      url: "https://pub.dev"
    source: hosted
    version: "0.4.2"
  clock:
    dependency: transitive
    description:
      name: clock
      sha256: cb6d7f03e1de671e34607e909a7213e31d7752be4fb66a86d29fe1eb14bfb5cf
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  collection:
    dependency: "direct main"
    description:
      name: collection
      sha256: ee67cb0715911d28db6bf4af1026078bd6f0128b07a5f66fb2ed94ec6783c09a
      url: "https://pub.dev"
    source: hosted
    version: "1.18.0"
  colorfilter_generator:
    dependency: transitive
    description:
      name: colorfilter_generator
      sha256: ccc2995e440b1d828d55d99150e7cad64624f3cb4a1e235000de3f93cf10d35c
      url: "https://pub.dev"
    source: hosted
    version: "0.0.8"
  console:
    dependency: transitive
    description:
      name: console
      sha256: e04e7824384c5b39389acdd6dc7d33f3efe6b232f6f16d7626f194f6a01ad69a
      url: "https://pub.dev"
    source: hosted
    version: "4.1.0"
  convert:
    dependency: transitive
    description:
      name: convert
      sha256: b30acd5944035672bc15c6b7a8b47d773e41e2f17de064350988c5d02adb1c68
      url: "https://pub.dev"
    source: hosted
    version: "3.1.2"
  cross_file:
    dependency: transitive
    description:
      name: cross_file
      sha256: "7caf6a750a0c04effbb52a676dce9a4a592e10ad35c34d6d2d0e4811160d5670"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.4+2"
  crypto:
    dependency: transitive
    description:
      name: crypto
      sha256: "1e445881f28f22d6140f181e07737b22f1e099a5e1ff94b0af2f9e4a463f4855"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.6"
  csslib:
    dependency: transitive
    description:
      name: csslib
      sha256: "831883fb353c8bdc1d71979e5b342c7d88acfbc643113c14ae51e2442ea0f20f"
      url: "https://pub.dev"
    source: hosted
    version: "0.17.3"
  cupertino_icons:
    dependency: "direct main"
    description:
      name: cupertino_icons
      sha256: ba631d1c7f7bef6b729a622b7b752645a2d076dba9976925b8f25725a30e1ee6
      url: "https://pub.dev"
    source: hosted
    version: "1.0.8"
  dart_bech32:
    dependency: transitive
    description:
      name: dart_bech32
      sha256: "0e1dc1ff39c9669c9ffeafd5d675104918f7b50799692491badfea7e1fb40888"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  dart_style:
    dependency: transitive
    description:
      name: dart_style
      sha256: "7856d364b589d1f08986e140938578ed36ed948581fbc3bc9aef1805039ac5ab"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.7"
  dart_webrtc:
    dependency: transitive
    description:
      name: dart_webrtc
      sha256: c664ad88d5646735753add421ee2118486c100febef5e92b7f59cdbabf6a51f6
      url: "https://pub.dev"
    source: hosted
    version: "1.4.9"
  dbus:
    dependency: transitive
    description:
      name: dbus
      sha256: "365c771ac3b0e58845f39ec6deebc76e3276aa9922b0cc60840712094d9047ac"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.10"
  desktop_drop:
    dependency: "direct main"
    description:
      name: desktop_drop
      sha256: d55a010fe46c8e8fcff4ea4b451a9ff84a162217bdb3b2a0aa1479776205e15d
      url: "https://pub.dev"
    source: hosted
    version: "0.4.4"
  desktop_notifications:
    dependency: "direct main"
    description:
      name: desktop_notifications
      sha256: "6d92694ad6e9297a862c5ff7dd6b8ff64c819972557754769f819d2209612927"
      url: "https://pub.dev"
    source: hosted
    version: "0.6.3"
  device_info_plus:
    dependency: "direct main"
    description:
      name: device_info_plus
      sha256: c4af09051b4f0508f6c1dc0a5c085bf014d5c9a4a0678ce1799c2b4d716387a0
      url: "https://pub.dev"
    source: hosted
    version: "11.1.0"
  device_info_plus_platform_interface:
    dependency: transitive
    description:
      name: device_info_plus_platform_interface
      sha256: "282d3cf731045a2feb66abfe61bbc40870ae50a3ed10a4d3d217556c35c8c2ba"
      url: "https://pub.dev"
    source: hosted
    version: "7.0.1"
  dynamic_color:
    dependency: transitive
    description:
      name: dynamic_color
      sha256: eae98052fa6e2826bdac3dd2e921c6ce2903be15c6b7f8b6d8a5d49b5086298d
      url: "https://pub.dev"
    source: hosted
    version: "1.7.0"
  eip1559:
    dependency: transitive
    description:
      name: eip1559
      sha256: c2b81ac85f3e0e71aaf558201dd9a4600f051ece7ebacd0c5d70065c9b458004
      url: "https://pub.dev"
    source: hosted
    version: "0.6.2"
  eip55:
    dependency: transitive
    description:
      name: eip55
      sha256: "213a9b86add87a5216328e8494b0ab836e401210c4d55eb5e521bd39e39169e1"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  emoji_picker_flutter:
    dependency: "direct main"
    description:
      name: emoji_picker_flutter
      sha256: "08567e6f914d36c32091a96cf2f51d2558c47aa2bd47a590dc4f50e42e0965f6"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  emojis:
    dependency: "direct main"
    description:
      name: emojis
      sha256: "2e4d847c3f1e2670f30dc355909ce6fa7808b4e626c34a4dd503a360995a38bf"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.9"
  enhanced_enum:
    dependency: transitive
    description:
      name: enhanced_enum
      sha256: "074c5a8b9664799ca91e1e8b68003b8694cb19998671cbafd9c7779c13fcdecf"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.4"
  equatable:
    dependency: transitive
    description:
      name: equatable
      sha256: c2b87cb7756efdf69892005af546c56c0b5037f54d2a88269b4f347a505e3ca2
      url: "https://pub.dev"
    source: hosted
    version: "2.0.5"
  eth_sig_util:
    dependency: transitive
    description:
      name: eth_sig_util
      sha256: "20fdc5ce3864e70e5ade1c1cd03cce4ef01018db00adab107303f9055d26b01a"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.9"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      sha256: "511392330127add0b769b75a987850d136345d9227c6b94c96a04cf4a391bf78"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.1"
  ffi:
    dependency: transitive
    description:
      name: ffi
      sha256: "16ed7b077ef01ad6170a3d0c57caa4a112a38d7a2ed5602e0aca9ca6f3d98da6"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.3"
  file:
    dependency: transitive
    description:
      name: file
      sha256: "5fc22d7c25582e38ad9a8515372cd9a93834027aacf1801cf01164dac0ffa08c"
      url: "https://pub.dev"
    source: hosted
    version: "7.0.0"
  file_picker:
    dependency: "direct main"
    description:
      path: "../flutter_file_picker"
      relative: true
    source: path
    version: "6.1.1"
  file_selector_linux:
    dependency: transitive
    description:
      name: file_selector_linux
      sha256: "712ce7fab537ba532c8febdb1a8f167b32441e74acd68c3ccb2e36dcb52c4ab2"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.3"
  file_selector_macos:
    dependency: transitive
    description:
      name: file_selector_macos
      sha256: "271ab9986df0c135d45c3cdb6bd0faa5db6f4976d3e4b437cf7d0f258d941bfc"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.4+2"
  file_selector_platform_interface:
    dependency: transitive
    description:
      name: file_selector_platform_interface
      sha256: a3994c26f10378a039faa11de174d7b78eb8f79e4dd0af2a451410c1a5c3f66b
      url: "https://pub.dev"
    source: hosted
    version: "2.6.2"
  file_selector_windows:
    dependency: transitive
    description:
      name: file_selector_windows
      sha256: "8f5d2f6590d51ecd9179ba39c64f722edc15226cc93dcc8698466ad36a4a85a4"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.3+3"
  firebase_core:
    dependency: transitive
    description:
      name: firebase_core
      sha256: "51dfe2fbf3a984787a2e7b8592f2f05c986bfedd6fdacea3f9e0a7beb334de96"
      url: "https://pub.dev"
    source: hosted
    version: "3.6.0"
  firebase_core_platform_interface:
    dependency: transitive
    description:
      name: firebase_core_platform_interface
      sha256: e30da58198a6d4b49d5bce4e852f985c32cb10db329ebef9473db2b9f09ce810
      url: "https://pub.dev"
    source: hosted
    version: "5.3.0"
  firebase_core_web:
    dependency: transitive
    description:
      name: firebase_core_web
      sha256: f967a7138f5d2ffb1ce15950e2a382924239eaa521150a8f144af34e68b3b3e5
      url: "https://pub.dev"
    source: hosted
    version: "2.18.1"
  firebase_messaging:
    dependency: transitive
    description:
      name: firebase_messaging
      sha256: eb6e28a3a35deda61fe8634967c84215efc19133ba58d8e0fc6c9a2af2cba05e
      url: "https://pub.dev"
    source: hosted
    version: "15.1.3"
  firebase_messaging_platform_interface:
    dependency: transitive
    description:
      name: firebase_messaging_platform_interface
      sha256: b316c4ee10d93d32c033644207afc282d9b2b4372f3cf9c6022f3558b3873d2d
      url: "https://pub.dev"
    source: hosted
    version: "4.5.46"
  firebase_messaging_web:
    dependency: transitive
    description:
      name: firebase_messaging_web
      sha256: d7f0147a1a9fe4313168e20154a01fd5cf332898de1527d3930ff77b8c7f5387
      url: "https://pub.dev"
    source: hosted
    version: "3.9.2"
  fixnum:
    dependency: transitive
    description:
      name: fixnum
      sha256: b6dc7065e46c974bc7c5f143080a6764ec7a4be6da1285ececdc37be96de53be
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  fl_chart:
    dependency: transitive
    description:
      name: fl_chart
      sha256: d0f0d49112f2f4b192481c16d05b6418bd7820e021e265a3c22db98acf7ed7fb
      url: "https://pub.dev"
    source: hosted
    version: "0.68.0"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_blurhash:
    dependency: "direct main"
    description:
      name: flutter_blurhash
      sha256: "5e67678e479ac639069d7af1e133f4a4702311491188ff3e0227486430db0c06"
      url: "https://pub.dev"
    source: hosted
    version: "0.8.2"
  flutter_cache_manager:
    dependency: transitive
    description:
      name: flutter_cache_manager
      sha256: "400b6592f16a4409a7f2bb929a9a7e38c72cceb8ffb99ee57bbf2cb2cecf8386"
      url: "https://pub.dev"
    source: hosted
    version: "3.4.1"
  flutter_driver:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_foreground_task:
    dependency: "direct main"
    description:
      name: flutter_foreground_task
      sha256: "1f9974fa9b322cca33d091857926165f5be46387f489d2335395b3cf6ac91b74"
      url: "https://pub.dev"
    source: hosted
    version: "6.4.0"
  flutter_form_builder:
    dependency: transitive
    description:
      name: flutter_form_builder
      sha256: c278ef69b08957d484f83413f0e77b656a39b7a7bb4eb8a295da3a820ecc6545
      url: "https://pub.dev"
    source: hosted
    version: "9.5.0"
  flutter_highlighter:
    dependency: "direct main"
    description:
      name: flutter_highlighter
      sha256: "93173afd47a9ada53f3176371755e7ea4a1065362763976d06d6adfb4d946e10"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.1"
  flutter_hooks:
    dependency: "direct main"
    description:
      name: flutter_hooks
      sha256: b10cab7e0e4dbb60ff5360a740def3919aecbc672240f742a7130a2874204229
      url: "https://pub.dev"
    source: hosted
    version: "0.19.0"
  flutter_html:
    dependency: "direct main"
    description:
      name: flutter_html
      sha256: "02ad69e813ecfc0728a455e4bf892b9379983e050722b1dce00192ee2e41d1ee"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0-beta.2"
  flutter_html_table:
    dependency: "direct main"
    description:
      name: flutter_html_table
      sha256: e20c72d67ea2512e7b4949f6f7dd13d004e773b0f82c586a21f895e6bd90383c
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0-beta.2"
  flutter_layout_grid:
    dependency: transitive
    description:
      name: flutter_layout_grid
      sha256: "88b4f8484a0874962e27c47733ad256aeb26acc694a9f029edbef771d301885a"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.7"
  flutter_linkify:
    dependency: "direct main"
    description:
      name: flutter_linkify
      sha256: "74669e06a8f358fee4512b4320c0b80e51cffc496607931de68d28f099254073"
      url: "https://pub.dev"
    source: hosted
    version: "6.0.0"
  flutter_lints:
    dependency: "direct dev"
    description:
      name: flutter_lints
      sha256: "9e8c3858111da373efc5aa341de011d9bd23e2c5c5e0c62bccf32438e192d7b1"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.2"
  flutter_local_notifications:
    dependency: transitive
    description:
      name: flutter_local_notifications
      sha256: "674173fd3c9eda9d4c8528da2ce0ea69f161577495a9cc835a2a4ecd7eadeb35"
      url: "https://pub.dev"
    source: hosted
    version: "17.2.4"
  flutter_local_notifications_linux:
    dependency: transitive
    description:
      name: flutter_local_notifications_linux
      sha256: c49bd06165cad9beeb79090b18cd1eb0296f4bf4b23b84426e37dd7c027fc3af
      url: "https://pub.dev"
    source: hosted
    version: "4.0.1"
  flutter_local_notifications_platform_interface:
    dependency: transitive
    description:
      name: flutter_local_notifications_platform_interface
      sha256: "85f8d07fe708c1bdcf45037f2c0109753b26ae077e9d9e899d55971711a4ea66"
      url: "https://pub.dev"
    source: hosted
    version: "7.2.0"
  flutter_localizations:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_map:
    dependency: "direct main"
    description:
      name: flutter_map
      sha256: "5286f72f87deb132daa1489442d6cc46e986fc105cb727d9ae1b602b35b1d1f3"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  flutter_markdown:
    dependency: transitive
    description:
      name: flutter_markdown
      sha256: "04c4722cc36ec5af38acc38ece70d22d3c2123c61305d555750a091517bbe504"
      url: "https://pub.dev"
    source: hosted
    version: "0.6.23"
  flutter_native_splash:
    dependency: transitive
    description:
      name: flutter_native_splash
      sha256: ee5c9bd2b74ea8676442fd4ab876b5d41681df49276488854d6c81a5377c0ef1
      url: "https://pub.dev"
    source: hosted
    version: "2.4.2"
  flutter_olm:
    dependency: "direct main"
    description:
      name: flutter_olm
      sha256: "5e6211af8cba1abf7d1f92e543f6d573dfe6017fe4742e0d04ba84beab47f940"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  flutter_openssl_crypto:
    dependency: "direct main"
    description:
      name: flutter_openssl_crypto
      sha256: "6dcecf6f7c1804ae6f5d73ee05df8af72ea8133bf2447d25979d739503186c96"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.0"
  flutter_pdfview:
    dependency: "direct main"
    description:
      name: flutter_pdfview
      sha256: "715085f9f2c1ad5129dfe0d31a5f0e5481e2e296ce4e6ce72662de28df5456a6"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.4"
  flutter_plugin_android_lifecycle:
    dependency: transitive
    description:
      name: flutter_plugin_android_lifecycle
      sha256: "9b78450b89f059e96c9ebb355fa6b3df1d6b330436e0b885fb49594c41721398"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.23"
  flutter_riverpod:
    dependency: transitive
    description:
      name: flutter_riverpod
      sha256: "9532ee6db4a943a1ed8383072a2e3eeda041db5657cdf6d2acecf3c21ecbe7e1"
      url: "https://pub.dev"
    source: hosted
    version: "2.6.1"
  flutter_secure_storage:
    dependency: "direct main"
    description:
      name: flutter_secure_storage
      sha256: "165164745e6afb5c0e3e3fcc72a012fb9e58496fb26ffb92cf22e16a821e85d0"
      url: "https://pub.dev"
    source: hosted
    version: "9.2.2"
  flutter_secure_storage_linux:
    dependency: "direct overridden"
    description:
      name: flutter_secure_storage_linux
      sha256: "0912ae29a572230ad52d8a4697e5518d7f0f429052fd51df7e5a7952c7efe2a3"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.3"
  flutter_secure_storage_macos:
    dependency: transitive
    description:
      name: flutter_secure_storage_macos
      sha256: "1693ab11121a5f925bbea0be725abfcfbbcf36c1e29e571f84a0c0f436147a81"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.2"
  flutter_secure_storage_platform_interface:
    dependency: transitive
    description:
      name: flutter_secure_storage_platform_interface
      sha256: cf91ad32ce5adef6fba4d736a542baca9daf3beac4db2d04be350b87f69ac4a8
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  flutter_secure_storage_web:
    dependency: transitive
    description:
      name: flutter_secure_storage_web
      sha256: f4ebff989b4f07b2656fb16b47852c0aab9fed9b4ec1c70103368337bc1886a9
      url: "https://pub.dev"
    source: hosted
    version: "1.2.1"
  flutter_secure_storage_windows:
    dependency: transitive
    description:
      name: flutter_secure_storage_windows
      sha256: b20b07cb5ed4ed74fc567b78a72936203f587eba460af1df11281c9326cd3709
      url: "https://pub.dev"
    source: hosted
    version: "3.1.2"
  flutter_svg:
    dependency: "direct main"
    description:
      name: flutter_svg
      sha256: "1b7723a814d84fb65869ea7115cdb3ee7c3be5a27a755c1ec60e049f6b9fcbb2"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.11"
  flutter_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_translate:
    dependency: transitive
    description:
      name: flutter_translate
      sha256: bc09db690098879e3f90eb3aac3499e5282f32d5f9d8f1cc597d67bbc1e065ef
      url: "https://pub.dev"
    source: hosted
    version: "4.1.0"
  flutter_web_plugins:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_webrtc:
    dependency: "direct main"
    description:
      name: flutter_webrtc
      sha256: f6800cc2af79018c12e955ddf8ad007891fdfbb8199b0ce3dccd0977ed2add9c
      url: "https://pub.dev"
    source: hosted
    version: "0.11.7"
  flutter_windowmanager:
    dependency: transitive
    description:
      name: flutter_windowmanager
      sha256: b4d0bc06f6777952b729c0cdb7ce9ad1ecabd8b8b1cb0acb57a36621457dab1b
      url: "https://pub.dev"
    source: hosted
    version: "0.2.0"
  font_awesome_flutter:
    dependency: "direct main"
    description:
      name: font_awesome_flutter
      sha256: d3a89184101baec7f4600d58840a764d2ef760fe1c5a20ef9e6b0e9b24a07a3a
      url: "https://pub.dev"
    source: hosted
    version: "10.8.0"
  freezed_annotation:
    dependency: transitive
    description:
      name: freezed_annotation
      sha256: c2e2d632dd9b8a2b7751117abcfc2b4888ecfe181bd9fca7170d9ef02e595fe2
      url: "https://pub.dev"
    source: hosted
    version: "2.4.4"
  fuchsia_remote_debug_protocol:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  future_loading_dialog:
    dependency: "direct main"
    description:
      name: future_loading_dialog
      sha256: "2718b1a308db452da32ab9bca9ad496ff92b683e217add9e92cf50520f90537e"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.0"
  geolocator:
    dependency: "direct main"
    description:
      name: geolocator
      sha256: "6cb9fb6e5928b58b9a84bdf85012d757fd07aab8215c5205337021c4999bad27"
      url: "https://pub.dev"
    source: hosted
    version: "11.1.0"
  geolocator_android:
    dependency: transitive
    description:
      name: geolocator_android
      sha256: "7aefc530db47d90d0580b552df3242440a10fe60814496a979aa67aa98b1fd47"
      url: "https://pub.dev"
    source: hosted
    version: "4.6.1"
  geolocator_apple:
    dependency: transitive
    description:
      name: geolocator_apple
      sha256: bc2aca02423ad429cb0556121f56e60360a2b7d694c8570301d06ea0c00732fd
      url: "https://pub.dev"
    source: hosted
    version: "2.3.7"
  geolocator_platform_interface:
    dependency: transitive
    description:
      name: geolocator_platform_interface
      sha256: "386ce3d9cce47838355000070b1d0b13efb5bc430f8ecda7e9238c8409ace012"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.4"
  geolocator_web:
    dependency: transitive
    description:
      name: geolocator_web
      sha256: "49d8f846ebeb5e2b6641fe477a7e97e5dd73f03cbfef3fd5c42177b7300fb0ed"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  geolocator_windows:
    dependency: transitive
    description:
      name: geolocator_windows
      sha256: "53da08937d07c24b0d9952eb57a3b474e29aae2abf9dd717f7e1230995f13f0e"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.3"
  get_it:
    dependency: transitive
    description:
      name: get_it
      sha256: d85128a5dae4ea777324730dc65edd9c9f43155c109d5cc0a69cab74139fbac1
      url: "https://pub.dev"
    source: hosted
    version: "7.7.0"
  glob:
    dependency: transitive
    description:
      name: glob
      sha256: "0e7014b3b7d4dac1ca4d6114f82bf1782ee86745b9b42a92c9289c23d8a0ab63"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  go_router:
    dependency: "direct main"
    description:
      name: go_router
      sha256: b465e99ce64ba75e61c8c0ce3d87b66d8ac07f0b35d0a7e0263fcfc10f99e836
      url: "https://pub.dev"
    source: hosted
    version: "13.2.5"
  google_fonts:
    dependency: transitive
    description:
      name: google_fonts
      sha256: f0b8d115a13ecf827013ec9fc883390ccc0e87a96ed5347a3114cac177ef18e8
      url: "https://pub.dev"
    source: hosted
    version: "6.1.0"
  google_identity_services_web:
    dependency: transitive
    description:
      name: google_identity_services_web
      sha256: "5be191523702ba8d7a01ca97c17fca096822ccf246b0a9f11923a6ded06199b6"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.1+4"
  googleapis_auth:
    dependency: transitive
    description:
      name: googleapis_auth
      sha256: befd71383a955535060acde8792e7efc11d2fccd03dd1d3ec434e85b68775938
      url: "https://pub.dev"
    source: hosted
    version: "1.6.0"
  gradient_borders:
    dependency: transitive
    description:
      name: gradient_borders
      sha256: b1cd969552c83f458ff755aa68e13a0327d09f06c3f42f471b423b01427f21f8
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  grpc:
    dependency: transitive
    description:
      name: grpc
      sha256: e93ee3bce45c134bf44e9728119102358c7cd69de7832d9a874e2e74eb8cab40
      url: "https://pub.dev"
    source: hosted
    version: "3.2.4"
  gtk:
    dependency: transitive
    description:
      name: gtk
      sha256: e8ce9ca4b1df106e4d72dad201d345ea1a036cc12c360f1a7d5a758f78ffa42c
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  hex:
    dependency: transitive
    description:
      name: hex
      sha256: "4e7cd54e4b59ba026432a6be2dd9d96e4c5205725194997193bf871703b82c4a"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.0"
  highlighter:
    dependency: transitive
    description:
      name: highlighter
      sha256: "92180c72b9da8758e1acf39a45aa305a97dcfe2fdc8f3d1d2947c23f2772bfbc"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.1"
  hive:
    dependency: "direct main"
    description:
      name: hive
      sha256: "8dcf6db979d7933da8217edcec84e9df1bdb4e4edc7fc77dbd5aa74356d6d941"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.3"
  hive_flutter:
    dependency: "direct main"
    description:
      name: hive_flutter
      sha256: dca1da446b1d808a51689fb5d0c6c9510c0a2ba01e22805d492c73b68e33eecc
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  hooks_riverpod:
    dependency: "direct main"
    description:
      name: hooks_riverpod
      sha256: "70bba33cfc5670c84b796e6929c54b8bc5be7d0fe15bb28c2560500b9ad06966"
      url: "https://pub.dev"
    source: hosted
    version: "2.6.1"
  html:
    dependency: "direct main"
    description:
      name: html
      sha256: "3a7812d5bcd2894edf53dfaf8cd640876cf6cef50a8f238745c8b8120ea74d3a"
      url: "https://pub.dev"
    source: hosted
    version: "0.15.4"
  html_unescape:
    dependency: transitive
    description:
      name: html_unescape
      sha256: "15362d7a18f19d7b742ef8dcb811f5fd2a2df98db9f80ea393c075189e0b61e3"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  http:
    dependency: "direct main"
    description:
      name: http
      sha256: b9c29a161230ee03d3ccf545097fccd9b87a5264228c5d348202e0f0c28f9010
      url: "https://pub.dev"
    source: hosted
    version: "1.2.2"
  http2:
    dependency: transitive
    description:
      name: http2
      sha256: "9ced024a160b77aba8fb8674e38f70875e321d319e6f303ec18e87bd5a4b0c1d"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.0"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      sha256: "2aa08ce0341cc9b354a498388e30986515406668dbcc4f7c950c3e715496693b"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.2"
  image:
    dependency: "direct main"
    description:
      name: image
      sha256: f31d52537dc417fdcde36088fdf11d191026fd5e4fae742491ebd40e5a8bea7d
      url: "https://pub.dev"
    source: hosted
    version: "4.3.0"
  image_picker:
    dependency: "direct main"
    description:
      name: image_picker
      sha256: "021834d9c0c3de46bf0fe40341fa07168407f694d9b2bb18d532dc1261867f7a"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  image_picker_android:
    dependency: transitive
    description:
      name: image_picker_android
      sha256: "8faba09ba361d4b246dc0a17cb4289b3324c2b9f6db7b3d457ee69106a86bd32"
      url: "https://pub.dev"
    source: hosted
    version: "0.8.12+17"
  image_picker_for_web:
    dependency: transitive
    description:
      name: image_picker_for_web
      sha256: "717eb042ab08c40767684327be06a5d8dbb341fe791d514e4b92c7bbe1b7bb83"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.6"
  image_picker_ios:
    dependency: transitive
    description:
      name: image_picker_ios
      sha256: "4f0568120c6fcc0aaa04511cb9f9f4d29fc3d0139884b1d06be88dcec7641d6b"
      url: "https://pub.dev"
    source: hosted
    version: "0.8.12+1"
  image_picker_linux:
    dependency: transitive
    description:
      name: image_picker_linux
      sha256: "4ed1d9bb36f7cd60aa6e6cd479779cc56a4cb4e4de8f49d487b1aaad831300fa"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1+1"
  image_picker_macos:
    dependency: transitive
    description:
      name: image_picker_macos
      sha256: "3f5ad1e8112a9a6111c46d0b57a7be2286a9a07fc6e1976fdf5be2bd31d4ff62"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1+1"
  image_picker_platform_interface:
    dependency: transitive
    description:
      name: image_picker_platform_interface
      sha256: "9ec26d410ff46f483c5519c29c02ef0e02e13a543f882b152d4bfd2f06802f80"
      url: "https://pub.dev"
    source: hosted
    version: "2.10.0"
  image_picker_windows:
    dependency: transitive
    description:
      name: image_picker_windows
      sha256: "6ad07afc4eb1bc25f3a01084d28520496c4a3bb0cb13685435838167c9dcedeb"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1+1"
  import_sorter:
    dependency: "direct dev"
    description:
      name: import_sorter
      sha256: eb15738ccead84e62c31e0208ea4e3104415efcd4972b86906ca64a1187d0836
      url: "https://pub.dev"
    source: hosted
    version: "4.6.0"
  infinity_page_view_astro:
    dependency: transitive
    description:
      name: infinity_page_view_astro
      sha256: c6f49002a9866f4f43e93f81073579bb7104498e22a350a8375f43e7769dfb09
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  integration_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  intersperse:
    dependency: transitive
    description:
      name: intersperse
      sha256: "2f8a905c96f6cbba978644a3d5b31b8d86ddc44917662df7d27a61f3df66a576"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  intl:
    dependency: "direct main"
    description:
      name: intl
      sha256: d6f56758b7d3014a48af9701c085700aac781a92a87a62b1333b46d8879661cf
      url: "https://pub.dev"
    source: hosted
    version: "0.19.0"
  js:
    dependency: "direct main"
    description:
      name: js
      sha256: f2c445dce49627136094980615a031419f7f3eb393237e4ecd97ac15dea343f3
      url: "https://pub.dev"
    source: hosted
    version: "0.6.7"
  json_annotation:
    dependency: transitive
    description:
      name: json_annotation
      sha256: "1ce844379ca14835a50d2f019a3099f419082cfdd231cd86a142af94dd5c6bb1"
      url: "https://pub.dev"
    source: hosted
    version: "4.9.0"
  json_rpc_2:
    dependency: transitive
    description:
      name: json_rpc_2
      sha256: "246b321532f0e8e2ba474b4d757eaa558ae4fdd0688fdbc1e1ca9705f9b8ca0e"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.3"
  just_audio:
    dependency: "direct main"
    description:
      name: just_audio
      sha256: a49e7120b95600bd357f37a2bb04cd1e88252f7cdea8f3368803779b925b1049
      url: "https://pub.dev"
    source: hosted
    version: "0.9.42"
  just_audio_platform_interface:
    dependency: transitive
    description:
      name: just_audio_platform_interface
      sha256: "0243828cce503c8366cc2090cefb2b3c871aa8ed2f520670d76fd47aa1ab2790"
      url: "https://pub.dev"
    source: hosted
    version: "4.3.0"
  just_audio_web:
    dependency: transitive
    description:
      name: just_audio_web
      sha256: "9a98035b8b24b40749507687520ec5ab404e291d2b0937823ff45d92cb18d448"
      url: "https://pub.dev"
    source: hosted
    version: "0.4.13"
  keyboard_shortcuts:
    dependency: "direct main"
    description:
      path: "."
      ref: null-safety
      resolved-ref: a3d4020911860ff091d90638ab708604b71d2c5a
      url: "https://github.com/TheOneWithTheBraid/keyboard_shortcuts.git"
    source: git
    version: "0.1.4"
  latlong2:
    dependency: "direct main"
    description:
      name: latlong2
      sha256: "98227922caf49e6056f91b6c56945ea1c7b166f28ffcd5fb8e72fc0b453cc8fe"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.1"
  leak_tracker:
    dependency: transitive
    description:
      name: leak_tracker
      sha256: "3f87a60e8c63aecc975dda1ceedbc8f24de75f09e4856ea27daf8958f2f0ce05"
      url: "https://pub.dev"
    source: hosted
    version: "10.0.5"
  leak_tracker_flutter_testing:
    dependency: transitive
    description:
      name: leak_tracker_flutter_testing
      sha256: "932549fb305594d82d7183ecd9fa93463e9914e1b67cacc34bc40906594a1806"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.5"
  leak_tracker_testing:
    dependency: transitive
    description:
      name: leak_tracker_testing
      sha256: "6ba465d5d76e67ddf503e1161d1f4a6bc42306f9d66ca1e8f079a47290fb06d3"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  linkify:
    dependency: "direct main"
    description:
      name: linkify
      sha256: "4139ea77f4651ab9c315b577da2dd108d9aa0bd84b5d03d33323f1970c645832"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  lints:
    dependency: transitive
    description:
      name: lints
      sha256: cbf8d4b858bb0134ef3ef87841abdf8d63bfc255c266b7bf6b39daa1085c4290
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  list_counter:
    dependency: transitive
    description:
      name: list_counter
      sha256: c447ae3dfcd1c55f0152867090e67e219d42fe6d4f2807db4bbe8b8d69912237
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  lists:
    dependency: transitive
    description:
      name: lists
      sha256: "4ca5c19ae4350de036a7e996cdd1ee39c93ac0a2b840f4915459b7d0a7d4ab27"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  local_auth:
    dependency: transitive
    description:
      name: local_auth
      sha256: "280421b416b32de31405b0a25c3bd42dfcef2538dfbb20c03019e02a5ed55ed0"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  local_auth_android:
    dependency: transitive
    description:
      name: local_auth_android
      sha256: "6763aaf8965f21822624cb2fd3c03d2a8b3791037b5efb0fe4b13e110f5afc92"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.46"
  local_auth_darwin:
    dependency: transitive
    description:
      name: local_auth_darwin
      sha256: "6d2950da311d26d492a89aeb247c72b4653ddc93601ea36a84924a396806d49c"
      url: "https://pub.dev"
    source: hosted
    version: "1.4.1"
  local_auth_platform_interface:
    dependency: transitive
    description:
      name: local_auth_platform_interface
      sha256: "1b842ff177a7068442eae093b64abe3592f816afd2a533c0ebcdbe40f9d2075a"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.10"
  local_auth_windows:
    dependency: transitive
    description:
      name: local_auth_windows
      sha256: bc4e66a29b0fdf751aafbec923b5bed7ad6ed3614875d8151afe2578520b2ab5
      url: "https://pub.dev"
    source: hosted
    version: "1.0.11"
  local_notifier:
    dependency: transitive
    description:
      name: local_notifier
      sha256: f6cfc933c6fbc961f4e52b5c880f68e41b2d3cd29aad557cc654fd211093a025
      url: "https://pub.dev"
    source: hosted
    version: "0.1.6"
  localstorage:
    dependency: transitive
    description:
      name: localstorage
      sha256: fdff4f717114e992acfd4045dc4a9ab9b987ca57f020965d63e3eb34089c60d8
      url: "https://pub.dev"
    source: hosted
    version: "4.0.1+4"
  logging:
    dependency: transitive
    description:
      name: logging
      sha256: c8245ada5f1717ed44271ed1c26b8ce85ca3228fd2ffdb75468ab01979309d61
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  lottie:
    dependency: transitive
    description:
      name: lottie
      sha256: "893da7a0022ec2fcaa616f34529a081f617e86cc501105b856e5a3184c58c7c2"
      url: "https://pub.dev"
    source: hosted
    version: "1.4.3"
  macos_ui:
    dependency: transitive
    description:
      name: macos_ui
      sha256: "80f6539aba5a3a1182d5225a6c27969a780bcb1d2d8135b4ffb708570cf0c854"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.9"
  macos_window_utils:
    dependency: transitive
    description:
      name: macos_window_utils
      sha256: "3d3982495376077f23556b1e235faab3c6d478fe1238766f824e920708d60eba"
      url: "https://pub.dev"
    source: hosted
    version: "1.6.0"
  macros:
    dependency: transitive
    description:
      name: macros
      sha256: "0acaed5d6b7eab89f63350bccd82119e6c602df0f391260d0e32b5e23db79536"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.2-main.4"
  markdown:
    dependency: transitive
    description:
      name: markdown
      sha256: ef2a1298144e3f985cc736b22e0ccdaf188b5b3970648f2d9dc13efd1d9df051
      url: "https://pub.dev"
    source: hosted
    version: "7.2.2"
  matcher:
    dependency: transitive
    description:
      name: matcher
      sha256: d2323aa2060500f906aa31a895b4030b6da3ebdcc5619d14ce1aada65cd161cb
      url: "https://pub.dev"
    source: hosted
    version: "0.12.16+1"
  material_color_utilities:
    dependency: transitive
    description:
      name: material_color_utilities
      sha256: f7142bb1154231d7ea5f96bc7bde4bda2a0945d2806bb11670e30b850d56bdec
      url: "https://pub.dev"
    source: hosted
    version: "0.11.1"
  matrix:
    dependency: "direct main"
    description:
      path: "../matrix-dart-sdk"
      relative: true
    source: path
    version: "0.33.0"
  matrix2d:
    dependency: transitive
    description:
      name: matrix2d
      sha256: "188718dd3bc2a31e372cfd0791b0f77f4f13ea76164147342cc378d9132949e7"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  meta:
    dependency: transitive
    description:
      name: meta
      sha256: bdb68674043280c3428e9ec998512fb681678676b3c54e773629ffe74419f8c7
      url: "https://pub.dev"
    source: hosted
    version: "1.15.0"
  mgrs_dart:
    dependency: transitive
    description:
      name: mgrs_dart
      sha256: fb89ae62f05fa0bb90f70c31fc870bcbcfd516c843fb554452ab3396f78586f7
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  mime:
    dependency: transitive
    description:
      name: mime
      sha256: "801fd0b26f14a4a58ccb09d5892c3fbdeff209594300a542492cf13fba9d247a"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.6"
  mobile_scanner:
    dependency: transitive
    description:
      name: mobile_scanner
      sha256: d234581c090526676fd8fab4ada92f35c6746e3fb4f05a399665d75a399fb760
      url: "https://pub.dev"
    source: hosted
    version: "5.2.3"
  msix:
    dependency: "direct dev"
    description:
      name: msix
      sha256: c50d6bd1aafe0d071a3c1e5a5ccb056404502935cb0a549e3178c4aae16caf33
      url: "https://pub.dev"
    source: hosted
    version: "3.16.8"
  nested:
    dependency: transitive
    description:
      name: nested
      sha256: "03bac4c528c64c95c722ec99280375a6f2fc708eec17c7b3f07253b626cd2a20"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  nomo_app:
    dependency: "direct main"
    description:
      path: "../.."
      relative: true
    source: path
    version: "0.7.5+1"
  nomo_router:
    dependency: transitive
    description:
      name: nomo_router
      sha256: "29fc8a3f893a43b03e496c8d31636e0a128a6f66608d6fcfa0b301bc3ca5745c"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.3"
  nomo_ui_generator:
    dependency: transitive
    description:
      name: nomo_ui_generator
      sha256: "6813031314b471c53dd9bf0faa18863c4e1d58230e9ace2b25d3b900b6b661e4"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.3"
  nomo_ui_kit:
    dependency: "direct main"
    description:
      name: nomo_ui_kit
      sha256: f9e42d059ca583d294af225d24569ac1b5d33d945ffee2aef9efa70892b6be7a
      url: "https://pub.dev"
    source: hosted
    version: "0.0.35"
  nomo_webview:
    dependency: transitive
    description:
      path: "../nomo-webview"
      relative: true
    source: path
    version: "0.1.0"
  octo_image:
    dependency: transitive
    description:
      name: octo_image
      sha256: "34faa6639a78c7e3cbe79be6f9f96535867e879748ade7d17c9b1ae7536293bd"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  olm:
    dependency: transitive
    description:
      name: olm
      sha256: "3306bf534ceb914fd148b3b4a3d603fb5e067b2e6da8304025b47c24cfdf6b46"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.4"
  open_settings:
    dependency: transitive
    description:
      name: open_settings
      sha256: ceb716dc476352aecb939805b6fa6a593168a5ed1abfe3caa022b6b1715e94ae
      url: "https://pub.dev"
    source: hosted
    version: "2.0.2"
  otp:
    dependency: transitive
    description:
      name: otp
      sha256: fcb7f21e30c4cd80a0a982c27a9b75151cc1fe3d8f7ee680673c090171b1ad55
      url: "https://pub.dev"
    source: hosted
    version: "3.1.4"
  package_config:
    dependency: transitive
    description:
      name: package_config
      sha256: "1c5b77ccc91e4823a5af61ee74e6b972db1ef98c2ff5a18d3161c982a55448bd"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  package_info_plus:
    dependency: "direct main"
    description:
      name: package_info_plus
      sha256: df3eb3e0aed5c1107bb0fdb80a8e82e778114958b1c5ac5644fb1ac9cae8a998
      url: "https://pub.dev"
    source: hosted
    version: "8.1.0"
  package_info_plus_platform_interface:
    dependency: transitive
    description:
      name: package_info_plus_platform_interface
      sha256: ac1f4a4847f1ade8e6a87d1f39f5d7c67490738642e2542f559ec38c37489a66
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  path:
    dependency: transitive
    description:
      name: path
      sha256: "087ce49c3f0dc39180befefc60fdb4acd8f8620e5682fe2476afd0b3688bb4af"
      url: "https://pub.dev"
    source: hosted
    version: "1.9.0"
  path_parsing:
    dependency: transitive
    description:
      name: path_parsing
      sha256: "883402936929eac138ee0a45da5b0f2c80f89913e6dc3bf77eb65b84b409c6ca"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  path_provider:
    dependency: "direct main"
    description:
      name: path_provider
      sha256: "50c5dd5b6e1aaf6fb3a78b33f6aa3afca52bf903a8a5298f53101fdaee55bbcd"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.5"
  path_provider_android:
    dependency: transitive
    description:
      name: path_provider_android
      sha256: c464428172cb986b758c6d1724c603097febb8fb855aa265aeecc9280c294d4a
      url: "https://pub.dev"
    source: hosted
    version: "2.2.12"
  path_provider_foundation:
    dependency: transitive
    description:
      name: path_provider_foundation
      sha256: f234384a3fdd67f989b4d54a5d73ca2a6c422fa55ae694381ae0f4375cd1ea16
      url: "https://pub.dev"
    source: hosted
    version: "2.4.0"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      sha256: f7a1fe3a634fe7734c8d3f2766ad746ae2a2884abe22e241a8b301bf5cac3279
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      sha256: "88f5779f72ba699763fa3a3b06aa4bf6de76c8e5de842cf6f29e2e06476c2334"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      sha256: bd6f00dbd873bfb70d0761682da2b3a2c2fccc2b9e84c495821639601d81afe7
      url: "https://pub.dev"
    source: hosted
    version: "2.3.0"
  permission_handler:
    dependency: "direct main"
    description:
      name: permission_handler
      sha256: "18bf33f7fefbd812f37e72091a15575e72d5318854877e0e4035a24ac1113ecb"
      url: "https://pub.dev"
    source: hosted
    version: "11.3.1"
  permission_handler_android:
    dependency: transitive
    description:
      name: permission_handler_android
      sha256: "71bbecfee799e65aff7c744761a57e817e73b738fedf62ab7afd5593da21f9f1"
      url: "https://pub.dev"
    source: hosted
    version: "12.0.13"
  permission_handler_apple:
    dependency: transitive
    description:
      name: permission_handler_apple
      sha256: e6f6d73b12438ef13e648c4ae56bd106ec60d17e90a59c4545db6781229082a0
      url: "https://pub.dev"
    source: hosted
    version: "9.4.5"
  permission_handler_html:
    dependency: transitive
    description:
      name: permission_handler_html
      sha256: af26edbbb1f2674af65a8f4b56e1a6f526156bc273d0e65dd8075fab51c78851
      url: "https://pub.dev"
    source: hosted
    version: "0.1.3+2"
  permission_handler_platform_interface:
    dependency: transitive
    description:
      name: permission_handler_platform_interface
      sha256: e9c8eadee926c4532d0305dff94b85bf961f16759c3af791486613152af4b4f9
      url: "https://pub.dev"
    source: hosted
    version: "4.2.3"
  permission_handler_windows:
    dependency: transitive
    description:
      name: permission_handler_windows
      sha256: "1a790728016f79a41216d88672dbc5df30e686e811ad4e698bfc51f76ad91f1e"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      sha256: c15605cd28af66339f8eb6fbe0e541bfe2d1b72d5825efc6598f3e0a31b9ad27
      url: "https://pub.dev"
    source: hosted
    version: "6.0.2"
  platform:
    dependency: transitive
    description:
      name: platform
      sha256: "9b71283fc13df574056616011fb138fd3b793ea47cc509c189a6c3fa5f8a1a65"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.5"
  platform_detect:
    dependency: transitive
    description:
      name: platform_detect
      sha256: a62f99417fc4fa2d099ce0ccdbb1bd3977920f2a64292c326271f049d4bc3a4f
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  plugin_platform_interface:
    dependency: transitive
    description:
      name: plugin_platform_interface
      sha256: "4820fbfdb9478b1ebae27888254d445073732dae3d6ea81f0b7e06d5dedc3f02"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.8"
  pointycastle:
    dependency: transitive
    description:
      name: pointycastle
      sha256: "4be0097fcf3fd3e8449e53730c631200ebc7b88016acecab2b0da2f0149222fe"
      url: "https://pub.dev"
    source: hosted
    version: "3.9.1"
  polylabel:
    dependency: transitive
    description:
      name: polylabel
      sha256: "41b9099afb2aa6c1730bdd8a0fab1400d287694ec7615dd8516935fa3144214b"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  process:
    dependency: transitive
    description:
      name: process
      sha256: "21e54fd2faf1b5bdd5102afd25012184a6793927648ea81eea80552ac9405b32"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.2"
  proj4dart:
    dependency: transitive
    description:
      name: proj4dart
      sha256: c8a659ac9b6864aa47c171e78d41bbe6f5e1d7bd790a5814249e6b68bc44324e
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  protobuf:
    dependency: transitive
    description:
      name: protobuf
      sha256: "68645b24e0716782e58948f8467fd42a880f255096a821f9e7d0ec625b00c84d"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  provider:
    dependency: transitive
    description:
      name: provider
      sha256: c8a055ee5ce3fd98d6fc872478b03823ffdb448699c6ebdbbc71d59b596fd48c
      url: "https://pub.dev"
    source: hosted
    version: "6.1.2"
  pub_semver:
    dependency: transitive
    description:
      name: pub_semver
      sha256: "40d3ab1bbd474c4c2328c91e3a7df8c6dd629b79ece4c4bd04bee496a224fb0c"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  qr:
    dependency: transitive
    description:
      name: qr
      sha256: "5c4208b4dc0d55c3184d10d83ee0ded6212dc2b5e2ba17c5a0c0aab279128d21"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  qr_flutter:
    dependency: "direct main"
    description:
      name: qr_flutter
      sha256: c5c121c54cb6dd837b9b9d57eb7bc7ec6df4aee741032060c8833a678c80b87e
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  quiver:
    dependency: transitive
    description:
      name: quiver
      sha256: ea0b925899e64ecdfbf9c7becb60d5b50e706ade44a85b2363be2a22d88117d2
      url: "https://pub.dev"
    source: hosted
    version: "3.2.2"
  random_string:
    dependency: transitive
    description:
      name: random_string
      sha256: "03b52435aae8cbdd1056cf91bfc5bf845e9706724dd35ae2e99fa14a1ef79d02"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.1"
  receive_sharing_intent:
    dependency: "direct main"
    description:
      name: receive_sharing_intent
      sha256: ec76056e4d258ad708e76d85591d933678625318e411564dcb9059048ca3a593
      url: "https://pub.dev"
    source: hosted
    version: "1.8.1"
  record:
    dependency: "direct main"
    description:
      name: record
      sha256: "8cb57763d954624fbc673874930c6f1ceca3baaf9bfee24b25da6fd451362394"
      url: "https://pub.dev"
    source: hosted
    version: "5.2.0"
  record_android:
    dependency: transitive
    description:
      name: record_android
      sha256: "0b4739a2502fff402b0ac0ff1d6b2740854d116d78e06a4a16b3989821f84446"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  record_darwin:
    dependency: transitive
    description:
      name: record_darwin
      sha256: e487eccb19d82a9a39cd0126945cfc47b9986e0df211734e2788c95e3f63c82c
      url: "https://pub.dev"
    source: hosted
    version: "1.2.2"
  record_linux:
    dependency: transitive
    description:
      name: record_linux
      sha256: "74d41a9ebb1eb498a38e9a813dd524e8f0b4fdd627270bda9756f437b110a3e3"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.2"
  record_platform_interface:
    dependency: transitive
    description:
      name: record_platform_interface
      sha256: "8a575828733d4c3cb5983c914696f40db8667eab3538d4c41c50cbb79e722ef4"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  record_web:
    dependency: transitive
    description:
      name: record_web
      sha256: "10cb041349024ce4256e11dd35874df26d8b45b800678f2f51fd1318901adc64"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.4"
  record_windows:
    dependency: transitive
    description:
      name: record_windows
      sha256: "7bce0ac47454212ca8bfa72791d8b6a951f2fb0d4b953b64443c014227f035b4"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  reorderable_grid:
    dependency: transitive
    description:
      name: reorderable_grid
      sha256: "0b9cd95ef0f070ef99f92affe9cf85a4aa127099cd1334e5940950ce58cd981d"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.10"
  riverpod:
    dependency: "direct main"
    description:
      name: riverpod
      sha256: "59062512288d3056b2321804332a13ffdd1bf16df70dcc8e506e411280a72959"
      url: "https://pub.dev"
    source: hosted
    version: "2.6.1"
  route_gen:
    dependency: transitive
    description:
      name: route_gen
      sha256: d2f5a52373aab551dad0435807b90f4e28369b4ceee835ab34373987eb041396
      url: "https://pub.dev"
    source: hosted
    version: "0.0.6"
  rxdart:
    dependency: transitive
    description:
      name: rxdart
      sha256: "5c3004a4a8dbb94bd4bf5412a4def4acdaa12e12f269737a5751369e12d1a962"
      url: "https://pub.dev"
    source: hosted
    version: "0.28.0"
  screen_retriever:
    dependency: transitive
    description:
      name: screen_retriever
      sha256: "6ee02c8a1158e6dae7ca430da79436e3b1c9563c8cf02f524af997c201ac2b90"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.9"
  screenshot:
    dependency: transitive
    description:
      name: screenshot
      sha256: "63817697a7835e6ce82add4228e15d233b74d42975c143ad8cfe07009fab866b"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  screenshot_callback:
    dependency: transitive
    description:
      name: screenshot_callback
      sha256: "80ec324d67f1fb45794045b698cdc7711632b64b2e6a21924286faaade1e0be7"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  scroll_to_index:
    dependency: "direct main"
    description:
      name: scroll_to_index
      sha256: b707546e7500d9f070d63e5acf74fd437ec7eeeb68d3412ef7b0afada0b4f176
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  sdp_transform:
    dependency: transitive
    description:
      name: sdp_transform
      sha256: "73e412a5279a5c2de74001535208e20fff88f225c9a4571af0f7146202755e45"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.2"
  sec:
    dependency: transitive
    description:
      name: sec
      sha256: "8bbd56df884502192a441b5f5d667265498f2f8728a282beccd9db79e215f379"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  sentry:
    dependency: transitive
    description:
      name: sentry
      sha256: "****************************************************************"
      url: "https://pub.dev"
    source: hosted
    version: "8.9.0"
  sentry_flutter:
    dependency: transitive
    description:
      name: sentry_flutter
      sha256: "****************************************************************"
      url: "https://pub.dev"
    source: hosted
    version: "8.9.0"
  share_plus:
    dependency: "direct main"
    description:
      name: share_plus
      sha256: "3ef39599b00059db0990ca2e30fca0a29d8b37aae924d60063f8e0184cf20900"
      url: "https://pub.dev"
    source: hosted
    version: "7.2.2"
  share_plus_platform_interface:
    dependency: transitive
    description:
      name: share_plus_platform_interface
      sha256: "251eb156a8b5fa9ce033747d73535bf53911071f8d3b6f4f0b578505ce0d4496"
      url: "https://pub.dev"
    source: hosted
    version: "3.4.0"
  shared_preferences:
    dependency: "direct main"
    description:
      name: shared_preferences
      sha256: "95f9997ca1fb9799d494d0cb2a780fd7be075818d59f00c43832ed112b158a82"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.3"
  shared_preferences_android:
    dependency: transitive
    description:
      name: shared_preferences_android
      sha256: "3b9febd815c9ca29c9e3520d50ec32f49157711e143b7a4ca039eb87e8ade5ab"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.3"
  shared_preferences_foundation:
    dependency: transitive
    description:
      name: shared_preferences_foundation
      sha256: "07e050c7cd39bad516f8d64c455f04508d09df104be326d8c02551590a0d513d"
      url: "https://pub.dev"
    source: hosted
    version: "2.5.3"
  shared_preferences_linux:
    dependency: transitive
    description:
      name: shared_preferences_linux
      sha256: "580abfd40f415611503cae30adf626e6656dfb2f0cee8f465ece7b6defb40f2f"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  shared_preferences_platform_interface:
    dependency: transitive
    description:
      name: shared_preferences_platform_interface
      sha256: "57cbf196c486bc2cf1f02b85784932c6094376284b3ad5779d1b1c6c6a816b80"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  shared_preferences_web:
    dependency: transitive
    description:
      name: shared_preferences_web
      sha256: d2ca4132d3946fec2184261726b355836a82c33d7d5b67af32692aff18a4684e
      url: "https://pub.dev"
    source: hosted
    version: "2.4.2"
  shared_preferences_windows:
    dependency: transitive
    description:
      name: shared_preferences_windows
      sha256: "94ef0f72b2d71bc3e700e025db3710911bd51a71cefb65cc609dd0d9a982e3c1"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  shelf:
    dependency: transitive
    description:
      name: shelf
      sha256: ad29c505aee705f41a4d8963641f91ac4cee3c8fad5947e033390a7bd8180fa4
      url: "https://pub.dev"
    source: hosted
    version: "1.4.1"
  shelf_static:
    dependency: transitive
    description:
      name: shelf_static
      sha256: c87c3875f91262785dade62d135760c2c69cb217ac759485334c5857ad89f6e3
      url: "https://pub.dev"
    source: hosted
    version: "1.1.3"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.99"
  slugify:
    dependency: transitive
    description:
      name: slugify
      sha256: b272501565cb28050cac2d96b7bf28a2d24c8dae359280361d124f3093d337c3
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  source_gen:
    dependency: transitive
    description:
      name: source_gen
      sha256: "14658ba5f669685cd3d63701d01b31ea748310f7ab854e471962670abcf57832"
      url: "https://pub.dev"
    source: hosted
    version: "1.5.0"
  source_span:
    dependency: transitive
    description:
      name: source_span
      sha256: "53e943d4206a5e30df338fd4c6e7a077e02254531b138a15aec3bd143c1a8b3c"
      url: "https://pub.dev"
    source: hosted
    version: "1.10.0"
  sprintf:
    dependency: transitive
    description:
      name: sprintf
      sha256: "1fc9ffe69d4df602376b52949af107d8f5703b77cda567c4d7d86a0693120f23"
      url: "https://pub.dev"
    source: hosted
    version: "7.0.0"
  sqflite:
    dependency: "direct main"
    description:
      name: sqflite
      sha256: "79a297dc3cc137e758c6a4baf83342b039e5a6d2436fcdf3f96a00adaaf2ad62"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.0"
  sqflite_android:
    dependency: transitive
    description:
      name: sqflite_android
      sha256: "78f489aab276260cdd26676d2169446c7ecd3484bbd5fead4ca14f3ed4dd9ee3"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.0"
  sqflite_common:
    dependency: transitive
    description:
      name: sqflite_common
      sha256: "4468b24876d673418a7b7147e5a08a715b4998a7ae69227acafaab762e0e5490"
      url: "https://pub.dev"
    source: hosted
    version: "2.5.4+5"
  sqflite_common_ffi:
    dependency: "direct main"
    description:
      name: sqflite_common_ffi
      sha256: b8ba78c1b72a9ee6c2323b06af95d43fd13e03d90c8369cb454fd7f629a72588
      url: "https://pub.dev"
    source: hosted
    version: "2.3.4+3"
  sqflite_darwin:
    dependency: transitive
    description:
      name: sqflite_darwin
      sha256: "769733dddf94622d5541c73e4ddc6aa7b252d865285914b6fcd54a63c4b4f027"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1-1"
  sqflite_platform_interface:
    dependency: transitive
    description:
      name: sqflite_platform_interface
      sha256: "8dd4515c7bdcae0a785b0062859336de775e8c65db81ae33dd5445f35be61920"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.0"
  sqflite_sqlcipher:
    dependency: "direct main"
    description:
      name: sqflite_sqlcipher
      sha256: "16033fde6c7d7bd657b71a2bc42332ab02bc8001c3212f502d2e02714e735ec9"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0+1"
  sqlite3:
    dependency: transitive
    description:
      name: sqlite3
      sha256: bb174b3ec2527f9c5f680f73a89af8149dd99782fbb56ea88ad0807c5638f2ed
      url: "https://pub.dev"
    source: hosted
    version: "2.4.7"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      sha256: "73713990125a6d93122541237550ee3352a2d84baad52d375a4cad2eb9b7ce0b"
      url: "https://pub.dev"
    source: hosted
    version: "1.11.1"
  state_notifier:
    dependency: transitive
    description:
      name: state_notifier
      sha256: b8677376aa54f2d7c58280d5a007f9e8774f1968d1fb1c096adcb4792fba29bb
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      sha256: ba2aa5d8cc609d96bbb2899c28934f9e1af5cddbd60a827822ea467161eb54e7
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  stream_transform:
    dependency: transitive
    description:
      name: stream_transform
      sha256: "14a00e794c7c11aa145a170587321aedce29769c08d7f58b1d141da75e3b1c6f"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      sha256: "556692adab6cfa87322a115640c11f13cb77b3f076ddcc5d6ae3c20242bedcde"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  swipe_to:
    dependency: "direct main"
    description:
      name: swipe_to
      sha256: "58f61031803ece9b0efe09006809e78904c640c6d42d48715d1d1c3c28f8499a"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.6"
  sync_http:
    dependency: transitive
    description:
      name: sync_http
      sha256: "7f0cd72eca000d2e026bcd6f990b81d0ca06022ef4e32fb257b30d3d1014a961"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.1"
  syncfusion_flutter_core:
    dependency: transitive
    description:
      name: syncfusion_flutter_core
      sha256: e17dcc7a1d0701e84d0a83c0040503cdcc6c72e44db0d733ab4c706dd5b8b9f8
      url: "https://pub.dev"
    source: hosted
    version: "25.2.7"
  syncfusion_flutter_sliders:
    dependency: transitive
    description:
      name: syncfusion_flutter_sliders
      sha256: "842a452fd73fd61fbebbff72d726ffea4cdaacf088ca2738aeaf7f4454b3861e"
      url: "https://pub.dev"
    source: hosted
    version: "25.2.7"
  synchronized:
    dependency: transitive
    description:
      name: synchronized
      sha256: "69fe30f3a8b04a0be0c15ae6490fc859a78ef4c43ae2dd5e8a623d45bfcf9225"
      url: "https://pub.dev"
    source: hosted
    version: "3.3.0+3"
  tar:
    dependency: transitive
    description:
      name: tar
      sha256: "22f67e2d77b51050436620b2a5de521c58ca6f0b75af1d9ab3c8cae2eae58fcd"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.5"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      sha256: a29248a84fbb7c79282b40b8c72a1209db169a2e0542bce341da992fe1bc7e84
      url: "https://pub.dev"
    source: hosted
    version: "1.2.1"
  test_api:
    dependency: transitive
    description:
      name: test_api
      sha256: "5b8a98dafc4d5c4c9c72d8b31ab2b23fc13422348d2997120294d3bac86b4ddb"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.2"
  timezone:
    dependency: transitive
    description:
      name: timezone
      sha256: "2236ec079a174ce07434e89fcd3fcda430025eb7692244139a9cf54fdcf1fc7d"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.4"
  tint:
    dependency: transitive
    description:
      name: tint
      sha256: "9652d9a589f4536d5e392cf790263d120474f15da3cf1bee7f1fdb31b4de5f46"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  tor_detector_web:
    dependency: "direct main"
    description:
      name: tor_detector_web
      sha256: c4acbd6c0fecd2cd0e8fe00b1a37332422e041021a42488dfddcb3e7ec809b3f
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  translations_cleaner:
    dependency: "direct dev"
    description:
      name: translations_cleaner
      sha256: "060f4a8cd782e271509719741dd3540fe81ddaad49bd79e1d8fc4598299a6b84"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.5"
  tuple:
    dependency: transitive
    description:
      name: tuple
      sha256: a97ce2013f240b2f3807bcbaf218765b6f301c3eff91092bcfa23a039e7dd151
      url: "https://pub.dev"
    source: hosted
    version: "2.0.2"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      sha256: f9049c039ebfeb4cf7a7104a675823cd72dba8297f264b6637062516699fa006
      url: "https://pub.dev"
    source: hosted
    version: "1.4.0"
  unicode:
    dependency: transitive
    description:
      name: unicode
      sha256: "0f69e46593d65245774d4f17125c6084d2c20b4e473a983f6e21b7d7762218f1"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.1"
  universal_html:
    dependency: "direct main"
    description:
      name: universal_html
      sha256: "56536254004e24d9d8cfdb7dbbf09b74cf8df96729f38a2f5c238163e3d58971"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.4"
  universal_io:
    dependency: transitive
    description:
      name: universal_io
      sha256: "1722b2dcc462b4b2f3ee7d188dad008b6eb4c40bbd03a3de451d82c78bba9aad"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.2"
  unorm_dart:
    dependency: transitive
    description:
      name: unorm_dart
      sha256: "5b35bff83fce4d76467641438f9e867dc9bcfdb8c1694854f230579d68cd8f4b"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.0"
  url_launcher:
    dependency: "direct main"
    description:
      name: url_launcher
      sha256: "9d06212b1362abc2f0f0d78e6f09f726608c74e3b9462e8368bb03314aa8d603"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.1"
  url_launcher_android:
    dependency: transitive
    description:
      name: url_launcher_android
      sha256: "6fc2f56536ee873eeb867ad176ae15f304ccccc357848b351f6f0d8d4a40d193"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.14"
  url_launcher_ios:
    dependency: transitive
    description:
      name: url_launcher_ios
      sha256: e43b677296fadce447e987a2f519dcf5f6d1e527dc35d01ffab4fff5b8a7063e
      url: "https://pub.dev"
    source: hosted
    version: "6.3.1"
  url_launcher_linux:
    dependency: transitive
    description:
      name: url_launcher_linux
      sha256: e2b9622b4007f97f504cd64c0128309dfb978ae66adbe944125ed9e1750f06af
      url: "https://pub.dev"
    source: hosted
    version: "3.2.0"
  url_launcher_macos:
    dependency: transitive
    description:
      name: url_launcher_macos
      sha256: "769549c999acdb42b8bcfa7c43d72bf79a382ca7441ab18a808e101149daf672"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.1"
  url_launcher_platform_interface:
    dependency: transitive
    description:
      name: url_launcher_platform_interface
      sha256: "552f8a1e663569be95a8190206a38187b531910283c3e982193e4f2733f01029"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  url_launcher_web:
    dependency: transitive
    description:
      name: url_launcher_web
      sha256: "772638d3b34c779ede05ba3d38af34657a05ac55b06279ea6edd409e323dca8e"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.3"
  url_launcher_windows:
    dependency: transitive
    description:
      name: url_launcher_windows
      sha256: "44cf3aabcedde30f2dba119a9dea3b0f2672fbe6fa96e85536251d678216b3c4"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.3"
  uuid:
    dependency: transitive
    description:
      name: uuid
      sha256: a5be9ef6618a7ac1e964353ef476418026db906c4facdedaa299b7a2e71690ff
      url: "https://pub.dev"
    source: hosted
    version: "4.5.1"
  vector_graphics:
    dependency: transitive
    description:
      name: vector_graphics
      sha256: "0b9149c6ddb013818075b072b9ddc1b89a5122fff1275d4648d297086b46c4f0"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.12"
  vector_graphics_codec:
    dependency: transitive
    description:
      name: vector_graphics_codec
      sha256: "2430b973a4ca3c4dbc9999b62b8c719a160100dcbae5c819bae0cacce32c9cdb"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.12"
  vector_graphics_compiler:
    dependency: transitive
    description:
      name: vector_graphics_compiler
      sha256: f3b9b6e4591c11394d4be4806c63e72d3a41778547b2c1e2a8a04fadcfd7d173
      url: "https://pub.dev"
    source: hosted
    version: "1.1.12"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      sha256: "80b3257d1492ce4d091729e3a67a60407d227c27241d6927be0130c98e741803"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  video_compress:
    dependency: "direct main"
    description:
      name: video_compress
      sha256: "5b42d89f3970c956bad7a86c29682b0892c11a4ddf95ae6e29897ee28788e377"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.3"
  video_player:
    dependency: "direct main"
    description:
      name: video_player
      sha256: "4a8c3492d734f7c39c2588a3206707a05ee80cef52e8c7f3b2078d430c84bc17"
      url: "https://pub.dev"
    source: hosted
    version: "2.9.2"
  video_player_android:
    dependency: transitive
    description:
      name: video_player_android
      sha256: "391e092ba4abe2f93b3e625bd6b6a6ec7d7414279462c1c0ee42b5ab8d0a0898"
      url: "https://pub.dev"
    source: hosted
    version: "2.7.16"
  video_player_avfoundation:
    dependency: transitive
    description:
      name: video_player_avfoundation
      sha256: cd5ab8a8bc0eab65ab0cea40304097edc46da574c8c1ecdee96f28cd8ef3792f
      url: "https://pub.dev"
    source: hosted
    version: "2.6.2"
  video_player_platform_interface:
    dependency: transitive
    description:
      name: video_player_platform_interface
      sha256: "229d7642ccd9f3dc4aba169609dd6b5f3f443bb4cc15b82f7785fcada5af9bbb"
      url: "https://pub.dev"
    source: hosted
    version: "6.2.3"
  video_player_web:
    dependency: transitive
    description:
      name: video_player_web
      sha256: "881b375a934d8ebf868c7fb1423b2bfaa393a0a265fa3f733079a86536064a10"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.3"
  visibility_detector:
    dependency: transitive
    description:
      name: visibility_detector
      sha256: "15c54a459ec2c17b4705450483f3d5a2858e733aee893dcee9d75fd04814940d"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.3"
  vm_service:
    dependency: transitive
    description:
      name: vm_service
      sha256: "5c5f338a667b4c644744b661f309fb8080bb94b18a7e91ef1dbd343bed00ed6d"
      url: "https://pub.dev"
    source: hosted
    version: "14.2.5"
  voice_message_package:
    dependency: transitive
    description:
      name: voice_message_package
      sha256: cd7a717751e60908d37624309c6995c1c62c1bab6b54f7d15c2d71289f0b0cb6
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  wakelock_platform_interface:
    dependency: transitive
    description:
      name: wakelock_platform_interface
      sha256: "1f4aeb81fb592b863da83d2d0f7b8196067451e4df91046c26b54a403f9de621"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.0"
  wakelock_plus:
    dependency: "direct main"
    description:
      name: wakelock_plus
      sha256: bf4ee6f17a2fa373ed3753ad0e602b7603f8c75af006d5b9bdade263928c0484
      url: "https://pub.dev"
    source: hosted
    version: "1.2.8"
  wakelock_plus_platform_interface:
    dependency: transitive
    description:
      name: wakelock_plus_platform_interface
      sha256: "422d1cdbb448079a8a62a5a770b69baa489f8f7ca21aef47800c726d404f9d16"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.1"
  wakelock_windows:
    dependency: "direct overridden"
    description:
      path: wakelock_windows
      ref: main
      resolved-ref: f3610d6c246098fee74463de09434ed81fc2a7c8
      url: "https://github.com/chandrabezzo/wakelock.git"
    source: git
    version: "0.2.2"
  wallet:
    dependency: transitive
    description:
      name: wallet
      sha256: "687fd89a16557649b26189e597792962f405797fc64113e8758eabc2c2605c32"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.13"
  walletkit_dart:
    dependency: "direct main"
    description:
      path: "../walletkit-dart"
      relative: true
    source: path
    version: "1.0.0"
  watcher:
    dependency: transitive
    description:
      name: watcher
      sha256: "3d2ad6751b3c16cf07c7fca317a1413b3f26530319181b37e3b9039b84fc01d8"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  web:
    dependency: transitive
    description:
      name: web
      sha256: cd3543bd5798f6ad290ea73d210f423502e71900302dde696f8bff84bf89a1cb
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  web3dart:
    dependency: "direct main"
    description:
      name: web3dart
      sha256: "885e5e8f0cc3c87c09f160a7fce6279226ca41316806f7ece2001959c62ecced"
      url: "https://pub.dev"
    source: hosted
    version: "2.7.3"
  webdriver:
    dependency: transitive
    description:
      name: webdriver
      sha256: "003d7da9519e1e5f329422b36c4dcdf18d7d2978d1ba099ea4e45ba490ed845e"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.3"
  webrtc_interface:
    dependency: "direct main"
    description:
      name: webrtc_interface
      sha256: abec3ab7956bd5ac539cf34a42fa0c82ea26675847c0966bb85160400eea9388
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  webview_cef:
    dependency: transitive
    description:
      path: "../webview_cef"
      relative: true
    source: path
    version: "0.1.0"
  webview_flutter:
    dependency: transitive
    description:
      name: webview_flutter
      sha256: "889a0a678e7c793c308c68739996227c9661590605e70b1f6cf6b9a6634f7aec"
      url: "https://pub.dev"
    source: hosted
    version: "4.10.0"
  webview_flutter_android:
    dependency: transitive
    description:
      name: webview_flutter_android
      sha256: "74693a212d990b32e0b7055d27db973a18abf31c53942063948cdfaaef9787ba"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  webview_flutter_platform_interface:
    dependency: transitive
    description:
      name: webview_flutter_platform_interface
      sha256: d937581d6e558908d7ae3dc1989c4f87b786891ab47bb9df7de548a151779d8d
      url: "https://pub.dev"
    source: hosted
    version: "2.10.0"
  webview_flutter_wkwebview:
    dependency: transitive
    description:
      name: webview_flutter_wkwebview
      sha256: d4034901d96357beb1b6717ebf7d583c88e40cfc6eb85fe76dd1bf0979a9f251
      url: "https://pub.dev"
    source: hosted
    version: "3.16.0"
  win32:
    dependency: transitive
    description:
      name: win32
      sha256: "84ba388638ed7a8cb3445a320c8273136ab2631cd5f2c57888335504ddab1bc2"
      url: "https://pub.dev"
    source: hosted
    version: "5.8.0"
  win32_registry:
    dependency: transitive
    description:
      name: win32_registry
      sha256: "21ec76dfc731550fd3e2ce7a33a9ea90b828fdf19a5c3bcf556fa992cfa99852"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.5"
  window_manager:
    dependency: transitive
    description:
      name: window_manager
      sha256: "8699323b30da4cdbe2aa2e7c9de567a6abd8a97d9a5c850a3c86dcd0b34bbfbf"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.9"
  wkt_parser:
    dependency: transitive
    description:
      name: wkt_parser
      sha256: "8a555fc60de3116c00aad67891bcab20f81a958e4219cc106e3c037aa3937f13"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      sha256: "7a3f37b05d989967cdddcbb571f1ea834867ae2faa29725fd085180e0883aa15"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  xml:
    dependency: transitive
    description:
      name: xml
      sha256: b015a8ad1c488f66851d762d3090a21c600e479dc75e68328c52774040cf9226
      url: "https://pub.dev"
    source: hosted
    version: "6.5.0"
  yaml:
    dependency: transitive
    description:
      name: yaml
      sha256: "75769501ea3489fca56601ff33454fe45507ea3bfb014161abc3b43ae25989d5"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.2"
sdks:
  dart: ">=3.5.0 <4.0.0"
  flutter: ">=3.24.3"
