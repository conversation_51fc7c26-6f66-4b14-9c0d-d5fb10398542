import 'dart:async';
import 'dart:typed_data';
import 'dart:isolate';
import 'package:image/image.dart' as img;
import 'package:blurhash_dart/blurhash_dart.dart';
import 'package:matrix/matrix.dart';

Future<MatrixImageFileResizedResponse?> customImageResizer(
  MatrixImageFileResizeArguments arguments,
) async {
  final receivePort = ReceivePort();

  final isolate = await Isolate.spawn(
    _imageResizerIsolate,
    receivePort.sendPort,
  );

  final sendPort = await receivePort.first as SendPort;

  final resultPort = ReceivePort();

  sendPort.send([arguments, resultPort.sendPort]);

  final result = await resultPort.first;

  resultPort.close();
  receivePort.close();
  isolate.kill();

  if (result is MatrixImageFileResizedResponse) {
    print("leave");
    return result;
  } else if (result is Exception) {
    throw result;
  } else {
    return null;
  }
}

void _imageResizerIsolate(SendPort sendPort) {
  final receivePort = ReceivePort();

  sendPort.send(receivePort.sendPort);

  receivePort.listen((message) {
    final MatrixImageFileResizeArguments args = message[0];
    final SendPort replyPort = message[1];

    try {
      final img.Image dartImg = img.decodeImage(args.bytes)!;

      final resizedImg = resizeImage(
        dartImg,
        args.maxDimension,
      );

      final jpegBytes = img.encodeJpg(resizedImg, quality: 75);

      final blurhash =
          args.calcBlurhash ? genBlurhash(resizeImage(resizedImg, 200)) : null;

      replyPort.send(
        MatrixImageFileResizedResponse(
          bytes: Uint8List.fromList(jpegBytes),
          width: resizedImg.width,
          height: resizedImg.height,
          blurhash: blurhash?.hash,
        ),
      );
    } catch (e) {
      replyPort.send(e);
    }
  });
}

img.Image resizeImage(
  img.Image image,
  int max,
) {
  img.Image dartImg = image;
  final width = image.width;
  final height = image.height;

  if (width > max || height > max) {
    var w = max, h = max;
    if (width > height) {
      h = max * height ~/ width;
    } else {
      w = max * width ~/ height;
    }

    dartImg = img.copyResize(
      dartImg,
      width: w,
      height: h,
      interpolation: img.Interpolation.linear,
    );
  }
  return dartImg;
}

BlurHash genBlurhash(img.Image img) {
  return BlurHash.encode(img, numCompX: 4, numCompY: 3);
}
