import 'package:flutter/material.dart';
import 'package:matrix/matrix.dart';
import 'package:decentralized_messenger/widgets/mxc_image.dart';
import 'package:decentralized_messenger/widgets/presence_builder.dart';

class Avatar extends StatelessWidget {
  final Uri? mxContent;
  final String? name;
  final double size;
  final void Function()? onTap;
  static const double defaultSize = 44;
  final Client? client;
  final double fontSize;
  final String? presenceUserId;
  final Color? presenceBackgroundColor;
  final bool isDirectChat;
  final bool isNoteToSelfe;

  const Avatar({
    this.mxContent,
    this.name,
    this.size = defaultSize,
    this.onTap,
    this.client,
    this.fontSize = 18,
    this.presenceUserId,
    this.presenceBackgroundColor,
    super.key,
    this.isDirectChat = true,
    this.isNoteToSelfe = false,
  });

  @override
  Widget build(BuildContext context) {
    final noPic = mxContent == null ||
        mxContent.toString().isEmpty ||
        mxContent.toString() == 'null';
    final textWidget = Center(
      child: Icon(
        isDirectChat
            ? Icons.person_outline
            : isNoteToSelfe
                ? Icons.note_alt_outlined
                : Icons.group_outlined,
        size: size * 0.6,
        color: Theme.of(context).colorScheme.onPrimary,
      ),
    );
    final borderRadius = BorderRadius.circular(8);
    final presenceUserId = this.presenceUserId;
    final color = noPic
        ? Theme.of(context).primaryColor
        : Theme.of(context).secondaryHeaderColor;
    final container = Stack(
      children: [
        ClipRRect(
          borderRadius: borderRadius,
          child: Container(
            width: size,
            height: size,
            color: color,
            child: noPic
                ? textWidget
                : MxcImage(
                    key: Key(mxContent.toString()),
                    uri: mxContent,
                    fit: BoxFit.cover,
                    width: size,
                    height: size,
                    placeholder: (_) => textWidget,
                    cacheKey: mxContent.toString(),
                  ),
          ),
        ),
        PresenceBuilder(
          client: client,
          userId: presenceUserId,
          builder: (context, presence) {
            if (presence == null ||
                (presence.presence == PresenceType.offline &&
                    presence.lastActiveTimestamp == null)) {
              return const SizedBox.shrink();
            }
            final dotColor = presence.presence.isOnline
                ? Colors.green
                : presence.presence.isUnavailable
                    ? Colors.orange
                    : Colors.grey;
            return Positioned(
              bottom: 0,
              right: 0,
              child: Container(
                width: 10,
                height: 10,
                decoration: BoxDecoration(
                  color: dotColor,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    width: 1,
                    color: Theme.of(context).colorScheme.surface,
                  ),
                ),
              ),
            );
          },
        ),
      ],
    );
    if (onTap == null) return container;
    return InkWell(
      onTap: onTap,
      borderRadius: borderRadius,
      child: container,
    );
  }
}
