// ignore_for_file: overridden_fields

import 'dart:async';
import 'dart:convert';

// import 'package:decentralized_messenger/nomo_interface.dart';
// import 'package:decentralized_messenger/utils/external_list_notifier.dart';
import 'package:decentralized_messenger/utils/uia_request_manager.dart';
import 'package:decentralized_messenger/widgets/matrix.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import 'package:collection/collection.dart';
import 'package:desktop_notifications/desktop_notifications.dart';
import 'package:flutter_gen/gen_l10n/l10n.dart';
import 'package:future_loading_dialog/future_loading_dialog.dart';
import 'package:go_router/go_router.dart';
import 'package:http/http.dart' as http;
import 'package:image_picker/image_picker.dart';
import 'package:matrix/encryption.dart';
import 'package:matrix/matrix.dart';
import 'package:nomo_app/common/services/logger.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:universal_html/html.dart' as html;

import 'package:decentralized_messenger/utils/client_manager.dart';
import 'package:decentralized_messenger/utils/init_with_restore.dart';
import 'package:decentralized_messenger/utils/localized_exception_extension.dart';
import 'package:decentralized_messenger/utils/platform_infos.dart';
import 'package:decentralized_messenger/utils/voip_plugin.dart';
import 'package:decentralized_messenger/widgets/fluffy_chat_app.dart';
import '../config/app_config.dart';
import '../config/setting_keys.dart';
import '../pages/key_verification/key_verification_dialog.dart';
import '../utils/account_bundles.dart';
// import 'local_notifications_extension.dart';

// import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class NomoMatrix extends MatrixState {
  final List<Client> clients;
  StreamSubscription? _encryptionEventSub;
  int accountTs = -1;

  //StreamSubscription? _syncSub;
  //final Set<String> _initializedRooms = {};

  final Map<String, String>? queryParameters;
  @override
  final SharedPreferences store;
  static NomoMatrix? _instance;

  static NomoMatrix get I {
    if (_instance == null) {
      throw ('NomoMatrix is not initialized');
    }
    return _instance!;
  }

  static NomoMatrix? get IorNull {
    return _instance;
  }

  static void init({
    required List<Client> clients,
    required SharedPreferences store,
    Map<String, String>? queryParameters,
  }) {
    _instance = NomoMatrix._internal(
      clients: clients,
      store: store,
      queryParameters: queryParameters,
    );

    I.initMatrix();
    if (PlatformInfos.isWeb) {
      I.initConfig().then((_) => I.initSettings());
    } else {
      I.initSettings();
    }
    I.initLoadingDialog();

    I.setupEncryption();

    // I.initPushNotifications();
  }

  // void initPushNotifications() {
  //   final client = _instance!.client;

  //   // Create a notifier for the rooms list
  //   final roomsNotifier = ExternalListChangeNotifier<Room>(
  //       previousList: client.rooms, getList: () => client.rooms);

  // Initial subscription for existing rooms
  // for (var room in client.rooms) {
  //   NomoInterface.I.subscribeTopic(removeSpecialCharacters(room.id));
  // }

  // Listen for changes in the rooms list
  // roomsNotifier.stream.listen((updatedRooms) {
  //   final previousRoomIds =
  //       Set.from(roomsNotifier.previousList.map((room) => room.id));
  //   final currentRoomIds = Set.from(updatedRooms.map((room) => room.id));

  // Subscribe to new rooms
  // final newRooms = currentRoomIds.difference(previousRoomIds);
  // final leftRooms = previousRoomIds.difference(currentRoomIds);
  // for (var roomId in newRooms) {
  //   NomoInterface.I.subscribeTopic(removeSpecialCharacters(roomId));
  // }

  // Unsubscribe from removed rooms
  // for (var roomId in leftRooms) {
  //   NomoInterface.I.unsubscribeTopic(removeSpecialCharacters(roomId));
  // }
  //   roomsNotifier.updatePreviousList(updatedRooms);
  // });

  // Listen for sync events to trigger checks for room list changes
  //   client.onSync.stream.listen((event) {
  //     if (event.hasRoomUpdate) {
  //       roomsNotifier.checkForChanges();
  //     }
  //   });
  // }

  String removeSpecialCharacters(String input) {
    final RegExp pattern = RegExp(r'[^a-zA-Z0-9]');
    String cleanedString = input.replaceAll(pattern, '');
    return cleanedString;
  }

  static BuildContext? get navContext => navigatorKey.currentContext;

  static void initContext(BuildContext ctx) {
    //context = ctx;
  }

  NomoMatrix._internal({
    required this.clients,
    required this.store,
    this.queryParameters,
  });

  int _activeClient = -1;
  @override
  String? activeBundle;

  //@override
  //HomeserverSummary? loginHomeserverSummary;
  @override
  XFile? loginAvatar;
  @override
  String? loginUsername;
  @override
  bool? loginRegistrationSupported;
  @override
  VoipPlugin? voipPlugin;

  @override
  Client get client {
    if (clients.isEmpty) {
      clients.add(getLoginClient());
    }
    if (_activeClient < 0 || _activeClient >= clients.length) {
      return currentBundle!.first!;
    }
    return clients[_activeClient];
  }

  // ignore: avoid_init_to_null

  @override
  bool get isMultiAccount => clients.length > 1;

  @override
  int getClientIndexByMatrixId(String matrixId) =>
      clients.indexWhere((client) => client.userID == matrixId);

  @override
  late String currentClientSecret;
  @override
  RequestTokenResponse? currentThreepidCreds;

  @override
  void setActiveClient(Client? cl) {
    final i = clients.indexWhere((c) => c == cl);
    if (i != -1) {
      _activeClient = i;
      createVoipPlugin();
    } else {
      Logs().w('Tried to set an unknown client ${cl!.userID} as active');
    }
  }

  @override
  List<Client?>? get currentBundle {
    if (!hasComplexBundles) {
      return List.from(clients);
    }
    final bundles = accountBundles;
    if (bundles.containsKey(activeBundle)) {
      return bundles[activeBundle];
    }
    return bundles.values.first;
  }

  @override
  Map<String?, List<Client?>> get accountBundles {
    final resBundles = <String?, List<_AccountBundleWithClient>>{};
    for (var i = 0; i < clients.length; i++) {
      final bundles = clients[i].accountBundles;
      for (final bundle in bundles) {
        if (bundle.name == null) {
          continue;
        }
        resBundles[bundle.name] ??= [];
        resBundles[bundle.name]!.add(
          _AccountBundleWithClient(
            client: clients[i],
            bundle: bundle,
          ),
        );
      }
    }
    for (final b in resBundles.values) {
      b.sort(
        (a, b) => a.bundle!.priority == null
            ? 1
            : b.bundle!.priority == null
                ? -1
                : a.bundle!.priority!.compareTo(b.bundle!.priority!),
      );
    }
    return resBundles
        .map((k, v) => MapEntry(k, v.map((vv) => vv.client).toList()));
  }

  @override
  bool get hasComplexBundles => accountBundles.values.any((v) => v.length > 1);

  Client? _loginClientCandidate;

  @override
  Client getLoginClient() {
    if (clients.isNotEmpty && !client.isLogged()) {
      return client;
    }
    final candidate = _loginClientCandidate ??= ClientManager.createClient(
      '${AppConfig.applicationName}-${DateTime.now().millisecondsSinceEpoch}',
    )..onLoginStateChanged
          .stream
          .where((l) => l == LoginState.loggedIn)
          .first
          .then((_) {
        if (!clients.contains(_loginClientCandidate)) {
          clients.add(_loginClientCandidate!);
        }
        ClientManager.addClientNameToStore(
          _loginClientCandidate!.clientName,
          store,
        );
        _registerSubs(_loginClientCandidate!.clientName);
        _loginClientCandidate = null;
        // FluffyChatApp.router.go('/rooms');
      });
    return candidate;
  }

  @override
  Client? getClientByName(String name) =>
      clients.firstWhereOrNull((c) => c.clientName == name);

  @override
  Map<String, dynamic>? get shareContent => _shareContent;

  @override
  set shareContent(Map<String, dynamic>? content) {
    _shareContent = content;
    onShareContentChanged.add(_shareContent);
  }

  Map<String, dynamic>? _shareContent;

  @override
  final StreamController<Map<String, dynamic>?> onShareContentChanged =
      StreamController.broadcast();

  void _initWithStore() async {
    try {
      if (client.isLogged()) {
        // TODO: Figure out how this works in multi account
        final statusMsg = store.getString(SettingKeys.ownStatusMessage);
        if (statusMsg?.isNotEmpty ?? false) {
          Logs().v('Send cached status message: "$statusMsg"');
          await client.setPresence(
            client.userID!,
            PresenceType.online,
            statusMsg: statusMsg,
          );
        }
      }
    } catch (e, s) {
      client.onLoginStateChanged.addError(e, s);
      rethrow;
    }
  }

  @override
  final onRoomKeyRequestSub = <String, StreamSubscription>{};
  @override
  final onKeyVerificationRequestSub = <String, StreamSubscription>{};
  @override
  final onNotification = <String, StreamSubscription>{};
  @override
  final onLoginStateChanged = <String, StreamSubscription<LoginState>>{};
  @override
  final onUiaRequest = <String, StreamSubscription<UiaRequest>>{};
  @override
  StreamSubscription<html.Event>? onFocusSub;
  @override
  StreamSubscription<html.Event>? onBlurSub;

  String? _cachedPassword;
  Timer? _cachedPasswordClearTimer;

  @override
  String? get cachedPassword => _cachedPassword;

  @override
  set cachedPassword(String? p) {
    Logs().d('Password cached');
    _cachedPasswordClearTimer?.cancel();
    _cachedPassword = p;
    _cachedPasswordClearTimer = Timer(const Duration(minutes: 10), () {
      _cachedPassword = null;
      Logs().d('Cached Password cleared');
    });
  }

  @override
  bool webHasFocus = true;

  @override
  String? get activeRoomId {
    final route = router.routeInformationProvider.value.uri.path;
    if (!route.startsWith('/rooms/')) return null;
    return route.split('/')[2];
  }

  @override
  final linuxNotifications =
      PlatformInfos.isLinux ? NotificationsClient() : null;
  @override
  final Map<String, int> linuxNotificationIds = {};

  @override
  void initLoadingDialog() {
    if (navContext == null) {
      return;
    }
    WidgetsBinding.instance.addPostFrameCallback((_) {
      LoadingDialog.defaultTitle = L10n.of(navContext!)!.loadingPleaseWait;
      LoadingDialog.defaultBackLabel = L10n.of(navContext!)!.close;
      LoadingDialog.defaultOnError =
          (e) => (e as Object?)!.toLocalizedString(navContext!);
    });
  }

  @override
  Future<void> initConfig() async {
    try {
      final configJsonString =
          utf8.decode((await http.get(Uri.parse('config.json'))).bodyBytes);
      final configJson = json.decode(configJsonString);
      AppConfig.loadFromJson(configJson);
    } on FormatException catch (_) {
      Logs().v('[ConfigLoader] config.json not found');
    } catch (e) {
      Logs().v('[ConfigLoader] config.json not found', e);
    }
  }

  Future<void> setupEncryption() async {
    Logs().i("Setting up encryption handlers...");

    _encryptionEventSub?.cancel();
/*     _syncSub?.cancel();

    // if (client.encryption?.enabled == true) {
       print("Encryption is enabled");

     _syncSub = client.onSync.stream.listen((sync) async {
      for (final room in client.rooms) {
        if (room.encrypted && !_initializedRooms.contains(room.id)) {
          _initializedRooms.add(room.id);
          print("Initializing encryption for room: ${room.id}");

          try {
            // final participants = await room.requestParticipants();
            // print("Got ${participants.length} participants for room: ${room.id}");

            // final deviceKeys = await room.getUserDeviceKeys();
            // print("Got ${deviceKeys.length} device keys for room: ${room.id}");

            // Try to load any existing sessions
            await client.encryption?.keyManager
                .loadOutboundGroupSession(room.id);
          } catch (e) {
            print("Error initializing room encryption: $e");
          }
        }
      }
    }); */

    _encryptionEventSub = client.onEvent.stream.listen((event) async {
      if (event.type == EventUpdateType.timeline) {
        final isEncrypted = event.content['type'] == EventTypes.Encrypted;
        if (!isEncrypted) return;

        final content = event.content['content'];
        if (content == null) return;

        // skip own events, we can only fetch newer keys via cross signing
        if (event.content["sender"] == client.userID) return;

        if (content['msgtype'] == MessageTypes.BadEncrypted &&
            content['can_request_session'] == true) {
          Logs().w("[Encryption] === Bad Encrypted Message Info ===");
          Logs().w("[Encryption] Room ID: ${event.roomID}");
          Logs().w("[Encryption] Session ID: ${content['session_id']}");
          Logs().w("[Encryption] Sender Key: ${content['sender_key']}");

          await handleBadMessageKey(
              event.roomID, content['session_id'], content['sender_key']);
        }
      }
    });
    // }
  }

  Future<void> handleBadMessageKey(
      String roomId, String sessionId, String senderKey) async {
    try {
      Logs().v("Starting key request process for room: $roomId");

      final room = client.getRoomById(roomId);
      if (room == null) {
        Logs().w("Error: Room not found for ID $roomId");
        return;
      }

      if (room.encrypted && client.encryption?.enabled == true) {
        Logs().v("Room is encrypted, requesting keys...");

        // First, try to find the sender's device by curve25519 key
        final senderDevice = client.getUserDeviceKeysByCurve25519Key(senderKey);
        final senderId = senderDevice?.userId;

        // Ensure we have the latest device keys
        await client.updateUserDeviceKeys(
          additionalUsers: senderId != null ? {senderId} : null,
        );

        // Clear any existing outbound group session and force a new session
        await client.encryption?.keyManager
            .clearOrUseOutboundGroupSession(roomId, use: true);

        // Request the specific session key
        await room.requestSessionKey(sessionId, senderKey);
        Logs().i("Key request sent for session: $sessionId");

        // Get all participants in the room to request their keys
        try {
          final participants = await room.requestParticipants();
          if (participants.isNotEmpty) {
            Logs()
                .v("Requesting keys from ${participants.length} participants");
            await client.updateUserDeviceKeys(
              additionalUsers: participants.map((p) => p.id).toSet(),
            );
          }
        } catch (e) {
          Logs().w("Failed to get room participants: $e");
        }

        // Request all keys for the timeline
        final timeline = await room.getTimeline();
        timeline.requestKeys();

        // await client.sync(); // call does nothing except of giving a response-object

        // Set up a one-time listener for key requests from this sender
        // This will automatically forward keys if we receive a request from the sender's device
        StreamSubscription? keyRequestSub;
        keyRequestSub = client.onRoomKeyRequest.stream.listen((request) async {
          // Check if this is from the sender's device
          if (request.requestingDevice.curve25519Key == senderKey) {
            Logs().i("Received key request from the sender, forwarding key...");
            await request.forwardKey();
          }
        });

        // Cancel the subscription after 30 seconds
        Future.delayed(const Duration(seconds: 30), () {
          keyRequestSub?.cancel();
        });

        // Try to request keys from all devices of the sender if we know who they are
        if (senderId != null) {
          final devices =
              client.userDeviceKeys[senderId]?.deviceKeys.values ?? [];
          if (devices.isNotEmpty) {
            Logs().v(
                "Requesting keys from ${devices.length} devices of sender $senderId");
            for (final device in devices) {
              if (device.curve25519Key != null &&
                  device.curve25519Key != senderKey) {
                try {
                  await room.requestSessionKey(
                      sessionId, device.curve25519Key!);
                } catch (e) {
                  Logs().w(
                      "Failed to request key from device ${device.deviceId}: $e");
                }
              }
            }
          }
        }
      } else {
        Logs().v("Room is not encrypted or encryption not enabled");
      }
    } catch (e, stackTrace) {
      Logger.logError(e, hint: "Error in handleBadMessageKey", s: stackTrace);
    }
  }

  void _registerSubs(String name) {
    final c = getClientByName(name);
    if (c == null) {
      Logs().w(
        'Attempted to register subscriptions for non-existing client $name',
      );
      return;
    }
    onRoomKeyRequestSub[name] ??=
        c.onRoomKeyRequest.stream.listen((RoomKeyRequest request) async {
      if (clients.any(
        ((cl) =>
            cl.userID == request.requestingDevice.userId &&
            cl.identityKey == request.requestingDevice.curve25519Key),
      )) {
        Logs().i(
          '[Key Request] Request is from one of our own clients, forwarding the key...',
        );
        await request.forwardKey();
      } else if (clients.any(
        ((cl) {
          final senderRoom = cl.getRoomById(request.room.id);
          final participiants = senderRoom?.getParticipants();
          return senderRoom != null &&
              participiants != null &&
              participiants.any((usr) {
                return usr.id == request.senderId &&
                    cl.userDeviceKeys[usr.id]
                            ?.deviceKeys[request.requestingDevice.deviceId] !=
                        null;
              });
        }),
      )) {
        Logs().i(
          '[Key Request] Request is from a known user, forwarding the key...',
        );
        await request.forwardKey();
      }
    });
    onKeyVerificationRequestSub[name] ??= c.onKeyVerificationRequest.stream
        .listen((KeyVerification request) async {
      var hidPopup = false;
      request.onUpdate = () {
        if (!hidPopup &&
            {KeyVerificationState.done, KeyVerificationState.error}
                .contains(request.state)) {
          navContext?.pop('dialog');
        }
        hidPopup = true;
      };
      request.onUpdate = null;
      hidPopup = true;
      if (navContext != null) {
        await KeyVerificationDialog(request: request).show(navContext!);
      }
    });
    onLoginStateChanged[name] ??= c.onLoginStateChanged.stream.listen((state) {
      final loggedInWithMultipleClients = clients.length > 1;
      if (state == LoginState.loggedOut) {
        InitWithRestoreExtension.deleteSessionBackup(name);
      }
      if (loggedInWithMultipleClients && state != LoginState.loggedIn) {
        _cancelSubs(c.clientName);
        clients.remove(c);
        ClientManager.removeClientNameFromStore(c.clientName, store);
        if (navContext == null) {
          return;
        }
        ScaffoldMessenger.of(navContext!).showSnackBar(
          SnackBar(
            content: Text(L10n.of(navContext!)!.oneClientLoggedOut),
          ),
        );

        if (state != LoginState.loggedIn) {
          // FluffyChatApp.router.go('/rooms');
        }
      } else {
        // FluffyChatApp.router
        //     .go(state == LoginState.loggedIn ? '/rooms' : '/home');
      }
    });
    onUiaRequest[name] ??= c.onUiaRequest.stream.listen(uiaRequestHandler);
    // if (PlatformInfos.isWeb || PlatformInfos.isLinux) {
    //   c.onSync.stream.first.then((s) {
    //     html.Notification.requestPermission();
    //     onNotification[name] ??= c.onEvent.stream
    //         .where(
    //           (e) =>
    //               e.type == EventUpdateType.timeline &&
    //               [EventTypes.Message, EventTypes.Sticker, EventTypes.Encrypted]
    //                   .contains(e.content['type']) &&
    //               e.content['sender'] != c.userID,
    //         )
    //         .listen(showLocalNotification);
    //   });
    // }
  }

  void _cancelSubs(String name) {
    onRoomKeyRequestSub[name]?.cancel();
    onRoomKeyRequestSub.remove(name);
    onKeyVerificationRequestSub[name]?.cancel();
    onKeyVerificationRequestSub.remove(name);
    onLoginStateChanged[name]?.cancel();
    onLoginStateChanged.remove(name);
    onNotification[name]?.cancel();
    onNotification.remove(name);
  }

  @override
  void initMatrix() {
    _initWithStore();

    for (final c in clients) {
      _registerSubs(c.clientName);
    }

    if (kIsWeb) {
      onFocusSub = html.window.onFocus.listen((_) => webHasFocus = true);
      onBlurSub = html.window.onBlur.listen((_) => webHasFocus = false);
    }

    // if (PlatformInfos.isMobile) {
    //   backgroundPush = BackgroundPush(
    //     this,
    //     onFcmError: (errorMsg, {Uri? link}) async {
    //       final result = await showOkCancelAlertDialog(
    //         barrierDismissible: true,
    //         context: context,
    //         title: L10n.of(context)!.pushNotificationsNotAvailable,
    //         message: errorMsg,
    //         fullyCapitalizedForMaterial: false,
    //         okLabel: link == null
    //             ? L10n.of(context)!.ok
    //             : L10n.of(context)!.learnMore,
    //         cancelLabel: L10n.of(context)!.doNotShowAgain,
    //       );
    //       if (result == OkCancelResult.ok && link != null) {
    //         launchUrlString(
    //           link.toString(),
    //           mode: LaunchMode.externalApplication,
    //         );
    //       }
    //       if (result == OkCancelResult.cancel) {
    //         await store.setBool(SettingKeys.showNoGoogle, true);
    //       }
    //     },
    //   );
    // }

    createVoipPlugin();
  }

  @override
  void createVoipPlugin() async {
    if (store.getBool(SettingKeys.experimentalVoip) == false) {
      voipPlugin = null;
      return;
    }
    voipPlugin = VoipPlugin(this);
  }

  // @override
  // void didChangeAppLifecycleState(AppLifecycleState state) {
  //   Logs().v('AppLifecycleState = $state');
  //   final foreground = state != AppLifecycleState.inactive &&
  //       state != AppLifecycleState.paused;
  //   client.syncPresence =
  //       state == AppLifecycleState.resumed ? null : PresenceType.unavailable;
  //   if (PlatformInfos.isMobile) {
  //     client.backgroundSync = foreground;
  //     client.requestHistoryOnLimitedTimeline = !foreground;
  //     Logs().v('Set background sync to', foreground);
  //   }
  // }

  @override
  void initSettings() {
    AppConfig.fontSizeFactor =
        double.tryParse(store.getString(SettingKeys.fontSizeFactor) ?? '') ??
            AppConfig.fontSizeFactor;

    AppConfig.renderHtml =
        store.getBool(SettingKeys.renderHtml) ?? AppConfig.renderHtml;

    AppConfig.hideRedactedEvents =
        store.getBool(SettingKeys.hideRedactedEvents) ??
            AppConfig.hideRedactedEvents;

    AppConfig.hideUnknownEvents =
        store.getBool(SettingKeys.hideUnknownEvents) ??
            AppConfig.hideUnknownEvents;

    AppConfig.separateChatTypes =
        store.getBool(SettingKeys.separateChatTypes) ??
            AppConfig.separateChatTypes;

    AppConfig.autoplayImages =
        store.getBool(SettingKeys.autoplayImages) ?? AppConfig.autoplayImages;

    AppConfig.sendTypingNotifications =
        store.getBool(SettingKeys.sendTypingNotifications) ??
            AppConfig.sendTypingNotifications;

    AppConfig.sendPublicReadReceipts =
        store.getBool(SettingKeys.sendPublicReadReceipts) ??
            AppConfig.sendPublicReadReceipts;

    AppConfig.sendOnEnter =
        store.getBool(SettingKeys.sendOnEnter) ?? AppConfig.sendOnEnter;

    AppConfig.experimentalVoip = store.getBool(SettingKeys.experimentalVoip) ??
        AppConfig.experimentalVoip;
  }

  @override
  void dispose() {
    super.dispose();
    _encryptionEventSub?.cancel();
    //_syncSub?.cancel();
  }

  // @override
  // void dispose() {
  //   onRoomKeyRequestSub.values.map((s) => s.cancel());
  //   onKeyVerificationRequestSub.values.map((s) => s.cancel());
  //   onLoginStateChanged.values.map((s) => s.cancel());
  //   onNotification.values.map((s) => s.cancel());
  //   client.httpClient.close();
  //   onFocusSub?.cancel();
  //   onBlurSub?.cancel();
  //   backgroundPush?.onRoomSync?.cancel();

  //   linuxNotifications?.close();
  // }
}

class _AccountBundleWithClient {
  final Client? client;
  final AccountBundle? bundle;

  _AccountBundleWithClient({this.client, this.bundle});
}
