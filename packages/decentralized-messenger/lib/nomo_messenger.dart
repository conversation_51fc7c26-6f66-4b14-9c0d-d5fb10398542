import 'package:decentralized_messenger/config/app_config.dart';
import 'package:decentralized_messenger/main.dart';
import 'package:decentralized_messenger/pages/nomo_api/login_api.dart';
import 'package:decentralized_messenger/pages/nomo_provider/login_provider.dart'
as provider;
import 'package:decentralized_messenger/pages/nomo_widget/shared_media.dart';
import 'package:decentralized_messenger/widgets/fluffy_chat_app.dart';
import 'package:decentralized_messenger/widgets/nomo_matrix.dart';
import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:matrix/matrix.dart';
import 'package:receive_sharing_intent/receive_sharing_intent.dart';
//import 'package:hive/hive.dart';

class NomoMessenger {
  /// Get a user-friendly message and color for a login state
  static Map<String, dynamic> getLoginStateMessage(
      provider.LoginState loginState) {
    String message;
    Color backgroundColor;

    switch (loginState) {
      case provider.LoginState.success:
        message = 'Successfully re-logged in';
        backgroundColor = Colors.green;
        break;
      case provider.LoginState.no_displayname:
        message = 'Re-logged in, but you need to set a display name';
        backgroundColor = Colors.orange;
        break;
      case provider.LoginState.client_error:
      case provider.LoginState.server_error:
        message = 'Re-login completed with issues';
        backgroundColor = Colors.orange;
        break;
      default:
        message = 'Re-login completed';
        backgroundColor = Colors.blue;
    }

    return {
      'message': message,
      'backgroundColor': backgroundColor,
    };
  }

  /// Request keys for all encrypted rooms
  static Future<void> requestKeysForAllRooms() async {
    try {
      final client = NomoMatrix.I.client;
      if (client.encryption?.enabled != true) {
        Logs().i("Encryption not enabled, skipping key requests");
        return;
      }

      Logs().i("Requesting keys for all encrypted rooms");
      for (final room in client.rooms) {
        if (room.encrypted) {
          Logs().i("Requesting keys for room: ${room.id}");
          try {
            final timeline = await room.getTimeline();
            timeline.requestKeys();

            // Request participants to update device keys
            final participants = await room.requestParticipants();
            if (participants.isNotEmpty) {
              await client.updateUserDeviceKeys(
                additionalUsers: participants.map((p) => p.id).toSet(),
              );
            }
          } catch (e) {
            Logs().w("Error requesting keys for room ${room.id}: $e");
          }
        }
      }
      Logs().i("Finished requesting keys for all rooms");
    } catch (e) {
      Logs().e("Error in requestKeysForAllRooms: $e");
    }
  }

  static Future<provider.LoginState> reLogin(
      BuildContext context, ProviderContainer container) async {

      final client = NomoMatrix.I.client;

      // Reset the homeserver without clearing cache
      client.homeserver = Uri.parse(chatHomeServer);
      try {
        await client.logout();
      } catch (e) {
        print('Error during logout: $e');
        return provider.LoginState.client_error;
      }
      context.go('/home');
      return provider.LoginState.success;
      // Perform login without clearing cache
      /* final loginState = await container
          .read(provider.chatLoginProvider.notifier)
          .fullProcess(client);

      // If login was successful, request keys for all encrypted rooms
      if (loginState == provider.LoginState.success) {
        await requestKeysForAllRooms();
      }

      switch (loginState) {
        case provider.LoginState.success:
          context.go('/rooms');
          break;
        case provider.LoginState.no_displayname:
          context.goNamed('setDisplayname');
          break;
        case provider.LoginState.client_error:
          context.goNamed('chatUnderConstruction');
          break;
        case provider.LoginState.server_error:
          context.goNamed('chatUnderConstruction');
          break;
        default:
      }

      return loginState;
    } catch (e) {
      print('Error during re-login: $e');
      return provider.LoginState.server_error;
    }*/
  }

  static Future<void> clearCache
      ({
    required
    List<Client> clients}) async {
    const storage = FlutterSecureStorage();
    for (final client in clients) {
      await client.clear();
// client.logout(); //do not logout the user since we need a open box to delete the hive!!
      final storageKey =
          '${AppConfig.applicationName}_session_backup_${client.clientName}';
      await storage.delete(key: storageKey);
    }
//Hive.deleteFromDisk();
  }

  static Future<void> initInviteLink(String? inviteLink) async {
    $ref
        .read(invitationLink.notifier)
        .state = inviteLink;
  }

  static Future<void> initShareMedia(
      List<SharedMediaFile>? shareMediaFiles) async {
    $ref
        .read(sharedFilesProvider.notifier)
        .state = shareMediaFiles;
  }

  static Future<int> getNotificationCount() async {
    try {
      int notifications = 0;
      final rooms = NomoMatrix.I.client.rooms;
      for (final room in rooms) {
        notifications = notifications + room.notificationCount;
      }
      return notifications;
    } catch (e) {
      return 0;
    }
  }

  /// Manually request keys for all encrypted rooms
  ///
  /// This can be called if you're having issues with encrypted messages
  /// not being decrypted properly.
  static Future<void> manuallyRequestKeys(BuildContext context) async {
    try {
      await requestKeysForAllRooms();

// Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Requested keys for all encrypted rooms'),
          backgroundColor: Colors.blue,
        ),
      );
    } catch (e) {
// Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error requesting keys: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
