import 'package:decentralized_messenger/pages/new_group/group_details.dart';
import 'package:decentralized_messenger/pages/new_group/select_user.dart';
import 'package:flutter/material.dart';

import 'package:flutter_gen/gen_l10n/l10n.dart';

import 'package:decentralized_messenger/pages/new_group/new_group.dart';
import 'package:future_loading_dialog/future_loading_dialog.dart';
import 'package:nomo_app/common/widgets/scaffold/zeniq_scaffold.dart';
import 'package:nomo_ui_kit/components/buttons/primary/nomo_primary_button.dart';
import 'package:nomo_ui_kit/components/text/nomo_text.dart';
import 'package:nomo_ui_kit/theme/nomo_theme.dart';

class NewGroupView extends StatefulWidget {
  final NewGroupController controller;

  const NewGroupView(this.controller, {super.key});

  @override
  State<NewGroupView> createState() => _NewGroupViewState();
}

class _NewGroupViewState extends State<NewGroupView> {
  final formState = GlobalKey<FormState>();
  void _navigateBack(BuildContext context) {
    if (widget.controller.pageController.page == 1) {
      widget.controller.pageController.animateToPage(0,
          duration: const Duration(milliseconds: 300), curve: Curves.easeInOut);
    }
    if (widget.controller.pageController.page == 0) {
      Navigator.of(context).pop();
    }
  }

  void _navigateForward(BuildContext context) async {
    if (widget.controller.pageController.page == 1) {
      if (formState.currentState!.validate()) {
        await showFutureLoadingDialog(
            context: context, future: () => widget.controller.submitAction());
      }
    }
    if (widget.controller.pageController.page == 0) {
      widget.controller.pageController.animateToPage(1,
          duration: const Duration(milliseconds: 300), curve: Curves.easeInOut);
    }
  }

  int currentPage = 0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: Center(
          child: BackButton(
            onPressed: () => _navigateBack(context),
          ),
        ),
        title: Text(L10n.of(context)!.createGroup),
      ),
      body: ZeniqBackgroundWidget(
        child: PageView(
          physics: const NeverScrollableScrollPhysics(),
          controller: widget.controller.pageController,
          onPageChanged: (value) => setState(() {
            currentPage = value;
          }),
          children: [
            SelectUser(
              controller: widget.controller,
            ),
            GroupDetails(
              controller: widget.controller,
              formState: formState,
            ),
          ],
        ),
      ),
      bottomNavigationBar: Builder(
        builder: (context) {
          final text = currentPage == 1
              ? 'finish'
              : widget.controller.selectedProfiles.isNotEmpty
                  ? 'btn_next'
                  : 'skip';
          return Padding(
            padding: const EdgeInsets.only(left: 20, right: 20, bottom: 20),
            child: PrimaryNomoButton(
                height: 50,
                onPressed: () => _navigateForward(context),
                child: NomoText(text,
                    style: context.theme.typography.b3.copyWith(
                      color: context.colors.onPrimary,
                      fontWeight: FontWeight.bold,
                    ))),
          );
        },
      ),
    );
  }
}
