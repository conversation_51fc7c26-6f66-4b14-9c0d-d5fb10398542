import 'package:decentralized_messenger/pages/nomo_api/assets_api.dart';
import 'package:decentralized_messenger/pages/nomo_api/wallet_info.dart';
import 'package:decentralized_messenger/widgets/nomo_matrix.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:walletkit_dart/walletkit_dart.dart';
// import 'package:webon_kit_dart/webon_kit_dart.dart';

final amountInputProvider = StateProvider.autoDispose<BigInt?>((ref) {
  return null;
});

final receiveTokenProvider = StateProvider<Token?>((ref) => null);

final receiveAddressProvider =
    FutureProvider.family<String?, String>((ref, userID) async {
  return await getAddressOfReceiver(userID: userID);
});

Future<String?> getAddressOfReceiver(
    {required String userID, CoinEntity? selectedToken}) async {
  final accessToken = NomoMatrix.I.client.accessToken!;
  final walletInfo = await fetchWalletInfo(userID, accessToken);
  if (walletInfo == null) {
    return null;
  }
  if (selectedToken == null) {
    return null;
  }
  final address =
      getAddressForSymbol(addresses: walletInfo, symbol: selectedToken.symbol);
  return address;
}

String? getAddressForSymbol(
    {required ChatWalletInfo addresses, required String symbol}) {
  switch (symbol) {
    case 'BTC':
      return addresses.btcAddress;
    case 'BCH':
      return addresses.bitcoinCashAddress;
    case 'LTC':
      return addresses.litecoinAddress;
    case 'ZENIQ Coin':
      return addresses.zeniqAddress;
    case 'EURO':
      return addresses.btcAddress;
    default:
      return addresses.evmAddress;
  }
}

class Token {
  final String? name;
  final String symbol;
  final int decimals;
  final String? contractAddress;
  final String? balance;
  final String? network;
  final String? receiveAddress;

  const Token({
    required this.name,
    required this.symbol,
    required this.decimals,
    required this.contractAddress,
    required this.balance,
    required this.network,
    required this.receiveAddress,
  });

  Token copyWith({
    String? name,
    String? symbol,
    int? decimals,
    String? contractAddress,
    String? balance,
    String? network,
    String? receiveAddress,
    String? assetIcon,
    double? selectedValue,
  }) {
    return Token(
      name: name ?? this.name,
      symbol: symbol ?? this.symbol,
      decimals: decimals ?? this.decimals,
      contractAddress: contractAddress ?? this.contractAddress,
      balance: balance ?? this.balance,
      network: network ?? this.network,
      receiveAddress: receiveAddress ?? this.receiveAddress,
    );
  }

  static String getAssetName(Token token) {
    final String symbol;

    if (token.symbol == "ZENIQ Coin" ||
        token.symbol == "ZENIQ Token" ||
        token.symbol == "ZENIQ @ETH" ||
        token.symbol == "ZENIQ @BSC") {
      symbol = "ZENIQ Coin";
    } else if (token.symbol == "USDC") {
      symbol = "usd-coin";
    } else if (token.symbol == "AVINOC ERC20" ||
        token.symbol == "AVINOC ZEN20") {
      symbol = 'avinoc';
    } else if (token.symbol == "WBTC") {
      symbol = "BTC";
    } else {
      symbol = token.symbol;
    }

    return symbol.toLowerCase();
  }

  @override
  String toString() {
    return 'Token(name: $name, symbol: $symbol, decimals: $decimals, contractAddress: $contractAddress, balance: $balance, network: $network, receiveAddress: $receiveAddress)';
  }
}
