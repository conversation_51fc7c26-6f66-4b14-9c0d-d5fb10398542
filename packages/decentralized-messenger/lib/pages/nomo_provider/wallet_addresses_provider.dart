import 'package:decentralized_messenger/pages/nomo_api/assets_api.dart';
import 'package:decentralized_messenger/pages/nomo_api/wallet_info.dart';
import 'package:decentralized_messenger/widgets/nomo_matrix.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

final walletAddressesProvider =
    StateNotifierProvider<WalletAddressesNotifer, ChatWalletInfo?>((ref) {
  return WalletAddressesNotifer();
});

class WalletAddressesNotifer extends StateNotifier<ChatWalletInfo?> {
  WalletAddressesNotifer() : super(null);

  void set(ChatWalletInfo? wallets) async {
    if (wallets != null && state != null && wallets.compareTo(state!)) {
      return;
    }
    state = wallets;
    await _checkWalletAddressKeysUplaod();
  }

  Future<void> _checkWalletAddressKeysUplaod() async {
    final String? accessToken = NomoMatrix.I.client.accessToken;
    if (accessToken == null) {
      return;
    }
    final String? userId = NomoMatrix.I.client.userID;
    if (userId == null) {
      return;
    }
    final walletInfo = await fetchWalletInfo(userId, accessToken);
    if (state != null &&
        (walletInfo == null || !state!.compareTo(walletInfo))) {
      uploadWalletInfo(userId, accessToken, state!.toJson());
    }
  }
}
