import 'dart:math';

double? tryParseInputDecimal(String str) {
  String normalized = str.replaceFirst(",", ".");
  normalized = normalized.trim();
  if (normalized.startsWith(".")) {
    normalized = "0$normalized";
  }
  final amount = double.tryParse(normalized);
  return amount;
}

BigInt? convertInputDoubleToBI(double? amount, int decimals) {
  if (amount == null) {
    return null;
  }
  final BigInt amountBI = BigInt.from(amount * pow(10, decimals));
  return amountBI;
}

double? convertAmountBItoDouble(BigInt? amount, int decimals) {
  if (amount == null) {
    return null;
  }
  final double amountDouble = amount.toDouble() / pow(10, decimals);
  return amountDouble;
}