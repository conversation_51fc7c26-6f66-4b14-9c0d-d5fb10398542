import 'dart:typed_data';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:share_plus/share_plus.dart';

Future<void> shareQrCode(String qrCode, String displayName) async {
  final invitationText =
      '$displayName invites you to join a chat conversation! Click on the link below $qrCode';
  final invitationTextApple =
      '$displayName invites you to join a chat conversation! Click on the link below to join.';

    await Share.share(invitationText, subject: invitationTextApple,);

}

Future<String> generateQrCodeImage(String qrCode) async {
  final qrImageData = await QrPainter(
    data: qrCode,
    gapless: true,
    version: QrVersions.auto,
    emptyColor: Colors.white,
  ).toImageData(878);

  String qrCodeFilePath;
  if (qrImageData != null) {
    qrCodeFilePath = await saveImage(qrImageData, "Nomo Qr-Code");
  } else {
    throw Exception("Failed to generate QR code image");
  }

  return qrCodeFilePath;
}

Future<String> saveImage(ByteData data, String name) async {
  final directory = await getApplicationDocumentsDirectory();
  final imagePath = '${directory.path}/$name.png';

  final buffer = data.buffer;
  final bytes = buffer.asUint8List(data.offsetInBytes, data.lengthInBytes);

  final file = File(imagePath);
  await file.writeAsBytes(bytes);

  return imagePath;
}
