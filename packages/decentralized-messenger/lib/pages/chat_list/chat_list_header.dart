import 'package:decentralized_messenger/pages/chat_list/search_filter_chip.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/l10n.dart';
import 'package:decentralized_messenger/pages/chat_list/chat_list.dart';
import 'package:decentralized_messenger/pages/chat_list/client_chooser_button.dart';
import 'package:nomo_ui_kit/components/input/textInput/nomo_input.dart';
import 'package:nomo_ui_kit/theme/nomo_theme.dart';

class ChatListHeader extends StatelessWidget implements PreferredSizeWidget {
  final ChatListController controller;

  const ChatListHeader({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    final selectMode = controller.selectMode;
    final isSearchMode = controller.isTextFieldActiv;

    return SliverAppBar(
      floating: true,
      toolbarHeight: 72,
      pinned: true,
      scrolledUnderElevation: selectMode == SelectMode.normal ? 0 : null,
      backgroundColor: selectMode == SelectMode.normal
          ? Theme.of(context).colorScheme.surface
          : null,
      automaticallyImplyLeading: false,
      leading: selectMode == SelectMode.normal
          ? null
          : IconButton(
              tooltip: L10n.of(context)!.cancel,
              icon: const Icon(
                Icons.arrow_back,
              ),
              onPressed: controller.cancelAction,
            ),
      bottom: isSearchMode
          ? PreferredSize(
              preferredSize: Size.fromHeight(50),
              child: SizedBox(
                height: 50,
                width: double.infinity,
                child: Padding(
                  padding: EdgeInsets.symmetric(vertical: 2.5),
                  child: ListView(
                      physics: const BouncingScrollPhysics(
                          parent: AlwaysScrollableScrollPhysics()),
                      scrollDirection: Axis.horizontal,
                      children: [
                        const SizedBox(width: 20),
                        SearchFilterWidget(
                            resultFilter: ResultFilter.chats,
                            label: L10n.of(context)!.chats,
                            controller: controller),
                        const SizedBox(width: 10),
                        SearchFilterWidget(
                            resultFilter: ResultFilter.public,
                            label: L10n.of(context)!.publicRooms,
                            controller: controller),
                        const SizedBox(width: 10),
                        SearchFilterWidget(
                            resultFilter: ResultFilter.users,
                            label: L10n.of(context)!.users,
                            controller: controller),
                        const SizedBox(width: 20),
                        // SearchFilterWidget(
                        //     resultFilter: ResultFilter.chats,
                        //     label: L10n.of(context)!.chats,
                        //     controller: controller),
                      ]),
                ),
              ),
            )
          : null,
      title: AnimatedSwitcher(
        duration: const Duration(milliseconds: 300),
        transitionBuilder: (child, animation) {
          return ScaleTransition(
            scale: animation,
            child: SizeTransition(
              sizeFactor: animation,
              child: child,
            ),
          );
        },
        child: selectMode == SelectMode.share
            ? Text(
                L10n.of(context)!.share,
                key: const ValueKey(SelectMode.share),
              )
            : selectMode == SelectMode.select
                ? Text(
                    controller.selectedRoomIds.length.toString(),
                    key: const ValueKey(SelectMode.select),
                  )
                : isSearchMode
                    ? NomoInput(
                        focusNode: controller.searchFocusNode,
                        textInputAction: TextInputAction.search,
                        onChanged: controller.onSearchEnter,
                        border: Border.fromBorderSide(
                          BorderSide.none,
                        ),
                        style: NomoTheme.of(context).typography.b3,
                        height: 56,
                        borderRadius: BorderRadius.circular(99),
                        background: NomoTheme.of(context).colors.background2,
                        enabled: true,
                        padding: EdgeInsets.all(0.0),
                        placeHolder: L10n.of(context)!.searchChatsRooms,
                        placeHolderStyle:
                            NomoTheme.of(context).typography.b3.copyWith(
                                  color: NomoTheme.of(context)
                                      .colors
                                      .foreground1
                                      .withOpacity(0.5),
                                  fontWeight: FontWeight.normal,
                                ),
                        leading: IconButton(
                          onPressed: () {
                            controller.cancelSearch();
                            controller.toggleSearchMode();
                          },
                          icon: Icon(
                            Icons.arrow_back,
                            color: NomoTheme.of(context).colors.foreground1,
                          ),
                        ),
                        trailling: controller.isSearching
                            ? SizedBox(
                                height: 30,
                                width: 30,
                                child: const CircularProgressIndicator())
                            : null,
                      )
                    : Row(
                        children: [
                          const Text(
                            "NOMO Chat",
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                          const Spacer(),
                          IconButton(
                            tooltip: L10n.of(context)!.search,
                            icon: const Icon(Icons.search_outlined),
                            iconSize: 32,
                            onPressed: () {
                              controller.toggleSearchMode();
                              controller.searchFocusNode.requestFocus();
                            },
                          ),
                          const SizedBox(width: 18),
                          ClientChooserButton(controller),
                        ],
                      ),
      ),
      actions: selectMode == SelectMode.share
          ? [
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16.0,
                  vertical: 8.0,
                ),
                child: ClientChooserButton(controller),
              ),
            ]
          : selectMode == SelectMode.select
              ? [
                  IconButton(
                    tooltip: L10n.of(context)!.toggleUnread,
                    icon: Icon(
                      size: 26,
                      controller.anySelectedRoomNotMarkedUnread
                          ? Icons.mark_chat_read_outlined
                          : Icons.mark_chat_unread_outlined,
                    ),
                    onPressed: controller.toggleUnread,
                  ),
                  IconButton(
                    tooltip: L10n.of(context)!.toggleFavorite,
                    icon: Icon(
                      size: 26,
                      controller.anySelectedRoomNotFavorite
                          ? Icons.push_pin_outlined
                          : Icons.push_pin,
                    ),
                    onPressed: controller.toggleFavouriteRoom,
                  ),
                  IconButton(
                    icon: Icon(
                      size: 26,
                      controller.anySelectedRoomNotMuted
                          ? Icons.volume_off_outlined
                          : Icons.volume_up_outlined,
                    ),
                    tooltip: L10n.of(context)!.toggleMuted,
                    onPressed: controller.toggleMuted,
                  ),
                  IconButton(
                    icon: const Icon(
                      Icons.delete_outlined,
                      size: 26,
                    ),
                    tooltip: L10n.of(context)!.archive,
                    onPressed: controller.archiveAction,
                  ),
                ]
              : null,
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(controller.isSearchMode ? 106 : 56);
}
