import 'package:decentralized_messenger/pages/chat/send_file_screen.dart';
import 'package:decentralized_messenger/pages/chat_list/chat_list.dart';
import 'package:decentralized_messenger/pages/nomo_utils/nomo_extensions.dart';
import 'package:flutter/material.dart';
import 'package:adaptive_dialog/adaptive_dialog.dart';
import 'package:flutter_gen/gen_l10n/l10n.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:future_loading_dialog/future_loading_dialog.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:matrix/matrix.dart';
import 'package:decentralized_messenger/config/app_config.dart';
import 'package:decentralized_messenger/utils/matrix_sdk_extensions/matrix_locals.dart';
import 'package:decentralized_messenger/utils/room_status_extension.dart';
import 'package:decentralized_messenger/widgets/hover_builder.dart';
import 'package:nomo_ui_kit/components/buttons/primary/nomo_primary_button.dart';
import 'package:nomo_ui_kit/components/dialog/nomo_dialog.dart';
import 'package:nomo_ui_kit/components/text/nomo_text.dart';
import 'package:nomo_ui_kit/theme/nomo_theme.dart';
import '../../config/themes.dart';
import '../../utils/date_time_extension.dart';
import '../../widgets/avatar.dart';
import 'package:decentralized_messenger/widgets/nomo_matrix.dart';

enum ArchivedRoomAction { delete, rejoin }

class ChatListItem extends StatelessWidget {
  final Room room;
  final bool activeChat;
  final bool selected;
  final void Function()? onTap;
  final void Function()? onLongPress;
  final void Function()? onForget;
  final ChatListController? controller;

  const ChatListItem(
    this.room, {
    this.activeChat = false,
    this.selected = false,
    this.onTap,
    this.onLongPress,
    this.onForget,
    this.controller,
    super.key,
  });

  void clickAction(BuildContext context) async {
    if (onTap != null) return onTap!();
    if (activeChat) return;
    if (room.membership == Membership.invite) {
      final inviteAction = await showDialog(
        context: context,
        useRootNavigator: false,
        builder: (context) {
          return NomoDialog(
            content: Column(
              children: [
                Avatar(
                  mxContent: room.avatar,
                  name: room.getLocalizedDisplayname(
                    MatrixLocals(L10n.of(context)!),
                  ),
                  size: 80,
                ),
                const SizedBox(height: 20),
                NomoText(
                  room.isDirectChat
                      ? L10n.of(context)!.invitePrivateChat
                      : L10n.of(context)!.inviteGroupChat,
                  fontSize: context.theme.typography.h1.fontSize,
                  fontWeight: FontWeight.bold,
                ),
                const SizedBox(height: 40),
                PrimaryNomoButton(
                  text: L10n.of(context)!.accept,
                  width: double.infinity,
                  onPressed: () =>
                      Navigator.of(context).pop(InviteActions.accept),
                ),
                const SizedBox(height: 10),
                PrimaryNomoButton(
                  text: L10n.of(context)!.decline,
                  width: double.infinity,
                  onPressed: () =>
                      Navigator.of(context).pop(InviteActions.decline),
                  type: ActionType.disabled,
                ),
              ],
            ),
          );
        },
      );
      if (inviteAction == null) return;
      if (inviteAction == InviteActions.decline) {
        await showFutureLoadingDialog(
          context: context,
          future: room.leave,
        );
        return;
      }
      final joinResult = await showFutureLoadingDialog(
        context: context,
        future: () async {
          final waitForRoom = room.client.waitForRoomInSync(
            room.id,
            join: true,
          );
          await room.join();
          await waitForRoom;
        },
      );
      if (joinResult.error != null) return;
    }

    if (room.membership == Membership.ban) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(L10n.of(context)!.youHaveBeenBannedFromThisChat),
        ),
      );
      return;
    }

    if (room.membership == Membership.leave) {
      context.go('/rooms/archive/${room.id}');
    }

    if (room.membership == Membership.join) {
      // Share content into this room
      final shareContent = NomoMatrix.I.shareContent;
      if (shareContent != null) {
        final shareFile = shareContent.tryGet<MatrixFile>('file');
        if (shareContent.tryGet<String>('msgtype') ==
                'chat.fluffy.shared_file' &&
            shareFile != null) {
          final file = XFile.fromData(shareFile.bytes,
              name: shareFile.name, length: shareFile.size);
          Navigator.of(context, rootNavigator: true).push(MaterialPageRoute(
            builder: (context) => NomoSendFileScreen(
              files: [file],
              room: room,
            ),
          ));
          NomoMatrix.I.shareContent = null;
        } else {
          final consent = await showOkCancelAlertDialog(
            context: context,
            title: L10n.of(context)!.forward,
            message: L10n.of(context)!.forwardMessageTo(
              room.getLocalizedDisplayname(MatrixLocals(L10n.of(context)!)),
            ),
            okLabel: L10n.of(context)!.forward,
            cancelLabel: L10n.of(context)!.cancel,
          );
          if (consent == OkCancelResult.cancel) {
            NomoMatrix.I.shareContent = null;
            return;
          }
          if (consent == OkCancelResult.ok) {
            room.sendEvent(shareContent);
            NomoMatrix.I.shareContent = null;
          }
        }
      }

      context.go('/rooms/${room.id}');
    }
  }

  Future<void> archiveAction(BuildContext context) async {
    {
      if ([Membership.leave, Membership.ban].contains(room.membership)) {
        await showFutureLoadingDialog(
          context: context,
          future: () => room.forget(),
        );
        return;
      }
      final confirmed = await showOkCancelAlertDialog(
        useRootNavigator: false,
        context: context,
        title: L10n.of(context)!.areYouSure,
        okLabel: L10n.of(context)!.yes,
        cancelLabel: L10n.of(context)!.no,
        message: L10n.of(context)!.archiveRoomDescription,
      );
      if (confirmed == OkCancelResult.cancel) return;
      await showFutureLoadingDialog(
        context: context,
        future: () => room.leave(),
      );
      return;
    }
  }

  @override
  Widget build(BuildContext context) {
    final isMuted = room.pushRuleState != PushRuleState.notify;
    final typingText = room.getLocalizedTypingText(context);
    final lastEvent = room.lastEvent;
    final ownMessage = lastEvent?.senderId == NomoMatrix.I.client.userID;
    final unread = room.isUnread || room.membership == Membership.invite;
    final unreadBubbleSize = unread || room.hasNewMessages
        ? room.notificationCount > 0
            ? 20.0
            : 14.0
        : 0.0;
    final hasNotifications = room.notificationCount > 0;
    final backgroundColor = selected
        ? Theme.of(context).colorScheme.primaryContainer
        : !activeChat
            ? Theme.of(context).colorScheme.secondaryContainer
            : null;
    final displayname = room.getLocalizedDisplayname(
      MatrixLocals(L10n.of(context)!),
    );
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: 8,
        vertical: 1,
      ),
      child: FutureBuilder(
        future: room.loadHeroUsers(),
        builder: (context, snapshot) => HoverBuilder(
          builder: (context, hovered) => ListTile(
            visualDensity: const VisualDensity(vertical: -0.5),
            contentPadding: const EdgeInsets.symmetric(horizontal: 8),
            onLongPress: room.name != "Note to self" ? onLongPress : null,
            leading: Avatar(
              mxContent: room.avatar,
              name: displayname,
              presenceUserId: room.directChatMatrixID,
              presenceBackgroundColor: backgroundColor,
              isDirectChat: room.isDirectChat,
              isNoteToSelfe: room.name == "Note to self",
            ),
            title: Row(
              children: <Widget>[
                room.name != "Note to self"
                    ? Expanded(
                        child: Text(
                          displayname,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          softWrap: false,
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      )
                    : Expanded(
                        child: Row(
                        children: [
                          Text(
                            displayname,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            softWrap: false,
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(width: 6),
                          FaIcon(
                            FontAwesomeIcons.squareCheck,
                            size: 14,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                        ],
                      )),
                const SizedBox(width: 4),
                if (isMuted)
                  const Padding(
                    padding: EdgeInsets.only(left: 4.0),
                    child: Icon(
                      Icons.volume_off_outlined,
                      size: 16,
                    ),
                  ),
                if (room.isFavourite || room.membership == Membership.invite)
                  Padding(
                    padding: EdgeInsets.only(
                      right: hasNotifications ? 4.0 : 0.0,
                    ),
                    child: Icon(
                      Icons.push_pin,
                      size: 16,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                if (lastEvent != null && room.membership != Membership.invite)
                  Padding(
                    padding: const EdgeInsets.only(left: 4.0),
                    child: Text(
                      lastEvent.originServerTs.localizedTimeShort(context),
                      style: TextStyle(
                        fontSize: 13,
                        color: unread
                            ? Theme.of(context).colorScheme.secondary
                            : Theme.of(context).textTheme.bodyMedium!.color,
                      ),
                    ),
                  ),
              ],
            ),
            subtitle: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                if (typingText.isEmpty &&
                    ownMessage &&
                    room.lastEvent!.status.isSending) ...[
                  const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator.adaptive(strokeWidth: 2),
                  ),
                  const SizedBox(width: 4),
                ],
                AnimatedContainer(
                  width: typingText.isEmpty ? 0 : 18,
                  clipBehavior: Clip.hardEdge,
                  decoration: const BoxDecoration(),
                  duration: NomoThemes.animationDuration,
                  curve: NomoThemes.animationCurve,
                  padding: const EdgeInsets.only(right: 4),
                  child: Icon(
                    Icons.edit_outlined,
                    color: Theme.of(context).colorScheme.secondary,
                    size: 14,
                  ),
                ),
                Expanded(
                  flex: 5,
                  child: typingText.isNotEmpty
                      ? Text(
                          typingText,
                          style: TextStyle(
                            color: Theme.of(context).colorScheme.primary,
                          ),
                          maxLines: 1,
                          softWrap: false,
                        )
                      : FutureBuilder<String>(
                          future: room.getLastMessage(context),
                          builder: (context, snapshot) {
                            return Opacity(
                              opacity: 0.5,
                              child: snapshot.data != null
                                  ? Text(
                                      room.membership == Membership.invite
                                          ? room.isDirectChat
                                              ? L10n.of(context)!
                                                  .invitePrivateChat
                                              : L10n.of(context)!
                                                  .inviteGroupChat
                                          : snapshot.data!,
                                      softWrap: false,
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                      style: TextStyle(
                                        fontWeight:
                                            unread ? FontWeight.w600 : null,
                                        color: Theme.of(context)
                                            .colorScheme
                                            .onSurfaceVariant,
                                        decoration:
                                            room.lastEvent?.redacted == true
                                                ? TextDecoration.lineThrough
                                                : null,
                                      ),
                                    )
                                  : Row(
                                      children: [
                                        Container(
                                          width: 14,
                                          height: 14,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2.0,
                                            strokeAlign:
                                                BorderSide.strokeAlignInside,
                                          ),
                                        ),
                                      ],
                                    ),
                            );
                          },
                        ),
                ),
                const Spacer(
                  flex: 2,
                ),
                AnimatedContainer(
                  duration: NomoThemes.animationDuration,
                  curve: NomoThemes.animationCurve,
                  padding: const EdgeInsets.symmetric(horizontal: 7),
                  height: unreadBubbleSize,
                  width: !hasNotifications && !unread && !room.hasNewMessages
                      ? 0
                      : (unreadBubbleSize - 9) *
                              room.notificationCount.toString().length +
                          9,
                  decoration: BoxDecoration(
                    color: room.highlightCount > 0 ||
                            room.membership == Membership.invite
                        ? Colors.red
                        : hasNotifications || room.markedUnread
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(context).colorScheme.primaryContainer,
                    borderRadius: BorderRadius.circular(AppConfig.borderRadius),
                  ),
                  child: Center(
                    child: hasNotifications
                        ? Text(
                            room.notificationCount.toString(),
                            style: TextStyle(
                              color: room.highlightCount > 0
                                  ? Colors.white
                                  : hasNotifications
                                      ? Theme.of(context).colorScheme.onPrimary
                                      : Theme.of(context)
                                          .colorScheme
                                          .onPrimaryContainer,
                              fontSize: 13,
                            ),
                          )
                        : const SizedBox.shrink(),
                  ),
                ),
              ],
            ),
            onTap: () {
              if (controller != null) {
                !controller!.isTextFieldActiv
                    ? null
                    : controller!.toggleSearchMode();
                controller!.cancelSearch();
              }
              clickAction(context);
            },
            trailing: room.name != "Note to self"
                ? onForget == null
                    ? hovered || selected
                        ? IconButton(
                            color: selected
                                ? Theme.of(context).colorScheme.primary
                                : null,
                            icon: Icon(
                              selected
                                  ? Icons.check_circle
                                  : Icons.check_circle_outlined,
                            ),
                            onPressed: onLongPress,
                          )
                        : null
                    : IconButton(
                        icon: const Icon(Icons.delete_outlined),
                        onPressed: onForget,
                      )
                : null,
          ),
        ),
      ),
    );
  }
}

enum InviteActions {
  accept,
  decline,
  block,
}
