// ignore_for_file: use_build_context_synchronously

import 'dart:async';
import 'dart:io';

import 'package:decentralized_messenger/main.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:adaptive_dialog/adaptive_dialog.dart';
import 'package:flutter_gen/gen_l10n/l10n.dart';
import 'package:future_loading_dialog/future_loading_dialog.dart';
import 'package:go_router/go_router.dart';
import 'package:matrix/matrix.dart';
import 'package:receive_sharing_intent/receive_sharing_intent.dart';

import 'package:decentralized_messenger/config/app_config.dart';
import 'package:decentralized_messenger/config/themes.dart';
import 'package:decentralized_messenger/pages/chat_list/chat_list_view.dart';
import 'package:decentralized_messenger/utils/localized_exception_extension.dart';
import 'package:decentralized_messenger/utils/matrix_sdk_extensions/matrix_locals.dart';
import 'package:decentralized_messenger/utils/init_with_restore.dart';
import '../../../utils/account_bundles.dart';
import '../../utils/matrix_sdk_extensions/matrix_file_extension.dart';
import '../../utils/url_launcher.dart';
import '../../utils/voip/callkeep_manager.dart';
import 'package:decentralized_messenger/widgets/nomo_matrix.dart';
import '../bootstrap/bootstrap_dialog.dart';

import 'package:decentralized_messenger/utils/tor_stub.dart'
    if (dart.library.html) 'package:tor_detector_web/tor_detector_web.dart';

enum SelectMode {
  normal,
  share,
  select,
}

enum PopupMenuAction {
  settings,
  invite,
  newGroup,
  newSpace,
  setStatus,
  archive,
}

enum ActiveFilter {
  allChats,
  groups,
  messages,
  spaces,
}

enum ResultFilter {
  chats,
  users,
  public;
}

class ChatList extends StatefulWidget {
  static BuildContext? contextForVoip;
  final bool displayNavigationRail;
  final String? activeChat;

  const ChatList({
    super.key,
    this.displayNavigationRail = false,
    required this.activeChat,
  });

  @override
  ChatListController createState() => ChatListController();
}

class ChatListController extends State<ChatList>
    with TickerProviderStateMixin, RouteAware {
  ///
  /// Search and Filter
  ///
  String searchPrompt = '';
  String? previousUserSearchPrompt;
  String? previousPublicRoomSearchPrompt;
  ResultFilter resultFilter = ResultFilter.chats;

  void setResultFilter(ResultFilter filter) {
    if (filter == resultFilter) return; // no change
    setState(() {
      resultFilter = filter;
    });
    _search();
  }

  Future<void> _searchUser() async {
    final client = NomoMatrix.I.client;
    SearchUserDirectoryResponse? userSearchResult;
    final searchQuery = searchPrompt.trim();
    if (searchQuery == previousUserSearchPrompt &&
        this.userSearchResult != null) return;
    try {
      userSearchResult = await client.searchUserDirectory(
        searchQuery,
        limit: 20,
      );
    } catch (e, s) {
      Logs().w('Searching has crashed', e, s);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            e.toLocalizedString(context),
          ),
        ),
      );
    }
    this.userSearchResult = userSearchResult;
    previousUserSearchPrompt = searchQuery;
  }

  Future<void> _searchPublicRooms() async {
    final client = NomoMatrix.I.client;

    QueryPublicRoomsResponse? roomSearchResult;
    final searchQuery = searchPrompt.trim();
    if (searchQuery == previousPublicRoomSearchPrompt &&
        this.roomSearchResult != null) return;
    try {
      roomSearchResult = await client.queryPublicRooms(
        server: searchServer,
        filter: PublicRoomQueryFilter(
          genericSearchTerm: searchQuery,
          roomTypes: [
            "null",
          ],
        ), // https://spec.matrix.org/unstable/client-server-api/#types
        limit: 20,
      );

      if (searchQuery.isValidMatrixId &&
          searchQuery.sigil == '#' &&
          roomSearchResult.chunk
                  .any((room) => room.canonicalAlias == searchQuery) ==
              false) {
        final response = await client.getRoomIdByAlias(searchQuery);
        final roomId = response.roomId;
        if (roomId != null) {
          roomSearchResult.chunk.add(
            PublicRoomsChunk(
              name: searchQuery,
              guestCanJoin: false,
              numJoinedMembers: 0,
              roomId: roomId,
              worldReadable: false,
              canonicalAlias: searchQuery,
            ),
          );
        }
      }
    } catch (e, s) {
      Logs().w('Searching has crashed', e, s);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            e.toLocalizedString(context),
          ),
        ),
      );
    }
    this.roomSearchResult = roomSearchResult;
    previousPublicRoomSearchPrompt = searchQuery;
  }

  void _search() async {
    if (!isSearching) {
      setState(() {
        isSearching = true;
      });
    }

    final searchFuture = switch (resultFilter) {
      ResultFilter.chats => searchThroughRoomNames,
      ResultFilter.public => _searchPublicRooms,
      ResultFilter.users => _searchUser,
    };

    await searchFuture.call();

    setState(() {
      isSearching = false;
    });
  }

  Future<void> searchThroughRoomNames() async {
    List<Room> searchedRooms = [];
    for (var room in filteredRooms) {
      final name =
          room.getLocalizedDisplayname(MatrixLocals(L10n.of(context)!));

      final doesMatchWithSearchText = searchPrompt.isNotEmpty &&
          name.toLowerCase().contains(searchPrompt.toLowerCase());
      if (doesMatchWithSearchText) {
        searchedRooms.add(room);
      }
    }
    this.searchedRooms = searchPrompt.isEmpty ? null : searchedRooms;
  }

  void onSearchEnter(String text) {
    searchPrompt = text;

    setState(() {
      isSearchMode = true;
    });
    _coolDown?.cancel();
    _coolDown = Timer(const Duration(milliseconds: 500), _search);
  }

  void startSearch() {
    setState(() {
      isSearchMode = true;
    });
    searchFocusNode.requestFocus();
    _coolDown?.cancel();
    _coolDown = Timer(const Duration(milliseconds: 500), _search);
  }

  void cancelSearch({bool unfocus = true}) {
    setState(() {
      isSearchMode = false;
      roomSearchResult = userSearchResult = null;
      isSearching = false;
    });
    if (unfocus) searchFocusNode.unfocus();
  }

  ///
  /// Other
  ///
  StreamSubscription? _intentDataStreamSubscription;

  StreamSubscription? _intentFileStreamSubscription;

  StreamSubscription? _intentUriStreamSubscription;

  bool get displayNavigationBar =>
      !NomoThemes.isColumnMode(context) &&
      (spaces.isNotEmpty || AppConfig.separateChatTypes);

  String? activeSpaceId;

  void resetActiveSpaceId() {
    setState(() {
      selectedRoomIds.clear();
      activeSpaceId = null;
    });
  }

  bool isTextFieldActiv = false;

  void toggleSearchMode() {
    setState(() {
      isTextFieldActiv = !isTextFieldActiv;
    });
  }

  void setActiveSpace(String? spaceId) {
    setState(() {
      selectedRoomIds.clear();
      activeSpaceId = spaceId;
      activeFilter = ActiveFilter.spaces;
    });
  }

  void createNewSpace() async {
    final spaceId = await context.push<String?>('/rooms/newspace');
    if (spaceId != null) {
      setActiveSpace(spaceId);
    }
  }

  int get selectedIndex {
    switch (activeFilter) {
      case ActiveFilter.allChats:
      case ActiveFilter.messages:
        return 0;
      case ActiveFilter.groups:
        return 1;
      case ActiveFilter.spaces:
        return AppConfig.separateChatTypes ? 2 : 1;
    }
  }

  ActiveFilter getActiveFilterByDestination(int? i) {
    switch (i) {
      case 1:
        if (AppConfig.separateChatTypes) {
          return ActiveFilter.groups;
        }
        return ActiveFilter.spaces;
      case 2:
        return ActiveFilter.spaces;
      case 0:
      default:
        if (AppConfig.separateChatTypes) {
          return ActiveFilter.messages;
        }
        return ActiveFilter.allChats;
    }
  }

  void onDestinationSelected(int? i) {
    setState(() {
      selectedRoomIds.clear();
      activeFilter = getActiveFilterByDestination(i);
    });
  }

  ActiveFilter activeFilter = AppConfig.separateChatTypes
      ? ActiveFilter.messages
      : ActiveFilter.allChats;

  bool Function(Room) getRoomFilterByActiveFilter(ActiveFilter activeFilter) {
    switch (activeFilter) {
      case ActiveFilter.allChats:
        return (room) => true;
      case ActiveFilter.groups:
        return (room) => !room.isSpace && !room.isDirectChat;
      case ActiveFilter.messages:
        return (room) => !room.isSpace && room.isDirectChat;
      case ActiveFilter.spaces:
        return (r) => r.isSpace;
    }
  }

  List<Room> get filteredRooms => NomoMatrix.I.client.rooms
      .where(getRoomFilterByActiveFilter(activeFilter))
      .toList();

  bool isSearchMode = false;
  String? searchServer;
  Timer? _coolDown;
  Future<QueryPublicRoomsResponse>? publicRoomsResponse;
  SearchUserDirectoryResponse? userSearchResult;
  QueryPublicRoomsResponse? roomSearchResult;
  List<Room>? searchedRooms;

  bool isSearching = false;
  static const String _serverStoreNamespace = 'im.fluffychat.search.server';

  void setServer() async {
    final newServer = await showTextInputDialog(
      useRootNavigator: false,
      title: L10n.of(context)!.changeTheHomeserver,
      context: context,
      okLabel: L10n.of(context)!.ok,
      cancelLabel: L10n.of(context)!.cancel,
      textFields: [
        DialogTextField(
          prefixText: 'https://',
          hintText: NomoMatrix.I.client.homeserver?.host,
          initialText: searchServer,
          keyboardType: TextInputType.url,
          autocorrect: false,
          validator: (server) => server?.contains('.') == true
              ? null
              : L10n.of(context)!.invalidServerName,
        ),
      ],
    );
    if (newServer == null) return;
    NomoMatrix.I.store.setString(_serverStoreNamespace, newServer.single);
    setState(() {
      searchServer = newServer.single;
    });
    _coolDown?.cancel();
    _coolDown = Timer(const Duration(milliseconds: 500), _search);
  }

  final FocusNode searchFocusNode = FocusNode();

  bool isTorBrowser = false;

  BoxConstraints? snappingSheetContainerSize;

  final ScrollController scrollController = ScrollController();
  final ValueNotifier<bool> scrolledToTop = ValueNotifier(true);

  final StreamController<Client> _clientStream = StreamController.broadcast();

  Stream<Client> get clientStream => _clientStream.stream;

  void addAccountAction() => context.go('/rooms/settings/account');

  void _onScroll() {
    final newScrolledToTop = scrollController.position.pixels <= 0;
    if (newScrolledToTop != scrolledToTop.value) {
      scrolledToTop.value = newScrolledToTop;
    }
  }

  void editSpace(BuildContext context, String spaceId) async {
    await NomoMatrix.I.client.getRoomById(spaceId)!.postLoad();
    if (mounted) {
      context.push('/rooms/$spaceId/details');
    }
  }

  // Needs to match GroupsSpacesEntry for 'separate group' checking.
  List<Room> get spaces =>
      NomoMatrix.I.client.rooms.where((r) => r.isSpace).toList();

  final selectedRoomIds = <String>{};

  String? get activeChat => widget.activeChat;

  SelectMode get selectMode => NomoMatrix.I.shareContent != null
      ? SelectMode.share
      : selectedRoomIds.isEmpty
          ? SelectMode.normal
          : SelectMode.select;

  // ignore: unused_element
  void _processIncomingSharedFiles(List<SharedMediaFile> files) {
    if (files.isEmpty) return;
    final file = File(files.first.path.replaceFirst('file://', ''));

    NomoMatrix.I.shareContent = {
      'msgtype': 'chat.fluffy.shared_file',
      'file': MatrixFile(
        bytes: file.readAsBytesSync(),
        name: file.path,
      ).detectFileType,
    };
    context.go('/rooms');
  }

  // ignore: unused_element
  void _processIncomingSharedText(String? text) {
    if (text == null) return;
    if (text.toLowerCase().startsWith(AppConfig.deepLinkPrefix) ||
        text.toLowerCase().startsWith(AppConfig.inviteLinkPrefix) ||
        (text.toLowerCase().startsWith(AppConfig.schemePrefix) &&
            !RegExp(r'\s').hasMatch(text))) {
      return _processIncomingUris(text);
    }
    NomoMatrix.I.shareContent = {
      'msgtype': 'm.text',
      'body': text,
    };
    context.go('/rooms');
  }

  void _processIncomingUris(String? text) async {
    if (text == null) return;
    context.go('/rooms');
    WidgetsBinding.instance.addPostFrameCallback((_) {
      UrlLauncher(context, text).openMatrixToUrl();
    });
  }

  void createNoteToSelf() async {
    if (!InitWithRestoreExtension.isNewDevice) return;
    InitWithRestoreExtension.isNewDevice = false;
    final client = NomoMatrix.I.client;
    final hasPrivateRoom =
        client.rooms.any((room) => room.name == 'Note to self');

    if (!hasPrivateRoom) {
      await client.createRoom(
        invite: [client.userID!],
        isDirect: true,
        name: "Note to self",
        preset: CreateRoomPreset.trustedPrivateChat,
      );
    }
    final Room noteToSelfRoom =
        client.rooms.firstWhere((room) => room.name == 'Note to self');
    await noteToSelfRoom.sendEvent({}, type: EventTypes.Dummy);
  }

  @override
  void initState() {
    scrollController.addListener(_onScroll);
    _waitForFirstSync();
    _hackyWebRTCFixForWeb();
    initWalletAddresses();
    CallKeepManager().initialize();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (mounted) {
        searchServer = NomoMatrix.I.store.getString(_serverStoreNamespace);
        createNoteToSelf();
      }

      // Workaround for system UI overlay style not applied on app start
      SystemChrome.setSystemUIOverlayStyle(
        Theme.of(context).appBarTheme.systemOverlayStyle!,
      );
    });

    _checkTorBrowser();

    super.initState();
  }

  @override
  void dispose() {
    _intentDataStreamSubscription?.cancel();
    _intentFileStreamSubscription?.cancel();
    _intentUriStreamSubscription?.cancel();
    scrollController.removeListener(_onScroll);
    super.dispose();
  }

  void toggleSelection(String roomId) {
    setState(
      () => selectedRoomIds.contains(roomId)
          ? selectedRoomIds.remove(roomId)
          : selectedRoomIds.add(roomId),
    );
  }

  Future<void> toggleUnread() async {
    await showFutureLoadingDialog(
      context: context,
      future: () async {
        final markUnread = anySelectedRoomNotMarkedUnread;
        final client = NomoMatrix.I.client;
        for (final roomId in selectedRoomIds) {
          final room = client.getRoomById(roomId)!;
          if (room.markedUnread == markUnread) continue;
          await client.getRoomById(roomId)!.markUnread(markUnread);
        }
      },
    );
    cancelAction();
  }

  Future<void> toggleFavouriteRoom() async {
    await showFutureLoadingDialog(
      context: context,
      future: () async {
        final makeFavorite = anySelectedRoomNotFavorite;
        final client = NomoMatrix.I.client;
        for (final roomId in selectedRoomIds) {
          final room = client.getRoomById(roomId)!;
          if (room.isFavourite == makeFavorite) continue;
          await client.getRoomById(roomId)!.setFavourite(makeFavorite);
        }
      },
    );
    cancelAction();
  }

  Future<void> toggleMuted() async {
    await showFutureLoadingDialog(
      context: context,
      future: () async {
        final newState = anySelectedRoomNotMuted
            ? PushRuleState.mentionsOnly
            : PushRuleState.notify;
        final client = NomoMatrix.I.client;
        for (final roomId in selectedRoomIds) {
          final room = client.getRoomById(roomId)!;
          if (room.pushRuleState == newState) continue;
          await client.getRoomById(roomId)!.setPushRuleState(newState);
        }
      },
    );
    cancelAction();
  }

  Future<void> archiveAction() async {
    final confirmed = await showOkCancelAlertDialog(
          useRootNavigator: false,
          context: context,
          title: L10n.of(context)!.areYouSure,
          okLabel: L10n.of(context)!.yes,
          cancelLabel: L10n.of(context)!.cancel,
          message: L10n.of(context)!.archiveRoomDescription,
        ) ==
        OkCancelResult.ok;
    if (!confirmed) return;
    await showFutureLoadingDialog(
      context: context,
      future: () => _archiveSelectedRooms(),
    );
    setState(() {});
  }

  void setStatus() async {
    final client = NomoMatrix.I.client;
    final currentPresence = await client.fetchCurrentPresence(client.userID!);
    final input = await showTextInputDialog(
      useRootNavigator: false,
      context: context,
      title: L10n.of(context)!.setStatus,
      message: L10n.of(context)!.leaveEmptyToClearStatus,
      okLabel: L10n.of(context)!.ok,
      cancelLabel: L10n.of(context)!.cancel,
      textFields: [
        DialogTextField(
          hintText: L10n.of(context)!.statusExampleMessage,
          maxLines: 6,
          minLines: 1,
          maxLength: 255,
          initialText: currentPresence.statusMsg,
        ),
      ],
    );
    if (input == null) return;
    if (!mounted) return;
    await showFutureLoadingDialog(
      context: context,
      future: () => client.setPresence(
        client.userID!,
        PresenceType.online,
        statusMsg: input.single,
      ),
    );
  }

  Future<void> _archiveSelectedRooms() async {
    final client = NomoMatrix.I.client;
    while (selectedRoomIds.isNotEmpty) {
      final roomId = selectedRoomIds.first;
      try {
        await client.getRoomById(roomId)!.leave();
      } finally {
        toggleSelection(roomId);
      }
    }
  }

  Future<void> addToSpace() async {
    final selectedSpace = await showConfirmationDialog<String>(
      context: context,
      title: L10n.of(context)!.addToSpace,
      message: L10n.of(context)!.addToSpaceDescription,
      fullyCapitalizedForMaterial: false,
      actions: NomoMatrix.I.client.rooms
          .where((r) => r.isSpace)
          .map(
            (space) => AlertDialogAction(
              key: space.id,
              label: space
                  .getLocalizedDisplayname(MatrixLocals(L10n.of(context)!)),
            ),
          )
          .toList(),
    );
    if (selectedSpace == null) return;
    final result = await showFutureLoadingDialog(
      context: context,
      future: () async {
        final space = NomoMatrix.I.client.getRoomById(selectedSpace)!;
        if (space.canSendDefaultStates) {
          for (final roomId in selectedRoomIds) {
            await space.setSpaceChild(roomId);
          }
        }
      },
    );
    if (result.error == null) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(L10n.of(context)!.chatHasBeenAddedToThisSpace),
        ),
      );
    }

    setState(() => selectedRoomIds.clear());
  }

  bool get anySelectedRoomNotMarkedUnread => selectedRoomIds.any(
        (roomId) => !NomoMatrix.I.client.getRoomById(roomId)!.markedUnread,
      );

  bool get anySelectedRoomNotFavorite => selectedRoomIds.any(
        (roomId) => !NomoMatrix.I.client.getRoomById(roomId)!.isFavourite,
      );

  bool get anySelectedRoomNotMuted => selectedRoomIds.any(
        (roomId) =>
            NomoMatrix.I.client.getRoomById(roomId)!.pushRuleState ==
            PushRuleState.notify,
      );

  bool waitForFirstSync = false;

  Future<void> _waitForFirstSync() async {
    final client = NomoMatrix.I.client;
    await client.roomsLoading;
    await client.accountDataLoading;
    await client.userDeviceKeysLoading;
    if (client.prevBatch == null) {
      await client.onSync.stream.first;

      // Display first login bootstrap if enabled
      if (client.encryption?.keyManager.enabled == true) {
        if (await client.encryption?.keyManager.isCached() == false ||
            await client.encryption?.crossSigning.isCached() == false ||
            client.isUnknownSession && !mounted) {
          await BootstrapDialog(client: client).show(context);
        }
      }
    }
    if (!mounted) return;
    setState(() {
      waitForFirstSync = true;
    });
  }

  void cancelAction() {
    if (selectMode == SelectMode.share) {
      setState(() => NomoMatrix.I.shareContent = null);
    } else {
      setState(() => selectedRoomIds.clear());
    }
  }

  void setActiveClient(Client client) {
    context.go('/rooms');
    setState(() {
      activeFilter = AppConfig.separateChatTypes
          ? ActiveFilter.messages
          : ActiveFilter.allChats;
      activeSpaceId = null;
      selectedRoomIds.clear();
      NomoMatrix.I.setActiveClient(client);
    });
    _clientStream.add(client);
  }

  void setActiveBundle(String bundle) {
    context.go('/rooms');
    setState(() {
      selectedRoomIds.clear();
      NomoMatrix.I.activeBundle = bundle;
      if (!NomoMatrix.I.currentBundle!
          .any((client) => client == NomoMatrix.I.client)) {
        NomoMatrix.I.setActiveClient(NomoMatrix.I.currentBundle!.first);
      }
    });
  }

  void editBundlesForAccount(String? userId, String? activeBundle) async {
    final l10n = L10n.of(context)!;
    final client =
        NomoMatrix.I.clients[NomoMatrix.I.getClientIndexByMatrixId(userId!)];
    final action = await showConfirmationDialog<EditBundleAction>(
      context: context,
      title: L10n.of(context)!.editBundlesForAccount,
      actions: [
        AlertDialogAction(
          key: EditBundleAction.addToBundle,
          label: L10n.of(context)!.addToBundle,
        ),
        if (activeBundle != client.userID)
          AlertDialogAction(
            key: EditBundleAction.removeFromBundle,
            label: L10n.of(context)!.removeFromBundle,
          ),
      ],
    );
    if (action == null) return;
    switch (action) {
      case EditBundleAction.addToBundle:
        final bundle = await showTextInputDialog(
          context: context,
          title: l10n.bundleName,
          textFields: [DialogTextField(hintText: l10n.bundleName)],
        );
        if (bundle == null || bundle.isEmpty || bundle.single.isEmpty) return;
        await showFutureLoadingDialog(
          context: context,
          future: () => client.setAccountBundle(bundle.single),
        );
        break;
      case EditBundleAction.removeFromBundle:
        await showFutureLoadingDialog(
          context: context,
          future: () => client.removeFromAccountBundle(activeBundle!),
        );
    }
  }

  bool get displayBundles =>
      NomoMatrix.I.hasComplexBundles &&
      NomoMatrix.I.accountBundles.keys.length > 1;

  String? get secureActiveBundle {
    if (NomoMatrix.I.activeBundle == null ||
        !NomoMatrix.I.accountBundles.keys.contains(NomoMatrix.I.activeBundle)) {
      return NomoMatrix.I.accountBundles.keys.first;
    }
    return NomoMatrix.I.activeBundle;
  }

  void resetActiveBundle() {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      setState(() {
        NomoMatrix.I.activeBundle = null;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return ChatListView(this);
  }

  void _hackyWebRTCFixForWeb() {
    ChatList.contextForVoip = context;
  }

  Future<void> _checkTorBrowser() async {
    if (!kIsWeb) return;
    final isTor = await TorBrowserDetector.isTorBrowser;
    isTorBrowser = isTor;
  }
}

enum EditBundleAction { addToBundle, removeFromBundle }
