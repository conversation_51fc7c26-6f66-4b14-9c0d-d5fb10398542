import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:adaptive_dialog/adaptive_dialog.dart';
import 'package:flutter_gen/gen_l10n/l10n.dart';

import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:keyboard_shortcuts/keyboard_shortcuts.dart';
import 'package:matrix/matrix.dart';
import 'package:decentralized_messenger/widgets/nomo_matrix.dart';
import 'package:nomo_ui_kit/theme/nomo_theme.dart';
import '../../utils/nomo_share.dart';
import 'chat_list.dart';

class ClientChooserButton extends ConsumerWidget {
  final ChatListController controller;

  const ClientChooserButton(this.controller, {super.key});

  List<PopupMenuEntry<Object>> _bundleMenuItems(BuildContext context) {
    // final matrix = NomoMatrix.I;
    // final bundles = matrix.accountBundles.keys.toList()
    //   ..sort(
    //     (a, b) => a!.isValidMatrixId == b!.isValidMatrixId
    //         ? 0
    //         : a.isValidMatrixId && !b.isValidMatrixId
    //             ? -1
    //             : 1,
    //   );
    // final matrix = NomoMatrix.I;
    // final bundles = matrix.accountBundles.keys.toList()
    //   ..sort(
    //     (a, b) => a!.isValidMatrixId == b!.isValidMatrixId
    //         ? 0
    //         : a.isValidMatrixId && !b.isValidMatrixId
    //             ? -1
    //             : 1,
    //   );
    return <PopupMenuEntry<Object>>[
      PopupMenuItem(
        value: SettingsAction.newGroup,
        child: Row(
          children: [
            const Icon(Icons.group_add_outlined),
            const SizedBox(width: 18),
            Text(L10n.of(context)!.createGroup),
          ],
        ),
      ),
      /* PopupMenuItem(
        value: SettingsAction.newSpace,
        child: Row(
          children: [
            const Icon(Icons.workspaces_outlined),
            const SizedBox(width: 18),
            Text(L10n.of(context)!.createNewSpace),
          ],
        ),
      ),*/
      // PopupMenuItem(
      //   value: SettingsAction.setStatus,
      //   child: Row(
      //     children: [
      //       const Icon(Icons.edit_outlined),
      //       const SizedBox(width: 18),
      //       Text(L10n.of(context)!.setStatus),
      //     ],
      //   ),
      // ),
      PopupMenuItem(
        value: SettingsAction.invite,
        child: Row(
          children: [
            Icon(Icons.adaptive.share_outlined),
            const SizedBox(width: 18),
            Text(L10n.of(context)!.inviteContact),
          ],
        ),
      ),
      // PopupMenuItem(
      //   value: SettingsAction.archive,
      //   child: Row(
      //     children: [
      //       const Icon(Icons.archive_outlined),
      //       const SizedBox(width: 18),
      //       Text(L10n.of(context)!.archive),
      //     ],
      //   ),
      // ),
      PopupMenuItem(
        value: SettingsAction.settings,
        child: Row(
          children: [
            const Icon(Icons.person_outline_rounded),
            const SizedBox(width: 18),
            Text(L10n.of(context)!.account),
          ],
        ),
      ),
      // const PopupMenuItem(
      //   value: null,
      //   child: Divider(height: 1),
      // ),
      // for (final bundle in bundles) ...[
      //   if (matrix.accountBundles[bundle]!.length != 1 ||
      //       matrix.accountBundles[bundle]!.single!.userID != bundle)
      //     PopupMenuItem(
      //       value: null,
      //       child: Column(
      //         crossAxisAlignment: CrossAxisAlignment.start,
      //         mainAxisSize: MainAxisSize.min,
      //         children: [
      //           Text(
      //             bundle!,
      //             style: TextStyle(
      //               color: Theme.of(context).textTheme.titleMedium!.color,
      //               fontSize: 14,
      //             ),
      //           ),
      //           const Divider(height: 1),
      //         ],
      //       ),
      //     ),
      //   ...matrix.accountBundles[bundle]!.map(
      //     (client) => PopupMenuItem(
      //       value: client,
      //       child: FutureBuilder<Profile?>(
      //         // analyzer does not understand this type cast for error
      //         // handling
      //         //
      //         // ignore: unnecessary_cast
      //         future: (client!.fetchOwnProfile() as Future<Profile?>)
      //             .onError((e, s) => null),
      //         builder: (context, snapshot) => Row(
      //           children: [
      //             Avatar(
      //               mxContent: snapshot.data?.avatarUrl,
      //               name:
      //                   snapshot.data?.displayName ?? client.userID!.localpart,
      //               size: 32,
      //               fontSize: 12,
      //             ),
      //             const SizedBox(width: 12),
      //             Expanded(
      //               child: Text(
      //                 snapshot.data?.displayName ?? client.userID!.localpart!,
      //                 overflow: TextOverflow.ellipsis,
      //               ),
      //             ),
      //             const SizedBox(width: 12),
      //             IconButton(
      //               icon: const Icon(Icons.edit_outlined),
      //               onPressed: () => controller.editBundlesForAccount(
      //                 client.userID,
      //                 bundle,
      //               ),
      //             ),
      //           ],
      //         ),
      //       ),
      //     ),
      //   ),
      // ],
      // PopupMenuItem(
      //   value: SettingsAction.addAccount,
      //   child: Row(
      //     children: [
      //       const Icon(Icons.person_add_outlined),
      //       const SizedBox(width: 18),
      //       Text(L10n.of(context)!.addAccount),
      //     ],
      //   ),
      // ),
      // const PopupMenuItem(
      //   value: null,
      //   child: Divider(height: 1),
      // ),
      // for (final bundle in bundles) ...[
      //   if (matrix.accountBundles[bundle]!.length != 1 ||
      //       matrix.accountBundles[bundle]!.single!.userID != bundle)
      //     PopupMenuItem(
      //       value: null,
      //       child: Column(
      //         crossAxisAlignment: CrossAxisAlignment.start,
      //         mainAxisSize: MainAxisSize.min,
      //         children: [
      //           Text(
      //             bundle!,
      //             style: TextStyle(
      //               color: Theme.of(context).textTheme.titleMedium!.color,
      //               fontSize: 14,
      //             ),
      //           ),
      //           const Divider(height: 1),
      //         ],
      //       ),
      //     ),
      //   ...matrix.accountBundles[bundle]!.map(
      //     (client) => PopupMenuItem(
      //       value: client,
      //       child: FutureBuilder<Profile?>(
      //         // analyzer does not understand this type cast for error
      //         // handling
      //         //
      //         // ignore: unnecessary_cast
      //         future: (client!.fetchOwnProfile() as Future<Profile?>)
      //             .onError((e, s) => null),
      //         builder: (context, snapshot) => Row(
      //           children: [
      //             Avatar(
      //               mxContent: snapshot.data?.avatarUrl,
      //               name:
      //                   snapshot.data?.displayName ?? client.userID!.localpart,
      //               size: 32,
      //               fontSize: 12,
      //             ),
      //             const SizedBox(width: 12),
      //             Expanded(
      //               child: Text(
      //                 snapshot.data?.displayName ?? client.userID!.localpart!,
      //                 overflow: TextOverflow.ellipsis,
      //               ),
      //             ),
      //             const SizedBox(width: 12),
      //             IconButton(
      //               icon: const Icon(Icons.edit_outlined),
      //               onPressed: () => controller.editBundlesForAccount(
      //                 client.userID,
      //                 bundle,
      //               ),
      //             ),
      //           ],
      //         ),
      //       ),
      //     ),
      //   ),
      // ],
      // PopupMenuItem(
      //   value: SettingsAction.addAccount,
      //   child: Row(
      //     children: [
      //       const Icon(Icons.person_add_outlined),
      //       const SizedBox(width: 18),
      //       Text(L10n.of(context)!.addAccount),
      //     ],
      //   ),
      // ),
    ];
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final matrix = NomoMatrix.I;

    int clientCount = 0;
    matrix.accountBundles.forEach((key, value) => clientCount += value.length);
    return FutureBuilder<Profile>(
      future: matrix.client.fetchOwnProfile(),
      builder: (context, snapshot) => Stack(
        alignment: Alignment.center,
        children: [
          ...List.generate(
            clientCount,
            (index) => KeyBoardShortcuts(
              keysToPress: _buildKeyboardShortcut(index + 1),
              helpLabel: L10n.of(context)!.switchToAccount(index + 1),
              onKeysPressed: () => _handleKeyboardShortcut(
                index,
                context,
              ),
              child: const SizedBox.shrink(),
            ),
          ),
          KeyBoardShortcuts(
            keysToPress: {
              LogicalKeyboardKey.controlLeft,
              LogicalKeyboardKey.tab,
            },
            helpLabel: L10n.of(context)!.nextAccount,
            onKeysPressed: () => _nextAccount(context),
            child: const SizedBox.shrink(),
          ),
          KeyBoardShortcuts(
            keysToPress: {
              LogicalKeyboardKey.controlLeft,
              LogicalKeyboardKey.shiftLeft,
              LogicalKeyboardKey.tab,
            },
            helpLabel: L10n.of(context)!.previousAccount,
            onKeysPressed: () => _previousAccount(context),
            child: const SizedBox.shrink(),
          ),
          PopupMenuButton<Object>(
            onSelected: (o) => _clientSelected(o, context),
            itemBuilder: _bundleMenuItems,
            child: Icon(
              Icons.account_circle_outlined,
              color: NomoTheme.of(context).colors.foreground1,
              size: 34,
            ),
          ),
        ],
      ),
    );
  }

  Set<LogicalKeyboardKey>? _buildKeyboardShortcut(int index) {
    if (index > 0 && index < 10) {
      return {
        LogicalKeyboardKey.altLeft,
        LogicalKeyboardKey(0x00000000030 + index),
      };
    } else {
      return null;
    }
  }

  void _clientSelected(
    Object object,
    BuildContext context,
  ) async {
    if (object is Client) {
      controller.setActiveClient(object);
    } else if (object is String) {
      controller.setActiveBundle(object);
    } else if (object is SettingsAction) {
      switch (object) {
        case SettingsAction.addAccount:
          final consent = await showOkCancelAlertDialog(
            context: context,
            title: L10n.of(context)!.addAccount,
            message: L10n.of(context)!.enableMultiAccounts,
            okLabel: L10n.of(context)!.next,
            cancelLabel: L10n.of(context)!.cancel,
          );
          if (consent != OkCancelResult.ok) return;
          context.go('/rooms/settings/addaccount');
          break;
        case SettingsAction.newGroup:
          context.go('/rooms/newgroup');
          break;
        case SettingsAction.newSpace:
          controller.createNewSpace();
          break;
        case SettingsAction.invite:
          NomoShare.shareInviteLink(context);
          break;
        case SettingsAction.settings:
          context.go('/rooms/settings');
          break;
        case SettingsAction.archive:
          context.go('/rooms/archive');
          break;
        case SettingsAction.setStatus:
          controller.setStatus();
          break;
      }
    }
  }

  void _handleKeyboardShortcut(
    int index,
    BuildContext context,
  ) {
    final matrix = NomoMatrix.I;
    final bundles = matrix.accountBundles.keys.toList()
      ..sort(
        (a, b) => a!.isValidMatrixId == b!.isValidMatrixId
            ? 0
            : a.isValidMatrixId && !b.isValidMatrixId
                ? -1
                : 1,
      );
    // beginning from end if negative
    if (index < 0) {
      int clientCount = 0;
      matrix.accountBundles
          .forEach((key, value) => clientCount += value.length);
      _handleKeyboardShortcut(clientCount, context);
    }
    for (final bundleName in bundles) {
      final bundle = matrix.accountBundles[bundleName];
      if (bundle != null) {
        if (index < bundle.length) {
          return _clientSelected(bundle[index]!, context);
        } else {
          index -= bundle.length;
        }
      }
    }
    // if index too high, restarting from 0
    _handleKeyboardShortcut(0, context);
  }

  int? _shortcutIndexOfClient(Client client) {
    int index = 0;
    final matrix = NomoMatrix.I;

    final bundles = matrix.accountBundles.keys.toList()
      ..sort(
        (a, b) => a!.isValidMatrixId == b!.isValidMatrixId
            ? 0
            : a.isValidMatrixId && !b.isValidMatrixId
                ? -1
                : 1,
      );
    for (final bundleName in bundles) {
      final bundle = matrix.accountBundles[bundleName];
      if (bundle == null) return null;
      if (bundle.contains(client)) {
        return index + bundle.indexOf(client);
      } else {
        index += bundle.length;
      }
    }
    return null;
  }

  void _nextAccount(BuildContext context) {
    final matrix = NomoMatrix.I;
    final client = matrix.client;
    final lastIndex = _shortcutIndexOfClient(client);
    _handleKeyboardShortcut(lastIndex! + 1, context);
  }

  void _previousAccount(BuildContext context) {
    final matrix = NomoMatrix.I;
    final client = matrix.client;
    final lastIndex = _shortcutIndexOfClient(client);
    _handleKeyboardShortcut(lastIndex! - 1, context);
  }
}

enum SettingsAction {
  addAccount,
  newGroup,
  newSpace,
  setStatus,
  invite,
  settings,
  archive,
}
