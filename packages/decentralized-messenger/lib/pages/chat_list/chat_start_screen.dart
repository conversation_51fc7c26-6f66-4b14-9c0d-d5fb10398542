import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:nomo_app/common/widgets/scaffold/zeniq_scaffold.dart';
import 'package:nomo_app/theming/theme.dart';
import 'package:nomo_ui_kit/components/buttons/primary/nomo_primary_button.dart';
import 'package:nomo_ui_kit/components/text/nomo_text.dart';
import 'package:nomo_ui_kit/theme/nomo_theme.dart';

class ChatStartScreen extends StatelessWidget {
  const ChatStartScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final nomoTheme = NomoTheme.themeOf(context);
    final theme = nomoTheme.value;
    final colorMode = nomoTheme.colorMode as ColorMode;
    final customModeBrigthness =
        theme.colors.brightness == Brightness.light ? "light" : "dark";

    final imagePath = switch (colorMode) {
      ColorMode.LIGHT => 'assets/images/light/start_new_chat.png',
      ColorMode.DARK => 'assets/images/dark/start_new_chat.png',
      ColorMode.AVINOC => 'assets/images/avinoc/start_new_chat.png',
      ColorMode.XERA => 'assets/images/xera/start_new_chat.png',
      ColorMode.BLACK => 'assets/images/dark/start_new_chat.png',
      ColorMode.CUSTOM =>
        'assets/images/$customModeBrigthness/start_new_chat.png',
    };

    return ZeniqScaffold(
      body: Center(
        child: Padding(
          padding: const EdgeInsets.only(top: 12),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              Image.asset(
                imagePath,
                width: 300,
                height: 300,
              ),
              const SizedBox(height: 24),
              NomoText(
                'Start your first chat',
                style: theme.typography.h2,
                color: theme.colors.primary,
                fontWeight: FontWeight.bold,
              ),
              NomoText(
                "right now!",
                style: theme.typography.h2,
                color: theme.colors.primary,
                fontWeight: FontWeight.bold,
              ),
              const SizedBox(height: 24),
              Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  NomoText(
                    "1. Tap on the New Chat button",
                    style: theme.typography.b3,
                  ),
                  NomoText(
                    "2. Scan the QR code of a friend",
                    style: theme.typography.b3,
                  ),
                  NomoText(
                    "3. Have fun chatting!",
                    style: theme.typography.b3,
                  ),
                ],
              ),
              const SizedBox(
                height: 24,
              ),
              PrimaryNomoButton(
                height: 53,
                margin: const EdgeInsets.symmetric(horizontal: 32),
                expandToConstraints: true,
                backgroundColor: theme.colors.primary,
                onPressed: () {
                  context.go('/rooms/newprivatechat');
                },
                text: "New Chat",
                textStyle: theme.typography.b3.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
