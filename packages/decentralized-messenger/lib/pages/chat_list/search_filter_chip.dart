import 'package:decentralized_messenger/pages/chat_list/chat_list.dart';
import 'package:flutter/material.dart';
import 'package:nomo_ui_kit/components/text/nomo_text.dart';
import 'package:nomo_ui_kit/theme/nomo_theme.dart';

class SearchFilterWidget extends StatelessWidget {
  const SearchFilterWidget({
    super.key,
    required this.resultFilter,
    required this.label,
    required this.controller,
  });

  final ResultFilter resultFilter;
  final String label;
  final ChatListController controller;

  @override
  Widget build(BuildContext context) {
    final isSelected = controller.resultFilter == resultFilter;
    return ActionChip(
      label: NomoText(
        label,
        style: NomoTheme.of(context).typography.b3,
        color: isSelected
            ? NomoTheme.of(context).colors.foreground1
            : NomoTheme.of(context).colors.foreground1.withOpacity(0.5),
        fontWeight: FontWeight.w600,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(50),
        side: isSelected
            ? BorderSide(color: NomoTheme.of(context).colors.primary, width: .5)
            : BorderSide(color: NomoTheme.of(context).colors.background1),
      ),
      backgroundColor: isSelected
          ? NomoTheme.of(context).colors.secondaryContainer
          : NomoTheme.of(context).colors.background2,
      onPressed: () {
        controller.setResultFilter(resultFilter);
      },
    );
  }
}
