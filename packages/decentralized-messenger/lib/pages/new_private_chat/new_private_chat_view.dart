import 'package:decentralized_messenger/pages/nomo_utils/send_invite.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/l10n.dart';
import 'package:go_router/go_router.dart';
import 'package:matrix/matrix.dart';
import 'package:nomo_app/common/widgets/scaffold/zeniq_scaffold.dart';
import 'package:nomo_ui_kit/components/buttons/primary/nomo_primary_button.dart';
import 'package:nomo_ui_kit/components/text/nomo_text.dart';
import 'package:nomo_ui_kit/theme/nomo_theme.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:decentralized_messenger/config/themes.dart';
import 'package:decentralized_messenger/pages/new_private_chat/new_private_chat.dart';
import 'package:decentralized_messenger/utils/localized_exception_extension.dart';
import 'package:decentralized_messenger/widgets/avatar.dart';
import 'package:decentralized_messenger/widgets/layouts/max_width_body.dart';
import 'package:decentralized_messenger/widgets/nomo_matrix.dart';

Future<String?> getDisplayName(String userId, context) async {
  return await NomoMatrix.I.client.getDisplayName(userId);
}

class NewPrivateChatView extends StatelessWidget {
  final NewPrivateChatController controller;
  static const double horizontalPadding = 28.0;

  const NewPrivateChatView(this.controller, {super.key});



  @override
  Widget build(BuildContext context) {
    final searchResponse = controller.searchResponse;
    final myCode = 'https://nomo.id/${NomoMatrix.I.client.userID}'
        .replaceAll(':zeniq.chat', '');
    final screenWidth = MediaQuery.of(context).size.width;
    final qrCardWidth = screenWidth > 600 ? 400.0 : screenWidth * 0.85;

    return ZeniqScaffold(
      title: L10n.of(context)!.newChat,
      leading: const BackButton(),
      body: MaxWidthBody(
        withScrolling: false,
        innerPadding: const EdgeInsets.symmetric(vertical: 8),
        child: Column(
          children: [
            Expanded(
              child: AnimatedCrossFade(
                duration: NomoThemes.animationDuration,
                crossFadeState: searchResponse == null
                    ? CrossFadeState.showFirst
                    : CrossFadeState.showSecond,
                firstChild: ListView(
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: horizontalPadding),
                      child: Center(
                        child: Card(
                          color: Colors.white,
                          elevation: 3,
                          child: Padding(
                            padding: const EdgeInsets.symmetric(vertical: 14.0),
                            child: SizedBox(
                              width: qrCardWidth,
                              height: qrCardWidth,
                              child: Column(
                                children: [
                                  Expanded(
                                    child: QrImage(
                                      data: myCode,
                                      version: QrVersions.auto,
                                    ),
                                  ),
                                  const SizedBox(height: 12),
                                  Padding(
                                    padding: const EdgeInsets.symmetric(horizontal: horizontalPadding),
                                    child: NomoText(
                                      "scan_qr_code_to_invite",
                                      style: NomoTheme.of(context).typography.b3,
                                      color: Colors.black,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 12),
                    PrimaryNomoButton(
                        margin: const EdgeInsets.symmetric(
                            vertical: 8, horizontal: horizontalPadding + 4),
                        height: 53,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(Icons.share_outlined),
                            const SizedBox(width: 12),
                            NomoText(
                              L10n.of(context)!.shareInviteLink,
                              style: NomoTheme.of(context).typography.b3,
                              color: NomoTheme.of(context).colors.onPrimary,
                              fontWeight: FontWeight.bold,
                            ),
                          ],
                        ),
                        onPressed: () async {
                          final userId = NomoMatrix.I.client.userID!;
                          final displayName = await getDisplayName(userId, context);
                          await shareQrCode(myCode, displayName!);
                        }),
                    Center(
                      child: NomoText(
                        "or",
                        style: NomoTheme.of(context).typography.b3,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    PrimaryNomoButton(
                      backgroundColor: NomoTheme.of(context).colors.background1,
                      margin: const EdgeInsets.symmetric(
                          vertical: 8, horizontal: horizontalPadding + 4),
                      height: 53,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.group_add_outlined,
                            color: NomoTheme.of(context).colors.foreground1,
                          ),
                          const SizedBox(width: 12),
                          NomoText(
                            L10n.of(context)!.createGroup,
                            style: NomoTheme.of(context).typography.b3,
                            fontWeight: FontWeight.bold,
                          ),
                        ],
                      ),
                      onPressed: () => context.go('/rooms/newgroup'),
                    ),
                  ],
                ),
                secondChild: FutureBuilder(
                  future: searchResponse,
                  builder: (context, snapshot) {
                    final result = snapshot.data;
                    final error = snapshot.error;
                    if (error != null) {
                      return Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            error.toLocalizedString(context),
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.error,
                            ),
                          ),
                          const SizedBox(height: 12),
                          OutlinedButton.icon(
                            onPressed: controller.searchUsers,
                            icon: const Icon(Icons.refresh_outlined),
                            label: Text(L10n.of(context)!.tryAgain),
                          ),
                        ],
                      );
                    }
                    if (result == null) {
                      return const Center(
                        child: CircularProgressIndicator.adaptive(),
                      );
                    }
                    if (result.isEmpty) {
                      return Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(Icons.search_outlined, size: 86),
                          Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Text(
                              L10n.of(context)!.noUsersFoundWithQuery(
                                controller.controller.text,
                              ),
                              style: TextStyle(
                                color: Theme.of(context).colorScheme.primary,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ],
                      );
                    }
                    return ListView.builder(
                      itemCount: result.length,
                      itemBuilder: (context, i) {
                        final contact = result[i];
                        final displayname = contact.displayName ??
                            contact.userId.localpart ??
                            contact.userId;
                        return ListTile(
                          leading: Avatar(
                            name: displayname,
                            mxContent: contact.avatarUrl,
                            presenceUserId: contact.userId,
                          ),
                          title: Text(displayname),
                          subtitle: Text(contact.userId),
                          onTap: () => controller.openUserModal(contact),
                        );
                      },
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
