import 'dart:async';
import 'package:flutter/material.dart';
import 'package:nomo_ui_kit/components/buttons/primary/nomo_primary_button.dart';
import 'package:nomo_ui_kit/components/text/nomo_text.dart';
import 'package:future_loading_dialog/future_loading_dialog.dart';
import 'package:geolocator/geolocator.dart';
import 'package:matrix/matrix.dart';

import 'package:decentralized_messenger/pages/chat/events/map_bubble.dart';
import 'package:nomo_ui_kit/theme/nomo_theme.dart';

class NomoSendLocationScreen extends StatefulWidget {
  final Room room;

  const NomoSendLocationScreen({
    required this.room,
    super.key,
  });

  @override
  NomoSendLocationScreenState createState() => NomoSendLocationScreenState();
}

class NomoSendLocationScreenState extends State<NomoSendLocationScreen> {
  bool disabled = false;
  bool denied = false;
  bool isSending = false;
  Position? position;
  Object? error;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) => requestLocation());
  }

  Future<void> requestLocation() async {
    if (!(await Geolocator.isLocationServiceEnabled())) {
      setState(() => disabled = true);
      return;
    }
    var permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        setState(() => denied = true);
        return;
      }
    }
    if (permission == LocationPermission.deniedForever) {
      setState(() => denied = true);
      return;
    }
    try {
      try {
        position = await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.best,
          timeLimit: const Duration(seconds: 30),
        );
      } on TimeoutException {
        position = await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.medium,
          timeLimit: const Duration(seconds: 30),
        );
      }
    } catch (e) {
      error = e;
    } finally {
      setState(() {});
    }
  }

  void sendAction() async {
    setState(() => isSending = true);
    final body =
        'https://www.openstreetmap.org/?mlat=${position!.latitude}&mlon=${position!.longitude}#map=16/${position!.latitude}/${position!.longitude}';
    final uri =
        'geo:${position!.latitude},${position!.longitude};u=${position!.accuracy}';
    await showFutureLoadingDialog(
      context: context,
      future: () => widget.room.sendLocation(body, uri),
    );
    Navigator.of(context, rootNavigator: false).pop(true);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: BackButton(
          onPressed: () => Navigator.of(context, rootNavigator: true).pop(),
          color: Colors.black,
        ),
        backgroundColor: Colors.transparent,
      ),
      backgroundColor: context.theme.colors.surface,
      extendBodyBehindAppBar: true,
      body: Builder(
        builder: (context) {
          if (position != null) {
            return SizedBox(
              width: context.width,
              height: context.height,
              child: MapWidget(
                latitude: position!.latitude,
                longitude: position!.longitude,
              ),
            );
          } else if (disabled) {
            return NomoText(
                'Location of your Device is turned off. Turn it on to share your location.');
          } else if (denied) {
            return NomoText('Cannot access your location');
          } else if (error != null) {
            return NomoText(error.toString());
          } else {
            return Center(child: CircularProgressIndicator.adaptive());
          }
        },
      ),
      extendBody: true,
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.only(bottom: 20, right: 20, left: 20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Builder(
              builder: (context) {
                if (disabled) {
                  return PrimaryNomoButton(
                    text: 'Open Settings',
                    width: double.infinity,
                    onPressed: () => Geolocator.openLocationSettings(),
                  );
                } else {
                  return PrimaryNomoButton(
                    text: 'Send',
                    enabled: isSending == false,
                    width: double.infinity,
                    onPressed: isSending ? null : sendAction,
                  );
                }
              },
            ),
          ],
        ),
      ),
    );
  }
}
