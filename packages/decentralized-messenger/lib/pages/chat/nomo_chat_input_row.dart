// ignore_for_file: avoid_print, use_build_context_synchronously

import 'package:decentralized_messenger/main.dart';
import 'package:decentralized_messenger/pages/chat/nomo_pop_up_menu_divider.dart';
import 'package:decentralized_messenger/pages/nomo_widget/nomo_camera.dart';
import 'package:decentralized_messenger/utils/nomo_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:animations/animations.dart';
import 'package:flutter_gen/gen_l10n/l10n.dart';
import 'package:keyboard_shortcuts/keyboard_shortcuts.dart';
import 'package:matrix/matrix.dart';

import 'package:decentralized_messenger/config/app_config.dart';
import 'package:decentralized_messenger/utils/platform_infos.dart';
import 'package:decentralized_messenger/widgets/avatar.dart';
import 'package:decentralized_messenger/widgets/nomo_matrix.dart';
import '../../config/themes.dart';
import 'chat.dart';
import 'input_bar.dart';

class NomoChatInputRow extends StatelessWidget {
  final ChatController controller;

  const NomoChatInputRow(this.controller, {super.key});

  @override
  Widget build(BuildContext context) {
    if (controller.showEmojiPicker &&
        controller.emojiPickerType == EmojiPickerType.reaction) {
      return const SizedBox.shrink();
    }
    final bottomSheetPadding = NomoThemes.isColumnMode(context) ? 16.0 : 8.0;
    return Padding(
      padding: EdgeInsets.symmetric(
          horizontal: 2 * bottomSheetPadding, vertical: bottomSheetPadding),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: controller.selectMode
            ? <Widget>[
                if (controller.selectedEvents
                    .every((event) => event.status == EventStatus.error))
                  SizedBox(
                    height: 56,
                    child: TextButton(
                      style: TextButton.styleFrom(
                        foregroundColor: Theme.of(context).colorScheme.error,
                      ),
                      onPressed: controller.deleteErrorEventsAction,
                      child: Row(
                        children: <Widget>[
                          const Icon(Icons.delete),
                          Text(L10n.of(context)!.delete),
                        ],
                      ),
                    ),
                  ),
                controller.selectedEvents.length == 1 &&
                        !controller.selectedEvents.first
                            .getDisplayEvent(controller.timeline!)
                            .status
                            .isSent
                    ? SizedBox(
                        height: 56,
                        child: TextButton(
                          onPressed: controller.sendAgainAction,
                          child: Row(
                            children: <Widget>[
                              Text(L10n.of(context)!.tryToSendAgain),
                              const SizedBox(width: 4),
                              const Icon(Icons.send_outlined, size: 16),
                            ],
                          ),
                        ),
                      )
                    : const SizedBox.shrink(),
              ]
            : <Widget>[
                InputActionButton(controller: controller),
                const SizedBox(
                  width: 7.5,
                ),
                if (NomoMatrix.I.isMultiAccount &&
                    NomoMatrix.I.hasComplexBundles &&
                    NomoMatrix.I.currentBundle!.length > 1)
                  Container(
                    height: 56,
                    alignment: Alignment.center,
                    child: _ChatAccountPicker(controller),
                  ),
                Expanded(
                  child: InputBar(
                    room: controller.room,
                    minLines: 1,
                    maxLines: 5,
                    autofocus: !PlatformInfos.isMobile,
                    keyboardType: TextInputType.multiline,
                    textInputAction:
                        AppConfig.sendOnEnter == true && PlatformInfos.isMobile
                            ? TextInputAction.send
                            : null,
                    onSubmitted: controller.onInputBarSubmitted,
                    onSubmitImage: controller.sendImageFromClipBoard,
                    focusNode: controller.inputFocus,
                    controller: controller.sendController,
                    decoration: InputDecoration(
                        hintText: L10n.of(context)!.writeAMessage,
                        hintStyle: TextStyle(
                          color: Theme.of(context)
                              .colorScheme
                              .onSurface
                              .withOpacity(.5),
                          fontWeight: FontWeight.normal,
                        ),
                        fillColor:
                            context.theme.colorScheme.surfaceContainerHighest,
                        hintMaxLines: 1,
                        contentPadding: const EdgeInsets.symmetric(
                            horizontal: 15, vertical: 4),
                        border: OutlineInputBorder(
                            borderSide: BorderSide.none,
                            borderRadius: BorderRadius.circular(
                                2 * AppConfig.borderRadius)),
                        enabledBorder: OutlineInputBorder(
                            borderSide: BorderSide.none,
                            borderRadius: BorderRadius.circular(
                                2 * AppConfig.borderRadius)),
                        disabledBorder: OutlineInputBorder(
                            borderSide: BorderSide.none,
                            borderRadius: BorderRadius.circular(
                                2 * AppConfig.borderRadius)),
                        filled: true,
                        prefixIcon: PlatformInfos.isDesktop
                            ? KeyBoardShortcuts(
                                keysToPress: {
                                  LogicalKeyboardKey.altLeft,
                                  LogicalKeyboardKey.keyE,
                                },
                                onKeysPressed: controller.emojiPickerAction,
                                helpLabel: L10n.of(context)!.emojis,
                                child: IconButton(
                                  tooltip: L10n.of(context)!.emojis,
                                  icon: PageTransitionSwitcher(
                                    transitionBuilder: (
                                      Widget child,
                                      Animation<double> primaryAnimation,
                                      Animation<double> secondaryAnimation,
                                    ) {
                                      return SharedAxisTransition(
                                        animation: primaryAnimation,
                                        secondaryAnimation: secondaryAnimation,
                                        transitionType:
                                            SharedAxisTransitionType.scaled,
                                        fillColor: Colors.transparent,
                                        child: child,
                                      );
                                    },
                                    child: Icon(
                                      controller.showEmojiPicker
                                          ? Icons.keyboard
                                          : Icons.emoji_emotions_outlined,
                                      key: ValueKey(controller.showEmojiPicker),
                                    ),
                                  ),
                                  onPressed: controller.emojiPickerAction,
                                ),
                              )
                            : null,
                        suffixIcon: Row(
                          mainAxisAlignment:
                              MainAxisAlignment.spaceBetween, // added line
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            if (!PlatformInfos.isMobile ||
                                controller.sendController.text.isNotEmpty) ...[
                              IconButton(
                                icon: const Icon(
                                  Icons.send_rounded,
                                  size: 25,
                                ),
                                onPressed: controller.send,
                                tooltip: L10n.of(context)!.send,
                              ),
                            ] else
                              IconButton(
                                icon: const Icon(
                                  Icons.camera_alt_outlined,
                                  size: 25,
                                ),
                                onPressed: () {
                                  Navigator.of(context, rootNavigator: true)
                                      .push(MaterialPageRoute(
                                    builder: (context) => NomoCameraPage(
                                      room: controller.room,
                                    ),
                                  ));
                                },
                                tooltip: L10n.of(context)!.send,
                              ),
                            if (PlatformInfos.platformCanRecord &&
                                controller.sendController.text.isEmpty)
                              IconButton(
                                tooltip: L10n.of(context)!.voiceMessage,
                                icon: const Icon(
                                  Icons.mic_none_rounded,
                                  size: 25,
                                ),
                                onPressed: controller.voiceMessageAction,
                              ),
                          ],
                        )),
                    onChanged: controller.onInputBarChanged,
                  ),
                ),
              ],
      ),
    );
  }
}

class InputActionButton extends StatelessWidget {
  const InputActionButton({
    super.key,
    required this.controller,
  });

  final ChatController controller;

  @override
  Widget build(BuildContext context) {
    return KeyBoardShortcuts(
      keysToPress: {
        LogicalKeyboardKey.altLeft,
        LogicalKeyboardKey.keyA,
      },
      onKeysPressed: () => controller.onAddPopupMenuButtonSelected('file'),
      helpLabel: L10n.of(context)!.sendFile,
      child: Container(
        height: 45,
        width: 45,
        alignment: Alignment.center,
        clipBehavior: Clip.hardEdge,
        decoration: BoxDecoration(
            shape: BoxShape.circle, color: context.theme.colorScheme.primary),
        child: PopupMenuButton<String>(
          icon: Icon(
            Icons.add_outlined,
            color: context.theme.colorScheme.onPrimary,
            size: 25,
          ),
          enableFeedback: true,
          clipBehavior: Clip.hardEdge,
          elevation: 1,
          surfaceTintColor: context.theme.colorScheme.surface,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConfig.borderRadius),
          ),
          padding: EdgeInsets.zero,
          color: context.theme.colorScheme.surface,
          onSelected: controller.onAddPopupMenuButtonSelected,
          onOpened: () {},
          itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
            PopupMenuItem<String>(
                value: 'file',
                padding: EdgeInsets.zero,
                child: NomoPopUpMenuitem(
                    icon: const Icon(
                      Icons.attachment_outlined,
                      size: 28,
                    ),
                    title: L10n.of(context)!.sendFile)),
            NomoPopupMenuDivider(
              height: 1,
              color: context.theme.colorScheme.onSurface.withOpacity(.2),
            ),
            PopupMenuItem<String>(
                value: 'camera',
                padding: EdgeInsets.zero,
                child: NomoPopUpMenuitem(
                    icon: const Icon(
                      Icons.camera_alt_outlined,
                      size: 28,
                    ),
                    title: L10n.of(context)!.openCamera)),
            NomoPopupMenuDivider(
              height: 1,
              color: context.theme.colorScheme.onSurface.withOpacity(.2),
            ),
            PopupMenuItem<String>(
                value: 'image',
                padding: EdgeInsets.zero,
                child: NomoPopUpMenuitem(
                    icon: const Icon(
                      Icons.image_outlined,
                      size: 28,
                    ),
                    title: L10n.of(context)!.sendImage)),
            if (SEND_ASSETS_ENABLED && controller.room.isDirectChat) ...[
              NomoPopupMenuDivider(
                height: 1,
                color: context.theme.colorScheme.onSurface.withOpacity(.2),
              ),
              PopupMenuItem<String>(
                  value: 'send-assets',
                  padding: EdgeInsets.zero,
                  child: NomoPopUpMenuitem(
                      icon: Image.asset(
                        'assets/icons/send_asset.png',
                        width: 22,
                        height: 22,
                        color: context.theme.colorScheme.onSurface,
                      ),
                      title: 'Send Assets')),
            ],
            NomoPopupMenuDivider(
              height: 1,
              color: context.theme.colorScheme.onSurface.withOpacity(.2),
            ),
            PopupMenuItem(
                value: 'send-webon',
                padding: EdgeInsets.zero,
                child: NomoPopUpMenuitem(
                    icon: Image.asset(
                      'assets/icons/add_webon.png',
                      color: context.theme.colorScheme.onSurface,
                      width: 28,
                      height: 28,
                    ),
                    showDivider: PlatformInfos.isMobile,
                    title: 'Send Webon')),
            if (PlatformInfos.isMobile) ...[
              NomoPopupMenuDivider(
                height: 1,
                color: context.theme.colorScheme.onSurface.withOpacity(.2),
              ),
              PopupMenuItem<String>(
                  value: 'location',
                  padding: EdgeInsets.zero,
                  child: NomoPopUpMenuitem(
                      icon: const Icon(
                        Icons.gps_fixed_outlined,
                        size: 28,
                      ),
                      showDivider: false,
                      title: L10n.of(context)!.shareLocation)),
            ]
          ],
        ),
      ),
    );
  }
}

class NomoPopUpMenuitem extends StatelessWidget {
  const NomoPopUpMenuitem(
      {super.key,
      required this.icon,
      required this.title,
      this.showDivider = true});

  final Widget icon;
  final String title;
  final bool showDivider;

  @override
  Widget build(BuildContext context) {
    final row = Container(
      height: 40,
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          icon,
          const SizedBox(width: 12),
          Text(
            title,
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.normal),
          ),
        ],
      ),
    );

    return row;
  }
}

class _ChatAccountPicker extends StatelessWidget {
  final ChatController controller;

  const _ChatAccountPicker(this.controller);

  void _popupMenuButtonSelected(String mxid, BuildContext context) {
    final client = NomoMatrix.I.currentBundle!
        .firstWhere((cl) => cl!.userID == mxid, orElse: () => null);
    if (client == null) {
      Logs().w('Attempted to switch to a non-existing client $mxid');
      return;
    }
    controller.setSendingClient(client);
  }

  @override
  Widget build(BuildContext context) {
    final clients = controller.currentRoomBundle;
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: FutureBuilder<Profile>(
        future: controller.sendingClient.fetchOwnProfile(),
        builder: (context, snapshot) => PopupMenuButton<String>(
          onSelected: (mxid) => _popupMenuButtonSelected(mxid, context),
          itemBuilder: (BuildContext context) => clients
              .map(
                (client) => PopupMenuItem<String>(
                  value: client!.userID,
                  child: FutureBuilder<Profile>(
                    future: client.fetchOwnProfile(),
                    builder: (context, snapshot) => ListTile(
                      leading: Avatar(
                        mxContent: snapshot.data?.avatarUrl,
                        name: snapshot.data?.displayName ??
                            client.userID!.localpart,
                        size: 20,
                      ),
                      title: Text(snapshot.data?.displayName ?? client.userID!),
                      contentPadding: const EdgeInsets.all(0),
                    ),
                  ),
                ),
              )
              .toList(),
          child: Avatar(
            mxContent: snapshot.data?.avatarUrl,
            name: snapshot.data?.displayName ??
                NomoMatrix.I.client.userID!.localpart,
            size: 20,
          ),
        ),
      ),
    );
  }
}
