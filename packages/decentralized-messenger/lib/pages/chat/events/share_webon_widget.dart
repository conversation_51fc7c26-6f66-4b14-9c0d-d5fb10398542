import 'package:decentralized_messenger/config/app_config.dart';
import 'package:decentralized_messenger/pages/nomo_provider/login_provider.dart';
import 'package:flutter/services.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:nomo_app/features/webons/manifest/nomo_manifest.dart';
import 'package:nomo_app/features/webons/webon_icon.dart';
import 'package:nomo_ui_kit/components/text/nomo_text.dart';

import 'package:flutter/material.dart';
import 'package:nomo_ui_kit/theme/nomo_theme.dart';
import 'package:flutter_gen/gen_l10n/l10n.dart';

class ShareWebonModel {
  final String nomo_manifest_version;
  final String webon_id;
  final String webon_name;
  final String webon_version;
  final String webon_url;
  final String deeplink;
  final List<String> permissions;
  final List<String> dependencies;

  const ShareWebonModel({
    required this.nomo_manifest_version,
    required this.webon_id,
    required this.webon_name,
    required this.webon_version,
    required this.webon_url,
    required this.deeplink,
    required this.permissions,
    required this.dependencies,
  });

  factory ShareWebonModel.fromJson(Map<String, dynamic> json) =>
      ShareWebonModel(
        nomo_manifest_version: json['nomo_manifest_version'],
        webon_id: json['webon_id'],
        webon_name: json['webon_name'],
        webon_version: json['webon_version'],
        webon_url: json['webon_url'],
        deeplink: json['deeplink'],
        permissions: List<String>.from(json['permissions'] ?? []),
        dependencies: List<String>.from(json['dependencies'] ?? []),
      );

  Map<String, dynamic> toJson() => {
        'nomo_manifest_version': nomo_manifest_version,
        'webon_id': webon_id,
        'webon_name': webon_name,
        'webon_version': webon_version,
        'webon_url': webon_url,
        'deeplink': deeplink,
        'permissions': permissions,
        'dependencies': dependencies,
      };
}

class ShareWebonWidget extends ConsumerWidget {
  const ShareWebonWidget({
    super.key,
    required this.shareWebonModel,
    required this.isMyMessage,
  });

  final ShareWebonModel shareWebonModel;
  final bool isMyMessage;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isInstalled = nomoInterface
        .getInstalledWebOns()
        .any((e) => e['webon_id'] == shareWebonModel.webon_id);
    final foregroundColor = isMyMessage
        ? context.theme.colors.onPrimary
        : context.theme.colors.foreground1;
    return Padding(
      padding: const EdgeInsets.only(top: 5),
      child: Row(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(AppConfig.borderRadius),
            child: Container(
              color: context.theme.colors.primary,
              width: 56,
              height: 56,
              child: WebOnIcon(
                webOn: NomoManifest(
                  nomo_manifest_version: shareWebonModel.nomo_manifest_version,
                  webon_id: shareWebonModel.webon_id,
                  webon_name: shareWebonModel.webon_name,
                  webon_version: shareWebonModel.webon_version,
                  dependencies: shareWebonModel.dependencies,
                  permissions: shareWebonModel.permissions,
                  webon_url: shareWebonModel.webon_url,
                ),
              ),
            ),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 2),
                NomoText(
                  shareWebonModel.webon_name,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: foregroundColor),
                ),
                SizedBox(
                  height: 20,
                  child: TextButton.icon(
                    onPressed: () {
                      Clipboard.setData(
                          ClipboardData(text: shareWebonModel.deeplink));
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                            L10n.of(context)!.copiedToClipboard,
                          ),
                        ),
                      );
                    },
                    style: ButtonStyle(
                      padding: WidgetStatePropertyAll(EdgeInsets.zero),
                    ),
                    icon: Icon(
                      Icons.copy,
                      size: 12,
                      color: foregroundColor,
                    ),
                    label: NomoText(
                      'Copy Link',
                      style: TextStyle(fontSize: 12, color: foregroundColor),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 15),
          ActionChip(
              label: Text(
                isInstalled ? 'Open' : 'Install',
                style: TextStyle(
                    fontSize: 12, color: context.theme.colors.primary),
              ),
              shape: RoundedRectangleBorder(
                  side: BorderSide(color: context.theme.colors.primary),
                  borderRadius: BorderRadius.circular(7)),
              backgroundColor: context.theme.colors.background1,
              onPressed: () {
                nomoInterface.installWebOn(shareWebonModel.deeplink);
              })
        ],
      ),
    );
  }
}
