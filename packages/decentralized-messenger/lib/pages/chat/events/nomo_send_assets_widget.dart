import 'package:decentralized_messenger/config/app_config.dart';
import 'package:decentralized_messenger/pages/nomo_provider/login_provider.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:nomo_app/common/blockchain/network/network.dart';
import 'package:nomo_app/common/blockchain/network/wallet_info.dart';
import 'package:nomo_app/common/extension/string.dart';
import 'package:nomo_app/features/custom_token/add_token/custom_token_provider.dart';
import 'package:nomo_app/features/custom_token/manage_tokens/manage_token_provider.dart';
import 'package:nomo_ui_kit/theme/nomo_theme.dart';
import 'package:walletkit_dart/walletkit_dart.dart';

// import 'package:webon_kit_dart/webon_kit_dart.dart';

class NomoSendAssetsWidget extends ConsumerWidget {
  const NomoSendAssetsWidget({
    super.key,
    required this.message,
    required this.ownMessage,
  });
  final NomoSendAssetsMessageModel message;
  final bool ownMessage;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final assetsProvider = ref.watch(manageAssetsProvider(WalletInfo.I.id));
    final isTokenAdded =
        assetsProvider.assets.any((e) => e.symbol == message.symbol);
    final actionChipLabel = isTokenAdded ? 'show'.tr : 'add'.tr;
    final subtitleText =
        ownMessage ? "has_been_sent".tr : "has_been_received".tr;
    final borderRadius = BorderRadius.circular(AppConfig.borderRadius);
    final name =
        NomoTheme.of(context).colorTheme.key.value.toString().toLowerCase();
    return DecoratedBox(
      decoration: BoxDecoration(
        color: context.theme.colors.background1,
        borderRadius: borderRadius,
        boxShadow: const [
          BoxShadow(
            color: Color(0x33000000),
            blurRadius: 5,
            spreadRadius: 0.25,
            blurStyle: BlurStyle.normal,
            offset: Offset(0, 0),
          ),
        ],
      ),
      child: SizedBox(
        height: 65,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
          child: Row(
            children: [
              SizedBox(
                height: 42.5,
                width: 42.5,
                child: Image.asset(
                  'assets/images/$name/send_ic.png',
                  fit: BoxFit.contain,
                  color: context.theme.colors.primary,
                ),
              ),
              const SizedBox(
                width: 10,
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${message.amount} ${message.symbol}',
                      maxLines: 1,
                      style: TextStyle(
                        fontSize: 17,
                        color: context.theme.colors.foreground1,
                        overflow: TextOverflow.ellipsis,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      subtitleText,
                      style: TextStyle(
                        fontSize: 15,
                        color: context.theme.colors.foreground1.withOpacity(.5),
                        overflow: TextOverflow.ellipsis,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(
                width: 15,
              ),
              ActionChip(
                label: Text(
                  actionChipLabel,
                  style: TextStyle(
                    fontSize: 12,
                    color: context.theme.colors.primary,
                  ),
                ),
                shape: RoundedRectangleBorder(
                  side: BorderSide(color: context.theme.colors.primary),
                  borderRadius: BorderRadius.circular(7),
                ),
                backgroundColor: context.theme.colors.background1,
                onPressed: () async {
                  if (isTokenAdded) {
                    nomoInterface.navigateToWallet(
                      message.symbol,
                      message.network,
                    );
                  } else {
                    final networkName = message.network;
                    if (networkName == null) {
                      return;
                    }
                    final web3Network =
                        Network.getNetworkTypeFromName(networkName);
                    if (web3Network == null) {
                      return;
                    }
                    final network = Network.getNetwork(web3Network);
                    final contractAddress = message.contractAddress;
                    if (contractAddress == null ||
                        contractAddress.isEmpty ||
                        network == null) {
                      return;
                    }
                    final evmNetwork = network.asEVM;
                    if (evmNetwork == null) {
                      return;
                    }
                    final tokenInfo = await getTokenInfo(
                      contractAddress: contractAddress,
                      rpc: evmNetwork.rpcInterface,
                    );
                    if (tokenInfo == null) {
                      return;
                    }
                    await addToken(
                      // ignore: use_build_context_synchronously
                      context: context,
                      info: tokenInfo,
                      network: web3Network,
                    );
                  }
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class NomoSendAssetsMessageModel {
  final String amount;
  final String? receiver;
  final String symbol;
  final String? network;
  final String? tokenName;
  final String txHash;
  final String? contractAddress;

  NomoSendAssetsMessageModel({
    required this.amount,
    required this.tokenName,
    required this.receiver,
    required this.symbol,
    this.network,
    this.contractAddress,
    required this.txHash,
  });

  Map<String, dynamic> toJson() {
    return {
      'amount': amount,
      'tokenName': tokenName,
      'receiver': receiver,
      'symbol': symbol,
      'txHash': txHash,
      'network': network,
      'contractAddress': contractAddress,
    };
  }

  factory NomoSendAssetsMessageModel.fromJson(Map<String, dynamic> json) {
    return NomoSendAssetsMessageModel(
      amount: json['amount'],
      tokenName: json['tokenName'],
      receiver: json['receiver'],
      symbol: json['symbol'],
      txHash: json['txHash'],
      network: json['network'],
      contractAddress: json['contractAddress'],
    );
  }
}
