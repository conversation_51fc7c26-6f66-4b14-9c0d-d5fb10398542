import 'dart:convert';

import 'package:decentralized_messenger/pages/chat/events/map_bubble.dart';
import 'package:decentralized_messenger/pages/chat/events/nomo_send_assets_widget.dart';
import 'package:decentralized_messenger/pages/chat/events/share_webon_widget.dart';
import 'package:decentralized_messenger/pages/device_settings/device_cleanup.dart';
import 'package:decentralized_messenger/pages/nomo_provider/login_provider.dart';
import 'package:decentralized_messenger/pages/nomo_utils/nomo_extensions.dart';
import 'package:flutter/material.dart';

import 'package:flutter_gen/gen_l10n/l10n.dart';
import 'package:flutter_linkify/flutter_linkify.dart';
import 'package:matrix/matrix.dart';

import 'package:decentralized_messenger/pages/chat/events/video_player.dart';
import 'package:decentralized_messenger/utils/adaptive_bottom_sheet.dart';
import 'package:decentralized_messenger/utils/date_time_extension.dart';
import 'package:decentralized_messenger/utils/matrix_sdk_extensions/matrix_locals.dart';
import 'package:decentralized_messenger/widgets/avatar.dart';
import 'package:decentralized_messenger/widgets/nomo_matrix.dart';
import 'package:nomo_app/features/webons/dev_mode/dev_mode.dart';
import 'package:nomo_app/features/webons/webon_lifecycle/install_webon.dart';
import 'package:nomo_app/features/scanner/code_handler/chat_code_handler.dart';
import 'package:nomo_app/main.dart';
import 'package:nomo_ui_kit/components/text/nomo_text.dart';
import 'package:url_launcher/url_launcher_string.dart';
import '../../../config/app_config.dart';
import '../../../utils/platform_infos.dart';
import '../../bootstrap/bootstrap_dialog.dart';
import 'audio_player.dart';
import 'cute_events.dart';
import 'html_message.dart';
import 'image_bubble.dart';
import 'message_download_content.dart';
import 'sticker.dart';

class MessageContent extends StatelessWidget {
  final Event event;
  final Color textColor;
  final void Function(Event)? onInfoTab;
  final BorderRadius borderRadius;

  const MessageContent(
    this.event, {
    this.onInfoTab,
    super.key,
    required this.textColor,
    required this.borderRadius,
  });

  void _verifyOrRequestKey(BuildContext context) async {
    final l10n = L10n.of(context)!;
    if (event.content['can_request_session'] != true) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            event.type == EventTypes.Encrypted
                ? l10n.needPantalaimonWarning
                : event.calcLocalizedBodyFallback(
                    MatrixLocals(l10n),
                  ),
          ),
        ),
      );
      return;
    }
    final client = NomoMatrix.I.client;

    if (client.isUnknownSession && client.encryption!.crossSigning.enabled) {
      final success = await BootstrapDialog(
        client: NomoMatrix.I.client,
      ).show(context);
      if (success != true) return;
    }
    event.requestKey();
    final sender = event.senderFromMemoryOrFallback;
    final devMode = $ref.read(devModeProvider) != null;
    await showAdaptiveBottomSheet(
      context: context,
      builder: (context) => Scaffold(
        appBar: AppBar(
          leading: CloseButton(onPressed: Navigator.of(context).pop),
          title: Text(
            l10n.whyIsThisMessageEncrypted,
            style: const TextStyle(fontSize: 16),
          ),
        ),
        body: SafeArea(
          child: ListView(
            padding: const EdgeInsets.all(16),
            children: [
              ListTile(
                contentPadding: EdgeInsets.zero,
                leading: Avatar(
                  mxContent: sender.avatarUrl,
                  name: sender.calcDisplayname(),
                  presenceUserId: sender.stateKey,
                  client: event.room.client,
                ),
                title: Text(sender.calcDisplayname()),
                subtitle: Text(event.originServerTs.localizedTime(context)),
                trailing: const Icon(Icons.lock_outlined),
              ),
              const Divider(),
              if(devMode) SelectableText(jsonEncode(event.content)),
              Text(
                event.calcLocalizedBodyFallback(
                  MatrixLocals(l10n),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final fontSize = AppConfig.messageFontSize * AppConfig.fontSizeFactor;
    final buttonTextColor = textColor;
    switch (event.type) {
      case EventTypes.Message:
      case EventTypes.Encrypted:
      case EventTypes.Sticker:
        switch (event.messageType) {
          case MessageTypes.Image:
            return ImageBubble(
              event,
              width: 400,
              height: 300,
              fit: BoxFit.cover,
              borderRadius: borderRadius,
            );
          case MessageTypes.Sticker:
            if (event.redacted) continue textmessage;
            return Sticker(event);
          case CuteEventContent.eventType:
            return CuteContent(event);
          case MessageTypes.Audio:
            if (PlatformInfos.isMobile ||
                    PlatformInfos.isMacOS ||
                    PlatformInfos.isWeb
                // Disabled until https://github.com/bleonard252/just_audio_mpv/issues/3
                // is fixed
                //   || PlatformInfos.isLinux
                ) {
              return AudioPlayerWidget(
                event,
                color: textColor,
              );
            }
            return MessageDownloadContent(event, textColor);
          case MessageTypes.Video:
            if (PlatformInfos.isMobile || PlatformInfos.isWeb) {
              return EventVideoPlayer(
                event,
                borderRadius: borderRadius.copyWith(
                  bottomLeft: const Radius.circular(0),
                  bottomRight: const Radius.circular(0),
                ),
              );
            }
            return MessageDownloadContent(event, textColor);
          case MessageTypes.File:
            return MessageDownloadContent(event, textColor);

          // case MessageTypes.Text:
          case MessageTypes.Notice:
          case MessageTypes.Emote:
            if (AppConfig.renderHtml &&
                !event.redacted &&
                event.isRichMessage) {
              var html = event.formattedText;
              if (event.messageType == MessageTypes.Emote) {
                html = '* $html';
              }
              return HtmlMessage(
                html: html,
                textColor: textColor,
                room: event.room,
              );
            }
            // else we fall through to the normal message rendering
            continue textmessage;
          case MessageTypes.BadEncrypted:
            return Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _ButtonContent(
                  textColor: buttonTextColor,
                  onPressed: () => _verifyOrRequestKey(context),
                  icon: '🔒',
                  label: L10n.of(context)!.encrypted,
                  fontSize: fontSize,
                ),
                const SizedBox(height: 8),
                TextButton.icon(
                  icon: const Icon(Icons.refresh, size: 16),
                  label: Text(
                    "Request keys again",
                    style: TextStyle(fontSize: fontSize * 0.8),
                  ),
                  onPressed: () async {
                    // Request keys for this room with a more aggressive approach
                    if (event.roomId != null) {
                      await DeviceCleanup.requestKeysForRoom(event.roomId!);
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text("Keys requested for this room")),
                      );
                    }
                  },
                ),
              ],
            );
          case EventTypes.Encrypted:
            return _ButtonContent(
              textColor: buttonTextColor,
              onPressed: () => _verifyOrRequestKey(context),
              icon: '🔒',
              label: L10n.of(context)!.encrypted,
              fontSize: fontSize,
            );
          case MessageTypes.Location:
            final geoUri =
                Uri.tryParse(event.content.tryGet<String>('geo_uri')!);
            if (geoUri != null && geoUri.scheme == 'geo') {
              final latlong = geoUri.path
                  .split(';')
                  .first
                  .split(',')
                  .map((s) => double.tryParse(s))
                  .toList();
              if (latlong.length == 2 &&
                  latlong.first != null &&
                  latlong.last != null) {
                return MapBubble(
                  latitude: latlong.first!,
                  longitude: latlong.last!,
                );
              }
            }
            continue textmessage;
          case MessageTypes.None:
          textmessage:
          default:
            if (event.redacted) {
              return NomoText(
                'deleted_message',
                fontSize: fontSize,
                style: TextStyle(fontStyle: FontStyle.italic, color: textColor),
                opacity: .75,
              );
            }
            if (event.isNomoSendAssetMessage) {
              final message = event.getNomoSendAssetMessage;
              return NomoSendAssetsWidget(
                message: message!,
                ownMessage: event.senderId == NomoMatrix.I.client.userID,
              );
            }
            if (event.isShareWebonMessage) {
              final message = event.getShareWebonMessage;
              return ShareWebonWidget(
                shareWebonModel: message!,
                isMyMessage: event.senderId == NomoMatrix.I.client.userID,
              );
            }
            final bigEmotes = event.onlyEmotes &&
                event.numberEmotes > 0 &&
                event.numberEmotes <= 10;
            return FutureBuilder<String>(
              future: event.calcLocalizedBody(
                MatrixLocals(L10n.of(context)!),
                hideReply: true,
              ),
              builder: (context, snapshot) {
                return Linkify(
                  text: snapshot.data ??
                      event.calcLocalizedBodyFallback(
                        MatrixLocals(L10n.of(context)!),
                        hideReply: true,
                      ),
                  style: TextStyle(
                    color: textColor,
                    fontSize: bigEmotes ? fontSize * 3 : fontSize,
                    decoration:
                        event.redacted ? TextDecoration.lineThrough : null,
                  ),
                  options: const LinkifyOptions(humanize: false),
                  linkStyle: TextStyle(
                    color: textColor.withAlpha(150),
                    fontSize: bigEmotes ? fontSize * 3 : fontSize,
                    decoration: TextDecoration.underline,
                    decorationColor: textColor.withAlpha(150),
                  ),
                  onOpen: (url) async {
                    final link = url.url;

                    if (isWebOnDeeplink(link)) {
                      nomoInterface.installWebOn(link);
                    } else if (ChatCodeHandler.isChatCode(link)) {
                      ChatCodeHandler.handle(link);
                    } else {
                      launchUrlString(link,
                          mode: LaunchMode.externalApplication);
                    }
                  },
                );
              },
            );
        }
      case EventTypes.CallInvite:
        return FutureBuilder<User?>(
          future: event.fetchSenderUser(),
          builder: (context, snapshot) {
            return _ButtonContent(
              label: L10n.of(context)!.startedACall(
                snapshot.data?.calcDisplayname() ??
                    event.senderFromMemoryOrFallback.calcDisplayname(),
              ),
              icon: '📞',
              textColor: buttonTextColor,
              onPressed: () => onInfoTab!(event),
              fontSize: fontSize,
            );
          },
        );
      default:
        return FutureBuilder<User?>(
          future: event.fetchSenderUser(),
          builder: (context, snapshot) {
            return _ButtonContent(
              label: L10n.of(context)!.userSentUnknownEvent(
                snapshot.data?.calcDisplayname() ??
                    event.senderFromMemoryOrFallback.calcDisplayname(),
                event.type,
              ),
              icon: 'ℹ️',
              textColor: buttonTextColor,
              onPressed: () => onInfoTab!(event),
              fontSize: fontSize,
            );
          },
        );
    }
  }
}

class _ButtonContent extends StatelessWidget {
  final void Function() onPressed;
  final String label;
  final String icon;
  final Color? textColor;
  final double fontSize;

  const _ButtonContent({
    required this.label,
    required this.icon,
    required this.textColor,
    required this.onPressed,
    required this.fontSize,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPressed,
      child: Text(
        '$icon  $label',
        style: TextStyle(
          color: textColor,
          fontSize: fontSize,
        ),
      ),
    );
  }
}
