import 'package:decentralized_messenger/pages/chat/chat.dart';
import 'package:decentralized_messenger/pages/chat/edit_chat_message_screen.dart';
import 'package:decentralized_messenger/pages/chat/events/nomo_reply_content.dart';
import 'package:decentralized_messenger/pages/chat/events/swipe_to_reply.dart';
import 'package:decentralized_messenger/pages/nomo_utils/nomo_extensions.dart';
import 'package:decentralized_messenger/utils/room_status_extension.dart';
import 'package:decentralized_messenger/utils/url_launcher.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:matrix/matrix.dart';

import 'package:decentralized_messenger/utils/date_time_extension.dart';
import 'package:decentralized_messenger/utils/string_color.dart';
import 'package:decentralized_messenger/widgets/avatar.dart';
import 'package:decentralized_messenger/widgets/hover_builder.dart';
import 'package:decentralized_messenger/widgets/nomo_matrix.dart';
import '../../../config/app_config.dart';
import 'message_content.dart';
import 'message_reactions.dart';
import 'state_message.dart';
import 'verification_request_content.dart';

class NomoMessage extends StatelessWidget {
  final Event event;
  final Event? nextEvent;
  final Event? previousEvent;
  final bool displayReadMarker;
  final void Function(Event) onSelect;
  final void Function(Event) onAvatarTab;
  final void Function(Event) onInfoTab;
  final void Function(String) scrollToEventId;
  final void Function() onSwipe;
  final bool longPressSelect;
  final bool selected;
  final Timeline timeline;
  final bool highlightMarker;
  final bool animateIn;
  final void Function()? resetAnimateIn;
  final Color? avatarPresenceBackgroundColor;
  final bool isDirectChat;
  final bool isReaction;
  final ChatController controller;
  final bool showSenderName;

  const NomoMessage(
    this.event, {
    this.nextEvent,
    this.previousEvent,
    this.showSenderName = true,
    this.displayReadMarker = false,
    this.longPressSelect = false,
    required this.onSelect,
    required this.onInfoTab,
    required this.onAvatarTab,
    required this.scrollToEventId,
    required this.onSwipe,
    this.selected = false,
    required this.timeline,
    this.highlightMarker = false,
    this.animateIn = false,
    this.resetAnimateIn,
    this.avatarPresenceBackgroundColor,
    required this.isDirectChat,
    required this.controller,
    this.isReaction = false,
    super.key,
  });

  BorderRadius getBorderRadius(bool ownMessage) {
    return BorderRadius.circular(AppConfig.borderRadius);
  }

  bool checkIfEventIsStateMessage(Event event) {
/*     Logs().v('Checking event type: ${event.type}');
    Logs().v('Original type: ${event.originalSource?.type}'); */

    if (event.originalSource?.type == EventTypes.Encrypted) {
      return false;
    }
    return !{
      EventTypes.Message,
      EventTypes.Sticker,
      // EventTypes.Encrypted,
      // EventTypes.CallInvite,
      // EventTypes.CallHangup,
    }.contains(event.type);
  }

  bool checkIfEventHasNoBubble(Event event) {
    return !{
      MessageTypes.Sticker,
      MessageTypes.Image,
      MessageTypes.Video,
    }.contains(event.messageType);
  }

  bool checkIfEventIsSendAssetsWidget(Event event) {
    return event.isNomoSendAssetMessage;
  }

  Widget getLeadingWidget(
      {required bool sameSender, required bool ownMessage}) {
    if (isDirectChat) {
      return const SizedBox();
    }
    if (sameSender || ownMessage) {
      return Padding(
        padding: const EdgeInsets.only(right: 8.0),
        child: SizedBox(
          width: 30,
          child: Center(
            child: SizedBox(
              width: 16,
              height: 16,
              child: event.fileSendingStatus != null
                  ? const CircularProgressIndicator.adaptive(
                      strokeWidth: 1,
                    )
                  : null,
            ),
          ),
        ),
      );
    } else {
      return Padding(
        padding: const EdgeInsets.only(right: 8.0),
        child: FutureBuilder<User?>(
          future: event.fetchSenderUser(),
          builder: (context, snapshot) {
            final user = snapshot.data ?? event.senderFromMemoryOrFallback;
            return Avatar(
              mxContent: user.avatarUrl,
              name: user.calcDisplayname(),
              presenceUserId: user.stateKey,
              presenceBackgroundColor: avatarPresenceBackgroundColor,
              onTap: () => onAvatarTab(event),
              size: 30,
            );
          },
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
/*     Logs().v('Building message: ${event.eventId}');
    Logs().v('Event type: ${event.type}');
    Logs().v('Event encryption info: ${event.toJson()}');
    Logs().v('Event content: ${event.content}');
    Logs().v('Message ${event.eventId}:');
    Logs().v('- Sender: ${event.senderId}');
    Logs().v('- My user ID: ${NomoMatrix.I.client.userID}');
    Logs().v('- Encryption state: ${event.room.encrypted}');
    Logs().v('- My device ID: ${NomoMatrix.I.client.deviceID}');
    Logs().v('- Sender device ID: ${event.eventId}');

    if (event.type == EventTypes.Encrypted) {
      Logs().v('- Encryption algorithm: ${event.content['algorithm']}');
      Logs().v('- Session ID: ${event.content['session_id']}');
      Logs().v(
          '- Device verification status: ${NomoMatrix.I.client.getUserDeviceKeysByCurve25519Key(event.senderId)?.verified}');
    } */

    if (checkIfEventIsStateMessage(event)) {
      if (event.type.startsWith('m.call.')) {
        return const SizedBox.shrink();
      }
      return StateMessage(event);
    }

    if (event.type == EventTypes.Message &&
        event.messageType == EventTypes.KeyVerificationRequest) {
      return VerificationRequestContent(event: event, timeline: timeline);
    }
    const horizontalPadding = 12.0;
    final messageKey = GlobalKey();

    final client = NomoMatrix.I.client;
    final ownMessage = event.senderId == client.userID;
    var color = Theme.of(context).colorScheme.surfaceContainerHighest;
    final displayTime = event.type == EventTypes.RoomCreate ||
        nextEvent == null ||
        !event.originServerTs.sameEnvironment(nextEvent!.originServerTs);
    final sameSender = nextEvent != null &&
        {
          EventTypes.Message,
          EventTypes.Sticker,
          EventTypes.Encrypted,
        }.contains(nextEvent!.type) &&
        nextEvent?.relationshipType == null &&
        nextEvent!.senderId == event.senderId &&
        !displayTime;
    final textColor = ownMessage
        ? Theme.of(context).colorScheme.onPrimary
        : Theme.of(context).colorScheme.onSurfaceVariant;

    final displayEvent = event.getDisplayEvent(timeline);
/*     Logs().v('Display event status: ${displayEvent.status}');
    Logs().v(
        'Display event decryption error: ${displayEvent.messageType == MessageTypes.BadEncrypted ? displayEvent.content : "none"}'); */
    final borderRadius = getBorderRadius(ownMessage);
    final noBubble = {
              MessageTypes.Sticker,
            }.contains(event.messageType) &&
            !event.redacted ||
        event.isNomoSendAssetMessage;
    final noPadding = {
      MessageTypes.File,
      MessageTypes.Audio,
      MessageTypes.Image,
      MessageTypes.Video,
    }.contains(event.messageType);

    final eventIsImageOrVideo = event.messageType == MessageTypes.Image ||
        event.messageType == MessageTypes.Video;

    if (ownMessage) {
      color = displayEvent.status.isError
          ? Colors.redAccent
          : Theme.of(context).colorScheme.primaryContainer;
    }

    final isPreviousFromOtherSender =
        nextEvent != null && nextEvent!.senderId != event.senderId;

    final hasBeenEdited = event.hasAggregatedEvents(
      timeline,
      RelationshipTypes.edit,
    );
    final replyBorderRadius = BorderRadius.circular(AppConfig.borderRadius / 2);
    final showReceiptsRow =
        event.hasAggregatedEvents(timeline, RelationshipTypes.reaction);
    final hasReplies = event.relationshipType == RelationshipTypes.reply;

    final contentPaddingTop = hasReplies ? 0.0 : 6.0;
    const contentPaddingRight = 12.0;
    const contentPaddingLeft = 12.0;
    const contentPaddingBottom = 6.0;

    final contentPadding = noPadding || noBubble
        ? EdgeInsets.zero
        : EdgeInsets.only(
            top: contentPaddingTop,
            right: contentPaddingRight,
            left: contentPaddingLeft,
          );
    final seenBy = controller.room.getSeenByUsers(controller.timeline!);
    final roomParticipants = controller.room.getParticipants()
      ..removeWhere((element) => element.id == client.userID);
    final seen = seenBy.containsAll(roomParticipants);
    final seenIcon = seen ? Icons.done_all_rounded : Icons.done_rounded;
    final content = [
      if (hasReplies)
        FutureBuilder<Event?>(
          future: event.getReplyEvent(timeline),
          builder: (BuildContext context, snapshot) {
            final replyEvent = snapshot.hasData
                ? snapshot.data!
                : Event(
                    eventId: event.relationshipEventId!,
                    content: {
                      'msgtype': 'm.text',
                      'body': '...',
                    },
                    senderId: event.senderId,
                    type: 'm.room.message',
                    room: event.room,
                    status: EventStatus.sent,
                    originServerTs: DateTime.now(),
                  );
            return Padding(
              padding: const EdgeInsets.only(
                bottom: 4.0,
              ),
              child: InkWell(
                borderRadius: replyBorderRadius,
                onTap: () => scrollToEventId(
                  replyEvent.eventId,
                ),
                child: AbsorbPointer(
                  child: NomoReplyContent(replyEvent,
                      ownMessage: ownMessage,
                      timeline: timeline,
                      borderRadius: replyBorderRadius),
                ),
              ),
            );
          },
        ),
      Padding(
        padding: contentPadding,
        child: MessageContent(
          displayEvent,
          textColor: textColor,
          borderRadius: borderRadius,
        ),
      ),
      if (!event.isNomoSendAssetMessage)
        Padding(
          padding: EdgeInsets.only(
            top: eventIsImageOrVideo ? 2 * contentPaddingTop : 0.0,
            bottom: contentPaddingBottom,
            right: contentPaddingRight,
            left: contentPaddingLeft,
          ),
          child: Align(
            alignment: Alignment.bottomRight,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (hasBeenEdited) ...[
                  Icon(
                    Icons.edit_outlined,
                    color: textColor.withOpacity(ownMessage ? .7 : .5),
                    size: 14,
                  ),
                  const SizedBox(width: 2),
                  Text('•',
                      style: TextStyle(
                          fontSize: 12,
                          color: textColor.withOpacity(ownMessage ? .7 : .5))),
                  const SizedBox(width: 2),
                ],
                Text(event.originServerTs.localizedTime(context),
                    style: TextStyle(
                        fontSize: 12,
                        color: textColor.withOpacity(ownMessage ? .7 : .5))),
                if (ownMessage) ...[
                  const SizedBox(width: 2),
                  if (event.status.isError) ...[
                    Icon(
                      Icons.error_outline,
                      color: textColor.withOpacity(ownMessage ? .7 : .5),
                      size: 16,
                    )
                  ] else
                    Icon(
                      seenIcon,
                      color: textColor.withOpacity(ownMessage ? .7 : .5),
                      size: 16,
                    ),
                ]
              ],
            ),
          ),
        )
    ];
    final Widget contentWidget = Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: content,
    );

    final message = LayoutBuilder(
      builder: (context, constraints) {
        double chatWidth;
        if (constraints.maxWidth > 630) {
          chatWidth = constraints.maxWidth /
              2; // Use half the width in medium width screens
        }
        if (constraints.maxWidth > 700) {
          chatWidth = constraints.maxWidth -
              100; // Use full widh - 100 for large screens
        } else {
          chatWidth = constraints.maxWidth -
              100; // Use full width -100 for smaller screens
        }

        return Padding(
          padding: EdgeInsets.only(
            left: horizontalPadding,
            right: horizontalPadding,
            top: isPreviousFromOtherSender ? 12 : 4.0,
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment:
                ownMessage ? MainAxisAlignment.end : MainAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              if (ownMessage)
                Expanded(
                  child: getLeadingWidget(
                      sameSender: sameSender, ownMessage: ownMessage),
                ),
              Container(
                constraints: BoxConstraints(
                  maxWidth: chatWidth,
                ),
                child: IntrinsicWidth(
                  child: Stack(
                    children: [
                      Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (!sameSender)
                            ownMessage ||
                                    event.room.isDirectChat ||
                                    !showSenderName
                                ? const SizedBox.shrink()
                                : FutureBuilder<User?>(
                                    future: event.fetchSenderUser(),
                                    builder: (context, snapshot) {
                                      final displayname =
                                          snapshot.data?.calcDisplayname() ??
                                              event.senderFromMemoryOrFallback
                                                  .calcDisplayname();
                                      return Text(
                                        displayname,
                                        style: TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.bold,
                                          color:
                                              (Theme.of(context).brightness ==
                                                      Brightness.light
                                                  ? displayname.color
                                                  : displayname.lightColorText),
                                        ),
                                      );
                                    },
                                  ),
                          Container(
                            key: messageKey,
                            decoration: BoxDecoration(
                                color: noBubble ? null : color,
                                borderRadius: borderRadius),
                            child: contentWidget,
                          ),
                          if (showReceiptsRow)
                            const SizedBox(
                              height: 25,
                            )
                        ],
                      ),
                      if (showReceiptsRow && !isReaction)
                        Positioned(
                          bottom: 0,
                          left: ownMessage ? null : 10,
                          right: ownMessage ? 10.0 : null,
                          child: MessageReactions(event, timeline),
                        ),
                    ],
                  ),
                ),
              ),
              if (!ownMessage) Expanded(child: Container()),
            ],
          ),
        );
      },
    );
    if (event.redacted || isReaction) {
      return message;
    }
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        if (controller.selectMode) {
          controller.onSelectMessage(event);
        } else if (event.messageType == MessageTypes.Location) {
          _navigateMap(context);
        }
      },
      onLongPress: longPressSelect
          ? null
          : () => _showReactionMenu(
              context, horizontalPadding, messageKey, message),
      child: Container(
        color: selected
            ? Theme.of(context).colorScheme.primary.withOpacity(.25)
            : null,
        child: SwipeToReply(
          key: ValueKey(event.eventId),
          onRightSwipe: () {
            onSwipe.call();
            HapticFeedback.mediumImpact();
          },
          replyIconColor: Theme.of(context).colorScheme.onSurface,
          child: HoverBuilder(
            builder: (context, hovered) {
              return AbsorbPointer(
                  absorbing: controller.selectMode ||
                      event.messageType == MessageTypes.Location,
                  child: message);
            },
          ),
        ),
      ),
    );
  }

  void _navigateMap(BuildContext context) {
    final geoUri = Uri.tryParse(event.content.tryGet<String>('geo_uri')!);
    if (geoUri != null && geoUri.scheme == 'geo') {
      final latlong = geoUri.path
          .split(';')
          .first
          .split(',')
          .map((s) => double.tryParse(s))
          .toList();
      if (latlong.length == 2 &&
          latlong.first != null &&
          latlong.last != null) {
        final geoUri =
            'https://www.google.com/maps/search/?api=1&query=${latlong.first!},${latlong.last!}';
        UrlLauncher(context, geoUri).launchUrl();
      }
    }
  }

  void _showReactionMenu(BuildContext context, double horizontalPadding,
      GlobalKey messageKey, Widget message) {
    {
      if (event.redacted) return;
      HapticFeedback.selectionClick();
      final RenderBox messageBox =
          messageKey.currentContext!.findRenderObject()! as RenderBox;
      final Offset messagePosition = messageBox.localToGlobal(Offset.zero);

      showDialog(
        context: context,
        barrierDismissible: true,
        barrierColor: Colors.transparent,
        useRootNavigator: false,
        useSafeArea: false,
        builder: (context) {
          return EditChatMessageScreen(
            controller: controller,
            message: message,
            messageOffset: messagePosition,
            messageSize: messageBox.size,
            event: event,
          );
        },
      );
    }
  }
}
