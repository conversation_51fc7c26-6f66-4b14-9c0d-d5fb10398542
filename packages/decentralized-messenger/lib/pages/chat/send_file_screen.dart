// ignore_for_file: use_build_context_synchronously

import 'dart:io';

import 'package:decentralized_messenger/pages/nomo_widget/shared_media.dart';
import 'package:decentralized_messenger/utils/matrix_sdk_extensions/matrix_file_extension.dart';
import 'package:decentralized_messenger/utils/resize_video.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:cross_file/cross_file.dart';
import 'package:future_loading_dialog/future_loading_dialog.dart';
import 'package:matrix/matrix.dart';
import 'package:mime/mime.dart';

import 'package:decentralized_messenger/config/app_config.dart';
import 'package:decentralized_messenger/utils/size_string.dart';
import 'package:nomo_app/features/chat/utils/platform_infos.dart';
import 'package:nomo_ui_kit/components/buttons/primary/nomo_primary_button.dart';
import 'package:nomo_ui_kit/theme/nomo_theme.dart';
import 'package:path_provider/path_provider.dart';

class NomoSendFileScreen extends StatefulWidget {
  final Room room;
  final List<XFile> files;

  const NomoSendFileScreen({
    required this.room,
    required this.files,
    super.key,
  });

  @override
  NomoSendFileScreenState createState() => NomoSendFileScreenState();
}

class NomoSendFileScreenState extends State<NomoSendFileScreen> {
  bool origImage = false;

  /// Images smaller than 20kb don't need compression.
  static const int minSizeToCompress = 20 * 1024;

  String? directory;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      final dic = await getTemporaryDirectory();
      directory = '${dic.path}/camerawesome';
      setState(() {});
    });
  }

  Future<void> _send() async {
    final clientConfig = await widget.room.client.getConfig();
    final maxUploadSize = clientConfig.mUploadSize ?? 100 * 1024 * 1024;
    for (var xfile in widget.files) {
      final MatrixFile file;
      MatrixImageFile? thumbnail;
      final length = await xfile.length();
      final mimeType = xfile.mimeType ?? lookupMimeType(xfile.path);

      // If file is a video, shrink it!
      if (PlatformInfos.isMobile &&
          mimeType != null &&
          mimeType.startsWith('video') &&
          length > minSizeToCompress &&
          !origImage) {
        file = await xfile.resizeVideo();
        thumbnail = await xfile.getVideoThumbnail();
      } else {
        // Else we just create a MatrixFile
        file = MatrixFile(
          bytes: await xfile.readAsBytes(),
          name: xfile.name,
          mimeType: xfile.mimeType,
        ).detectFileType;
      }

      if (file.bytes.length > maxUploadSize) {
        throw FileTooBigMatrixException(length, maxUploadSize);
      }

      widget.room
          .sendFileEvent(
        file,
        thumbnail: thumbnail,
        shrinkImageMaxDimension: origImage ? null : 1600,
      )
          .catchError((e) {
        if (e is MatrixException) {
          final retryAfterMs = e.retryAfterMs;
          if (e.error == MatrixError.M_LIMIT_EXCEEDED && retryAfterMs != null) {
            final retryAfterDuration =
                Duration(milliseconds: retryAfterMs + 1000);
            Future.delayed(retryAfterDuration, () {
              widget.room
                  .sendFileEvent(
                file,
                thumbnail: thumbnail,
                shrinkImageMaxDimension: origImage ? null : 1600,
              )
                  .catchError((e) {
                throw e;
              });
            });
          } else {
            throw e;
          }
        } else {
          throw e;
        }
        return e.errorMessage;
      });
      // just so it looks "smoother"
      await Future.delayed(Duration(milliseconds: 500));

      return;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (directory == null) return const SizedBox.shrink();
    final files = widget.files.map((file) => File(file.path)).toList();
    final file = File(widget.files.first.path);
    final bool allFilesAreImages = files.every((file) {
      final mimeType = lookupMimeType(file.path);
      return mimeType != null && mimeType.startsWith('image/');
    });
    final bool allFilesAreVideos = files.every((file) {
      final mimeType = lookupMimeType(file.path);
      return mimeType != null && mimeType.startsWith('video/');
    });
    final sizeString = files
        .fold<double>(0, (p, file) => p + file.readAsBytesSync().length)
        .sizeString;
    final fileName = widget.files.length == 1
        ? widget.files.single.name
        : '${widget.files.length}';
    Widget contentWidget;
    if (allFilesAreImages) {
      contentWidget = Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Material(
          borderRadius: BorderRadius.circular(AppConfig.borderRadius),
          elevation: Theme.of(context).appBarTheme.scrolledUnderElevation ?? 4,
          shadowColor: Theme.of(context).appBarTheme.shadowColor,
          clipBehavior: Clip.hardEdge,
          child: Image.file(
            file,
            fit: BoxFit.contain,
          ),
        ),
      );
    } else if (allFilesAreVideos) {
      contentWidget = Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Material(
            borderRadius: BorderRadius.circular(AppConfig.borderRadius),
            elevation:
                Theme.of(context).appBarTheme.scrolledUnderElevation ?? 4,
            shadowColor: Theme.of(context).appBarTheme.shadowColor,
            clipBehavior: Clip.hardEdge,
            child: VideoPreview(file: file)),
      );
    } else {
      contentWidget = Text('$fileName ($sizeString)');
    }
    return Scaffold(
      appBar: AppBar(
        leading: BackButton(
          onPressed: () => Navigator.of(context, rootNavigator: true).pop(),
        ),
        backgroundColor: Colors.transparent,
      ),
      backgroundColor: context.theme.colors.surface,
      extendBodyBehindAppBar: true,
      body: Padding(
        padding: const EdgeInsets.only(top: 30, bottom: 20),
        child: Center(child: contentWidget),
      ),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.only(bottom: 20, right: 20, left: 20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                CupertinoSwitch(
                  value: origImage,
                  onChanged: (v) => setState(() => origImage = v),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Send Original',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      Text(sizeString),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 5),
            PrimaryNomoButton(
              onPressed: () async {
                await showFutureLoadingDialog(
                  context: context,
                  future: () => _send(),
                );
                await Future.delayed(const Duration(milliseconds: 200));
                Navigator.of(context, rootNavigator: false).pop();
              },
              text: 'send',
              width: double.infinity,
            ),
          ],
        ),
      ),
    );
  }
}
