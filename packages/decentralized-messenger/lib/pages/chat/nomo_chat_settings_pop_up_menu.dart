// ignore_for_file: use_build_context_synchronously

import 'dart:async';

import 'package:decentralized_messenger/config/app_config.dart';
import 'package:decentralized_messenger/pages/nomo_utils/nomo_extensions.dart';
import 'package:decentralized_messenger/utils/nomo_extensions.dart';
import 'package:decentralized_messenger/widgets/nomo_matrix.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:adaptive_dialog/adaptive_dialog.dart';
import 'package:flutter_gen/gen_l10n/l10n.dart';
import 'package:future_loading_dialog/future_loading_dialog.dart';
import 'package:go_router/go_router.dart';
import 'package:keyboard_shortcuts/keyboard_shortcuts.dart';
import 'package:matrix/matrix.dart';

class NomoChatSettingsPopupMenu extends StatefulWidget {
  final Room room;
  final bool displayChatDetails;

  const NomoChatSettingsPopupMenu(this.room, this.displayChatDetails,
      {super.key});

  @override
  NomoChatSettingsPopupMenuState createState() =>
      NomoChatSettingsPopupMenuState();
}

class NomoChatSettingsPopupMenuState extends State<NomoChatSettingsPopupMenu> {
  StreamSubscription? notificationChangeSub;

  @override
  void dispose() {
    notificationChangeSub?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    notificationChangeSub ??= NomoMatrix.I.client.onAccountData.stream
        .where((u) => u.type == 'm.push_rules')
        .listen(
          (u) => setState(() {}),
        );
    final items = <PopupMenuEntry<String>>[
      widget.room.pushRuleState == PushRuleState.notify
          ? PopupMenuItem<String>(
              value: 'mute',
              child: Row(
                children: [
                  const Icon(Icons.notifications_off_outlined),
                  const SizedBox(width: 12),
                  Text(L10n.of(context)!.muteChat),
                ],
              ),
            )
          : PopupMenuItem<String>(
              value: 'unmute',
              child: Row(
                children: [
                  const Icon(Icons.notifications_on_outlined),
                  const SizedBox(width: 12),
                  Text(L10n.of(context)!.unmuteChat),
                ],
              ),
            ),
      PopupMenuItem<String>(
        value: 'leave',
        child: Row(
          children: [
            Icon(
              Icons.exit_to_app_rounded,
              color: context.theme.colorScheme.onSurface,
            ),
            const SizedBox(width: 12),
            Text(
              L10n.of(context)!.leave,
            ),
          ],
        ),
      ),
      if (widget.room.isDirectChat)
        PopupMenuItem<String>(
          value: 'block',
          child: Row(
            children: [
              Icon(
                Icons.block_flipped,
                color: context.theme.colorScheme.error,
              ),
              const SizedBox(width: 12),
              Text(
                L10n.of(context)!.block.capitalized,
                style: TextStyle(color: context.theme.colorScheme.error),
              ),
            ],
          ),
        ),
    ];

    if (widget.room.name == "Note to self") {
      return const SizedBox.shrink();
    }

    return Stack(
      alignment: Alignment.center,
      children: [
        KeyBoardShortcuts(
          keysToPress: {
            LogicalKeyboardKey.controlLeft,
            LogicalKeyboardKey.keyI,
          },
          helpLabel: L10n.of(context)!.chatDetails,
          onKeysPressed: _showChatDetails,
          child: const SizedBox.shrink(),
        ),
        PopupMenuButton(
          enableFeedback: true,
          clipBehavior: Clip.hardEdge,
          elevation: 1,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConfig.borderRadius),
          ),
          padding: EdgeInsets.zero,
          color: context.theme.colorScheme.surface,
          surfaceTintColor: context.theme.colorScheme.surface,
          icon: const Icon(Icons.more_vert_rounded),
          onSelected: (String choice) async {
            switch (choice) {
              case 'leave':
                final confirmed = await showOkCancelAlertDialog(
                  useRootNavigator: false,
                  context: context,
                  title: L10n.of(context)!.areYouSure,
                  okLabel: L10n.of(context)!.ok,
                  cancelLabel: L10n.of(context)!.cancel,
                  message: L10n.of(context)!.archiveRoomDescription,
                );
                if (confirmed == OkCancelResult.ok) {
                  final success = await showFutureLoadingDialog(
                    context: context,
                    future: () => widget.room.leave(),
                  );
                  if (success.error == null) {
                    context.go('/rooms');
                  }
                }
                break;
              case 'mute':
                await showFutureLoadingDialog(
                  context: context,
                  future: () =>
                      widget.room.setPushRuleState(PushRuleState.mentionsOnly),
                );
                break;
              case 'unmute':
                await showFutureLoadingDialog(
                  context: context,
                  future: () =>
                      widget.room.setPushRuleState(PushRuleState.notify),
                );
                break;
              case 'todos':
                context.go('/rooms/${widget.room.id}/tasks');
                break;
              case 'block':
                final userID = widget.room.directChatMatrixID;
                if (userID == null) return;
                Completer<void> completer = Completer<void>();
                NomoMatrix.I.client.onSync.stream.listen(
                  (event) async {
                    await Future.delayed(const Duration(seconds: 1));
                    completer.complete();
                    context.pop();
                  },
                );
                await showFutureLoadingDialog(
                    context: context,
                    future: () => Future.wait([
                          NomoMatrix.I.client.ignoreUser(userID),
                          widget.room.leave(),
                          completer.future
                        ]));
                break;
            }
          },
          itemBuilder: (BuildContext context) => items,
        ),
      ],
    );
  }

  void _showChatDetails() {
    if (GoRouterState.of(context).uri.path.endsWith('/details')) {
      context.go('/rooms/${widget.room.id}');
    } else {
      context.go('/rooms/${widget.room.id}/details');
    }
  }
}
