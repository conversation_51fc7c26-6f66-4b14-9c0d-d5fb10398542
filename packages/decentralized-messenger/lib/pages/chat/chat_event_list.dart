import 'package:decentralized_messenger/pages/chat/events/nomo_message.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import 'package:matrix/matrix.dart';
import 'package:nomo_app/common/database/global_hive_keys.dart';
import 'package:scroll_to_index/scroll_to_index.dart';

import 'package:decentralized_messenger/config/themes.dart';
import 'package:decentralized_messenger/pages/chat/chat.dart';
import 'package:decentralized_messenger/pages/chat/typing_indicators.dart';
import 'package:decentralized_messenger/pages/user_bottom_sheet/user_bottom_sheet.dart';
import 'package:decentralized_messenger/utils/account_config.dart';
import 'package:decentralized_messenger/utils/matrix_sdk_extensions/filtered_timeline_extension.dart';
import 'package:decentralized_messenger/utils/platform_infos.dart';

final locallyDeletedEventsProvider = StateProvider<List<String>>(
  (_) {
    final box = getGlobalHiveBox();
    final List<String> deletedEvents =
        box.get('deleted_events', defaultValue: <String>[]);
    return deletedEvents;
  },
);

class ChatEventList extends ConsumerWidget {
  final ChatController controller;
  const ChatEventList({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final timeline = controller.timeline;
    if (timeline == null) {
      Future.delayed(Duration.zero);
      return const Center(
        child: CircularProgressIndicator.adaptive(
          strokeWidth: 2,
        ),
      );
    }

    final deletedEvents = ref.watch(locallyDeletedEventsProvider);
    final horizontalPadding = NomoThemes.isColumnMode(context) ? 8.0 : 0.0;
    final events = timeline.events
        .where(
          (event) =>
              event.isVisibleInGui && !deletedEvents.contains(event.eventId),
        )
        .toList();
    final animateInEventIndex = controller.animateInEventIndex;

    // create a map of eventId --> index to greatly improve performance of
    // ListView's findChildIndexCallback
    final thisEventsKeyMap = <String, int>{};
    for (var i = 0; i < events.length; i++) {
      thisEventsKeyMap[events[i].eventId] = i;
    }

    final hasWallpaper =
        controller.room.client.applicationAccountConfig.wallpaperUrl != null;

    return SelectionArea(
      child: ListView.custom(
        padding: EdgeInsets.only(
          top: 16,
          bottom: 4,
          left: horizontalPadding,
          right: horizontalPadding,
        ),
        reverse: true,
        controller: controller.scrollController,
        keyboardDismissBehavior: PlatformInfos.isIOS
            ? ScrollViewKeyboardDismissBehavior.onDrag
            : ScrollViewKeyboardDismissBehavior.manual,
        childrenDelegate: SliverChildBuilderDelegate(
          (BuildContext context, int i) {
            // Footer to display typing indicator and read receipts:
            if (i == 0) {
              if (timeline.isRequestingFuture) {
                return const Center(
                  child: CircularProgressIndicator.adaptive(strokeWidth: 2),
                );
              }
              if (timeline.canRequestFuture) {
                return Center(
                  child: IconButton(
                    onPressed: controller.requestFuture,
                    icon: const Icon(Icons.refresh_outlined),
                  ),
                );
              }
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TypingIndicators(controller),
                ],
              );
            }

            // Request history button or progress indicator:
            if (i == events.length + 1) {
              if (timeline.isRequestingHistory) {
                return const Center(
                  child: CircularProgressIndicator.adaptive(strokeWidth: 2),
                );
              }
              if (timeline.canRequestHistory) {
                return Builder(
                  builder: (context) {
                    WidgetsBinding.instance
                        .addPostFrameCallback(controller.requestHistory);
                    return Center(
                      child: IconButton(
                        onPressed: controller.requestHistory,
                        icon: const Icon(Icons.refresh_outlined),
                      ),
                    );
                  },
                );
              }
              return const SizedBox.shrink();
            }
            i--;

            // The message at this index:
            final event = events[i];
            final animateIn = animateInEventIndex != null &&
                timeline.events.length > animateInEventIndex &&
                event == timeline.events[animateInEventIndex];

            return AutoScrollTag(
              key: ValueKey(event.eventId),
              index: i,
              controller: controller.scrollController,
              child: NomoMessage(
                event,
                animateIn: animateIn,
                controller: controller,
                resetAnimateIn: () {
                  controller.animateInEventIndex = null;
                },
                onSwipe: () => controller.replyAction(replyTo: event),
                onInfoTab: (p0) {},
                onAvatarTab: (Event event) => showDialog(
                  context: context,
                  builder: (c) => UserBottomSheet(
                    user: event.senderFromMemoryOrFallback,
                    outerContext: context,
                    onMention: () => controller.sendController.text +=
                        '${event.senderFromMemoryOrFallback.mention} ',
                  ),
                ),
                highlightMarker:
                    controller.scrollToEventIdMarker == event.eventId,
                onSelect: controller.onSelectMessage,
                scrollToEventId: (String eventId) =>
                    controller.scrollToEventId(eventId),
                longPressSelect: controller.selectedEvents.isNotEmpty,
                selected: controller.selectedEvents
                    .any((e) => e.eventId == event.eventId),
                timeline: timeline,
                displayReadMarker:
                    controller.readMarkerEventId == event.eventId &&
                        timeline.allowNewEvent == false,
                nextEvent: i + 1 < events.length ? events[i + 1] : null,
                previousEvent: i > 1 ? events[i - 2] : null,
                avatarPresenceBackgroundColor:
                    hasWallpaper ? Colors.transparent : null,
                isDirectChat: controller.room.isDirectChat,
              ),
            );
          },
          childCount: events.length + 2,
          findChildIndexCallback: (key) =>
              controller.findChildIndexCallback(key, thisEventsKeyMap),
        ),
      ),
    );
  }
}
