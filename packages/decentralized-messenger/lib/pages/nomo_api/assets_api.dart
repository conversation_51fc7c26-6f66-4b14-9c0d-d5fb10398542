import 'package:decentralized_messenger/pages/nomo_api/wallet_info.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

// import 'package:webon_kit_dart/webon_kit_dart.dart';

const String baseUrl = "https://zeniq.chat";

String getWalletEndpoint(String userId) {
  return "/_matrix/client/v3/user/$userId/account_data/nomo.wallets";
}

Future<void> uploadWalletInfo(
  String userId,
  String accessToken,
  Map<String, dynamic> wallets,
) async {
  final String endpoint = getWalletEndpoint(userId);

  final Uri url = Uri.parse(baseUrl + endpoint);

  try {
    final response = await http.put(
      url,
      headers: <String, String>{
        'Content-Type': 'application/json; charset=UTF-8',
        'Authorization': 'Bearer $accessToken',
      },
      body: jsonEncode(wallets),
    );

    if (response.statusCode == 200) {
      debugPrint("Wallets set successfully");
    } else {
      debugPrint("Failed to set wallets. Status code: ${response.statusCode}");
      debugPrint("Response body: ${response.body}");
    }
  } catch (error) {
    debugPrint("Error setting wallets: $error");
  }
}

Future<ChatWalletInfo?> fetchWalletInfo(
    String userId, String accessToken) async {
  final String endpoint = getWalletEndpoint(userId);

  final Uri url = Uri.parse(baseUrl + endpoint);

  try {
    final response = await http.get(
      url,
      headers: <String, String>{
        'Authorization': 'Bearer $accessToken',
      },
    );

    if (response.statusCode == 200) {
      final Map<String, dynamic> responseBody = jsonDecode(response.body);
      debugPrint('Received Wallet: $responseBody');
      final walletInfo = ChatWalletInfo.fromJson(responseBody);
      return walletInfo;
    } else {
      debugPrint(
        "Failed to retrieve wallets. Status code: ${response.statusCode}",
      );
      debugPrint("Response body: ${response.body}");
      return null;
    }
  } catch (error) {
    debugPrint("Error retrieving wallets: $error");
    return null;
  }
}
