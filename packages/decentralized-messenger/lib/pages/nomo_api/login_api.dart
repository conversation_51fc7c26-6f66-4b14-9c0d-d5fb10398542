// ignore_for_file: depend_on_referenced_packages

import 'dart:async';
import 'dart:convert';
import 'package:decentralized_messenger/nomo_interface.dart';
import 'package:decentralized_messenger/pages/nomo_api/user_matrix_model.dart';
import 'package:decentralized_messenger/utils/nomo_http_service.dart';
import 'package:web3dart/web3dart.dart';
import 'dart:typed_data';
import 'package:convert/convert.dart' as convert;
import 'package:http/http.dart' as http;
import 'package:web3dart/crypto.dart' as web3crypto;

const String chatHomeServer = "https://zeniq.chat";
const loginUrl = '$chatHomeServer/_matrix/client/v3/login';
const registerUrl = '$chatHomeServer/_matrix/client/v3/register';
final nomoInterface = NomoInterface.I;

Future<UserMatrix?> logIntoExistingChatAccountOrThrow() async {
  final Credentials credentials = await nomoInterface.getChatCredentials();

  final response = await _postChatLoginRequest(credentials);
  if (response.statusCode == 200) {
    final body = response.body;
    final userMatrix = UserMatrix.fromJson(jsonDecode(body));

    return userMatrix;
  }
  return await registerChatAccount(credentials);
}

Future<UserMatrix> registerChatAccount(credentials) async {
  final address = credentials.address;
  final String userName = address.toString();
  final message = "${userName}_${DateTime.now().millisecondsSinceEpoch}";
  final publickey = toChecksumAddress(address.toString());
  final String password = "0x${nomoInterface.signEvm(credentials, message)}";

  final Map<String, dynamic> jsonData = {
    'username': userName,
    'message': message,
    'publickey': publickey,
    'password': password,
  };

  final http.Response response = await HTTPService.client.post(
    Uri.parse(registerUrl),
    headers: <String, String>{
      'Content-Type': 'application/json; charset=UTF-8',
    },
    body: jsonEncode(jsonData),
  );
  if (response.statusCode == 200) {
    final String body = response.body;
    final userMatrix = UserMatrix.fromJson(jsonDecode(body));
    return userMatrix;
  } else {
    return Future.error("register error - not 200");
  }
}

String toChecksumAddress(String address) {
  if (!address.startsWith("0x")) {
    throw ArgumentError("not an EVM address");
  }
  final stripAddress = address.replaceFirst("0x", "").toLowerCase();
  final Uint8List keccakHash = web3crypto.keccakUtf8(stripAddress);
  final String keccakHashHex = convert.hex.encode(keccakHash);

  String checksumAddress = "0x";
  for (var i = 0; i < stripAddress.length; i++) {
    final bool high = int.parse(keccakHashHex[i], radix: 16) >= 8;
    checksumAddress += (high ? stripAddress[i].toUpperCase() : stripAddress[i]);
  }
  return checksumAddress;
}

Future<http.Response> _postChatLoginRequest(Credentials credentials) async {
  final address = credentials.address;
  final String userName = address.toString();
  final message = "${userName}_${DateTime.now().millisecondsSinceEpoch}";
  final publickey = _toChecksumAddress(address.toString());
  final String password = "0x${nomoInterface.signEvm(credentials, message)}";

  final Map<String, dynamic> jsonData = {
    'type': 'm.login.signature',
    'username': userName,
    'publickey': publickey,
    'message': message,
    'signature': password,
  };

  final http.Response response = await HTTPService.client
      .post(
    Uri.parse(loginUrl),
    headers: <String, String>{
      'Content-Type': 'application/json; charset=UTF-8',
    },
    body: jsonEncode(jsonData),
  )
      .timeout(
    const Duration(seconds: 10),
    onTimeout: () {
      throw TimeoutException("Login request timed out");
    },
  );
  return response;
}

String _toChecksumAddress(String address) {
  if (!address.startsWith("0x")) {
    throw ArgumentError("not an EVM address");
  }
  final stripAddress = address.replaceFirst("0x", "").toLowerCase();
  final Uint8List keccakHash = web3crypto.keccakUtf8(stripAddress);
  final String keccakHashHex = convert.hex.encode(keccakHash);

  String checksumAddress = "0x";
  for (var i = 0; i < stripAddress.length; i++) {
    final bool high = int.parse(keccakHashHex[i], radix: 16) >= 8;
    checksumAddress += (high ? stripAddress[i].toUpperCase() : stripAddress[i]);
  }
  return checksumAddress;
}

/*
Future<UserMatrix?> nomoFallbackLogin({required String fallbackString}) async {
  final user =
      await _logIntoExistingChatAccountOrThrow(fallbackString: fallbackString);
  return user;
}

*/