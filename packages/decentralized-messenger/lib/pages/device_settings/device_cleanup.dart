import 'dart:convert';

import 'package:flutter/material.dart';

import 'package:adaptive_dialog/adaptive_dialog.dart';
import 'package:flutter_gen/gen_l10n/l10n.dart';
import 'package:future_loading_dialog/future_loading_dialog.dart';
import 'package:matrix/matrix.dart';

import 'package:decentralized_messenger/nomo_interface.dart';
import 'package:decentralized_messenger/widgets/nomo_matrix.dart';
import 'package:nomo_app/common/services/logger.dart';

/// A utility class to help clean up old devices and improve encryption
class DeviceCleanup {
  /// Identifies and returns a list of inactive devices that can be safely removed
  ///
  /// Devices are considered inactive if they haven't been seen in the last 30 days
  /// and are not the current device
  static Future<List<Device>> getInactiveDevices() async {
    try {
      final client = NomoMatrix.I.client;
      final devices = await client.getDevices();
      final currentDeviceId = client.deviceID;

      final now = DateTime.now().millisecondsSinceEpoch;

      const thirtyDaysMs = 30 * 24 * 60 * 60 * 1000;

      // Filter out devices that are:
      // 1. Not the current device
      // 2. Haven't been seen in the last 30 days
      final inactiveDevices = devices!.where((device) {
        if (device.deviceId == currentDeviceId) {
          return false; // Don't include current device
        }

        final lastSeen = device.lastSeenTs ?? 0;
        return (now - lastSeen) > thirtyDaysMs;
      }).toList();

      return inactiveDevices;
    } catch (e, s) {
      Logger.logError(e, s: s, hint: "Error getting inactive devices");
      return [];
    }
  }

  /// Removes inactive devices after user confirmation
  ///
  /// Returns true if devices were removed successfully, false otherwise
  static Future<bool> cleanupInactiveDevices(BuildContext context) async {
    try {
      final inactiveDevices = await getInactiveDevices();

      if (inactiveDevices.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text("No inactive devices found")),
        );
        return false;
      }

      final result = await showOkCancelAlertDialog(
        context: context,
        title: "Clean up old devices",
        message:
            "Found ${inactiveDevices.length} inactive devices. Do you want to remove them?",
        okLabel: L10n.of(context)!.remove,
        cancelLabel: L10n.of(context)!.cancel,
      );

      if (result != OkCancelResult.ok) {
        return false;
      }

      final deviceIds = inactiveDevices.map((d) => d.deviceId).toList();

      final success = await showFutureLoadingDialog(
        context: context,
        future: () => NomoMatrix.I.client.uiaRequestBackground(
          (auth) => NomoMatrix.I.client.deleteDevices(deviceIds, auth: auth),
        ),
      );

      if (success.error != null) {
        throw success.error!;
      }

      await _requestKeysForAllRooms();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content:
              Text("Successfully removed ${inactiveDevices.length} devices"),
        ),
      );

      return true;
    } catch (e, s) {
      Logger.logError(e, s: s, hint: "Error cleaning up inactive devices");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text("Error removing devices: ${e.toString()}"),
        ),
      );
      return false;
    }
  }

  /// Request keys for all encrypted rooms
  ///
  /// This helps recover keys after cleaning up devices
  static Future<void> _requestKeysForAllRooms() async {
    try {
      final client = NomoMatrix.I.client;

      await client.updateUserDeviceKeys();

      for (final room in client.rooms) {
        if (room.encrypted) {
          final timeline = await room.getTimeline();
          timeline.requestKeys();

          await client.encryption?.keyManager
              .clearOrUseOutboundGroupSession(room.id, use: true);
        }
      }

      // await client.sync();
    } catch (e, s) {
      Logger.logError(e, s: s, hint: "Error requesting keys for all rooms");
    }
  }

  /// Delete inactive devices without requiring authentication
  ///
  /// This is a workaround for the UIA authentication loop issue
  static Future<bool> deleteInactiveDevicesWithoutAuth(
      BuildContext context) async {
    try {
      final inactiveDevices = await getInactiveDevices();

      if (inactiveDevices.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text("No inactive devices found")),
        );
        return false;
      }

      final result = await showOkCancelAlertDialog(
        context: context,
        title: "Clean up old devices",
        message:
            "Found ${inactiveDevices.length} inactive devices. Do you want to remove them?",
        okLabel: L10n.of(context)!.remove,
        cancelLabel: L10n.of(context)!.cancel,
      );

      if (result != OkCancelResult.ok) {
        return false;
      }

      final client = NomoMatrix.I.client;
      int successCount = 0;

      // Try to get a UIA session first
      String? session;
      try {
        // Make a request that will fail but give us a session ID
        final response = await client.httpClient.delete(
          Uri.parse(
              '${client.homeserver}/_matrix/client/v3/devices/${inactiveDevices.first.deviceId}'),
          headers: {
            'Authorization': 'Bearer ${client.accessToken}',
            'Content-Type': 'application/json',
          },
        );

        // Try to extract the session ID from the response
        final responseData = jsonDecode(response.body);
        session = responseData['session'] as String?;
      } catch (e) {
        Logs().w('Failed to get UIA session: $e');
        // Continue without a session
      }

      final dialogResult = await showFutureLoadingDialog(
        context: context,
        future: () async {
          for (final device in inactiveDevices) {
            try {
              Logs().i('Attempting to delete device ${device.deviceId}');

              // Use the same authentication approach as the login process
              Map<String, dynamic> authData;

              try {
                // Get the user's credentials
                final credentials = await NomoInterface.I.getChatCredentials();
                final address = credentials.address;
                final String userName = address.toString();
                final message =
                    "${userName}_${DateTime.now().millisecondsSinceEpoch}";
                final String signature =
                    "0x${NomoInterface.I.signEvm(credentials, message)}";

                // Create the auth data using the signature-based authentication
                authData = {
                  'auth': <String, dynamic>{
                    'type': 'm.login.signature',
                    'username': userName,
                    'message': message,
                    'signature': signature,
                  }
                };

                // Add session if we have one
                if (session != null) {
                  (authData['auth'] as Map<String, dynamic>)['session'] =
                      session;
                }
              } catch (e) {
                Logs().w('Failed to create signature auth: $e');
                // Fallback to dummy auth
                authData = {
                  'auth': <String, dynamic>{
                    'type': 'm.login.dummy',
                  }
                };

                // Add session if we have one
                if (session != null) {
                  (authData['auth'] as Map<String, dynamic>)['session'] =
                      session;
                }
              }

              final response = await client.httpClient.delete(
                Uri.parse(
                    '${client.homeserver}/_matrix/client/v3/devices/${device.deviceId}'),
                headers: {
                  'Authorization': 'Bearer ${client.accessToken}',
                  'Content-Type': 'application/json',
                },
                body: jsonEncode(authData),
              );

              if (response.statusCode >= 200 && response.statusCode < 300) {
                Logs().i('Successfully deleted device ${device.deviceId}');
                successCount++;
              } else {
                Logs().w(
                    'Failed to delete device ${device.deviceId}: HTTP ${response.statusCode} - ${response.body}');

                // Try to extract session from error response
                try {
                  final responseData = jsonDecode(response.body);
                  session = responseData['session'] as String?;
                } catch (e) {
                  // Ignore parsing errors
                }
              }
            } catch (e) {
              Logs().w('Failed to delete device ${device.deviceId}: $e');
              // Continue with other devices even if one fails
            }

            // Wait a bit between requests to avoid rate limiting
            await Future.delayed(Duration(milliseconds: 300));
          }

          // Force refresh the device list
          await client.getDevices();

          // Request keys for all rooms after cleaning up devices
          await _requestKeysForAllRooms();

          return successCount;
        },
      );

      if (dialogResult.error != null) {
        throw dialogResult.error!;
      }

      final deletedCount = dialogResult.result ?? 0;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(deletedCount > 0
              ? "Successfully removed $deletedCount devices"
              : "Failed to remove devices. Try again or use device verification instead."),
        ),
      );

      return deletedCount > 0;
    } catch (e, s) {
      Logger.logError(e,
          s: s, hint: "Error cleaning up inactive devices without auth");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text("Error removing devices: ${e.toString()}"),
        ),
      );
      return false;
    }
  }

  /// Request keys for a specific room with bad encrypted messages
  ///
  /// This is a more aggressive approach to request keys for a specific room
  static Future<void> requestKeysForRoom(String roomId) async {
    try {
      final client = NomoMatrix.I.client;
      final room = client.getRoomById(roomId);

      if (room == null || !room.encrypted) {
        return;
      }

      await client
          .updateUserDeviceKeys(); // queries device keys from server and writes them to the database

      // Clear any existing outbound group session to force a new session
      await client.encryption?.keyManager
          .clearOrUseOutboundGroupSession(roomId, use: true);

      // Get the timeline and request keys
      final timeline = await room.getTimeline();

      // Find all bad encrypted messages
      final badEncryptedEvents = timeline.events.where((event) =>
          event.type == EventTypes.Encrypted &&
          event.messageType == MessageTypes.BadEncrypted);

      // Request keys for each bad encrypted message
      for (final event in badEncryptedEvents) {
        final sessionId = event.content['session_id'];
        final senderKey = event.content['sender_key'];

        if (sessionId != null && senderKey != null) {
          await room.requestSessionKey(
            sessionId.toString(),
            senderKey.toString(),
          );
        }
      }

      // Request all keys for the timeline
      timeline.requestKeys();

      // Force a sync to process any incoming key requests
      // await client.sync(); // does nothing except of giving a GET-response
    } catch (e, s) {
      Logger.logError(e, s: s, hint: "Error requesting keys for room");
    }
  }
}
