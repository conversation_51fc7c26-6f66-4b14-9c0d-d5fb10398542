import 'package:decentralized_messenger/pages/nomo_widget/details_container.dart';
import 'package:flutter/material.dart';

import 'package:flutter_gen/gen_l10n/l10n.dart';

import 'package:decentralized_messenger/nomo_messenger.dart';
import 'package:decentralized_messenger/pages/device_settings/device_settings.dart';
import 'package:decentralized_messenger/widgets/layouts/max_width_body.dart';
import 'package:nomo_app/common/widgets/scaffold/zeniq_scaffold.dart';
import 'package:nomo_app/features/webons/dev_mode/dev_mode.dart';
import 'package:nomo_app/main.dart';
import 'package:nomo_ui_kit/components/text/nomo_text.dart';
import 'package:nomo_ui_kit/theme/nomo_theme.dart';
import 'user_device_list_item.dart';

class DevicesSettingsView extends StatelessWidget {
  final DevicesSettingsController controller;

  const DevicesSettingsView(this.controller, {super.key});

  @override
  Widget build(BuildContext context) {
    final devMode = $ref.read(devModeProvider) != null;
    return ZeniqBackgroundWidget(
      child: Scaffold(
        appBar: AppBar(
          leading: const Center(child: BackButton()),
          title: Text(L10n.of(context)!.devices),
        ),
        body: MaxWidthBody(
          withScrolling: false,
          child: FutureBuilder<bool>(
            future: controller.loadUserDevices(context),
            builder: (BuildContext context, snapshot) {
              if (snapshot.hasError) {
                return Center(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: <Widget>[
                      const Icon(Icons.error_outlined),
                      Text(snapshot.error.toString()),
                    ],
                  ),
                );
              }
              if (!snapshot.hasData || controller.devices == null) {
                return const Center(
                  child: CircularProgressIndicator.adaptive(strokeWidth: 2),
                );
              }
              return ListView(
                physics: const BouncingScrollPhysics(
                    parent: AlwaysScrollableScrollPhysics()),
                children: [
                  const SizedBox(height: 20),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: NomoText(
                      L10n.of(context)!.thisDevice,
                      style: context.typography.b3,
                      opacity: 0.5,
                    ),
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  DetailsContainer(
                    children: [
                      UserDeviceListItem(
                        controller.thisDevice!,
                        rename: controller.renameDeviceAction,
                        remove: (d) => controller.removeDevicesAction([d]),
                        verify: controller.verifyDeviceAction,
                        block: controller.blockDeviceAction,
                        unblock: controller.unblockDeviceAction,
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  //TODO: Implement delete inactive devices
                  Visibility(
                    visible: false,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: ElevatedButton.icon(
                        icon: const Icon(Icons.cleaning_services_outlined),
                        label: const Text("Clean up inactive devices"),
                        onPressed: () =>
                            controller.cleanupInactiveDevicesAction(),
                      ),
                    ),
                  ),
                  const SizedBox(height: 20),
                  Visibility(
                    visible: devMode,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: ElevatedButton.icon(
                        icon: const Icon(Icons.refresh),
                        label: const Text("Re-login to Chat"),
                        onPressed: () => controller.reLoginAction(context),
                      ),
                    ),
                  ),
                  // Request keys button
                  Visibility(
                    visible: devMode,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: ElevatedButton.icon(
                        icon: const Icon(Icons.key),
                        label: const Text("Request Encryption Keys"),
                        onPressed: () =>
                            NomoMessenger.manuallyRequestKeys(context),
                      ),
                    ),
                  ),
                  Visibility(
                      visible: devMode, child: const SizedBox(height: 40)),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: NomoText(
                      'other_devices',
                      style: context.typography.b3,
                      opacity: 0.5,
                    ),
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  if (controller.notThisDevice.isNotEmpty)
                    DetailsContainer(
                      children: List.generate(
                          (controller.notThisDevice.length * 2) - 1, (index) {
                        final int i = index ~/ 2;
                        if (index.isOdd) {
                          return Padding(
                            padding: const EdgeInsets.only(right: 20, left: 50),
                            child: Divider(
                              height: .2,
                              thickness: .1,
                              color: NomoTheme.of(context)
                                  .colors
                                  .foreground1
                                  .withOpacity(.5),
                            ),
                          );
                        }
                        final device = controller.notThisDevice[i];
                        return UserDeviceListItem(
                          device,
                          rename: controller.renameDeviceAction,
                          remove: (d) => controller.removeDevicesAction([d]),
                          verify: controller.verifyDeviceAction,
                          block: controller.blockDeviceAction,
                          unblock: controller.unblockDeviceAction,
                        );
                      }),
                    )
                  else
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: NomoText(
                        'no_other_devices',
                        style: context.typography.b3,
                        opacity: 0.5,
                      ),
                    ),
                  const SizedBox(height: 100),
                ],
              );
            },
          ),
        ),
      ),
    );
  }
}
