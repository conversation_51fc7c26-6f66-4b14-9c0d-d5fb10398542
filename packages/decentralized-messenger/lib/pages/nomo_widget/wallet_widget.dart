import 'package:decentralized_messenger/pages/nomo_utils/nomo_extensions.dart';
import 'package:decentralized_messenger/pages/nomo_widget/balance_widget.dart';
import 'package:decentralized_messenger/pages/nomo_widget/price_widget.dart';
import 'package:decentralized_messenger/utils/nomo_extensions.dart';
import 'package:decentralized_messenger/pages/nomo_provider/send_assets_provider.dart';
import 'package:flutter/material.dart';
// import 'package:webon_kit_dart/webon_kit_dart.dart';

class WalletWidget extends StatelessWidget {
  const WalletWidget({super.key, required this.token});
  final Token token;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        SizedBox(height: 40, width: 40, child: Image.asset(token.assetPath)),
        const SizedBox(width: 10),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(token.name ?? '',
                style: TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.bold,
                    color: context.theme.colorScheme.onSurface)),
            Text(token.symbol,
                style: TextStyle(
                    fontSize: 15,
                    color: context.theme.colorScheme.onSurface
                        .withOpacity(.5))),
          ],
        ),
        const Spacer(),
        Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [BalanceWidget(token: token), PriceWidget(token: token)],
        ),
      ],
    );
  }
}
