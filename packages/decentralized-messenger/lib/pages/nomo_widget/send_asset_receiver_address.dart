import 'package:flutter/material.dart';

class AddressText extends StatelessWidget {
  const AddressText({super.key, this.address});
  final String? address;

  @override
  Widget build(BuildContext context) {
    if (address == null) {
      return const Text(
          'No address found of your Chat Partner. Make sure he was online since the last update!');
    }
    return Text("Recipient's address: $address");
  }
}