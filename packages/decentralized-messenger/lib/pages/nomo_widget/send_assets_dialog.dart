import 'package:decentralized_messenger/pages/nomo_provider/send_assets_provider.dart';
import 'package:decentralized_messenger/pages/nomo_utils/bigint_functions.dart';
import 'package:decentralized_messenger/pages/nomo_utils/nomo_extensions.dart';
import 'package:decentralized_messenger/pages/nomo_widget/send_asset_receiver_address.dart';
import 'package:decentralized_messenger/pages/nomo_widget/send_assets_continue_button.dart';
import 'package:decentralized_messenger/pages/nomo_widget/wallet_widget.dart';
import 'package:decentralized_messenger/utils/nomo_extensions.dart';
import 'package:decentralized_messenger/widgets/nomo_matrix.dart';import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

final sendAssetsAmountController = TextEditingController();

class AmountAndAddressDialog extends ConsumerWidget {
  final String roomID;

  const AmountAndAddressDialog({super.key, required this.roomID});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedToken = ref.watch(receiveTokenProvider);
    if (selectedToken == null) {
      return AlertDialog(
        title: const Text('No Token Selected'),
        content: const Text('Please select a token first'),
        actions: [
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('OK'),
          ),
        ],
      );
    }
    final room = NomoMatrix.I.client.getRoomById(roomID);
    if (room == null || !room.isDirectChat) {
      return AlertDialog(
        title: const Text('Send Assets unavailable'),
        content: const Text('Send Assets is not available in Group Chats'),
        actions: [
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('OK'),
          ),
        ],
      );
    }
    final receiverID = room.directChatMatrixID ?? '';
    final address = ref.watch(receiveAddressProvider(receiverID));
    final height = MediaQuery.of(context).size.height;

    return Scaffold(
      appBar: AppBar(
        title: Text('Send ${selectedToken.name}'),
        automaticallyImplyLeading: true,
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 30),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'PORTFOLIO BALANCE',
              style: TextStyle(
                  fontSize: 15,
                  color:
                      context.theme.colorScheme.onSurface.withOpacity(.5)),
            ),
            const SizedBox(height: 10),
            WalletWidget(token: selectedToken),
            const SizedBox(height: 20),
            Card(
              color: context.theme.cardColor,
              child: ConstrainedBox(
                constraints: BoxConstraints(
                    maxHeight: height * .4, minHeight: height * .2),
                child: Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Enter Amount',
                            style: TextStyle(
                                fontSize: 15,
                                color: context.theme.colorScheme.onSurface
                                    .withOpacity(.5)),
                          ),
                          Text(
                            selectedToken.symbol,
                            style: TextStyle(
                                fontSize: 15,
                                color: context.theme.colorScheme.onSurface
                                    .withOpacity(.5)),
                          ),
                        ],
                      ),
                      TextField(
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 50,
                          fontWeight: FontWeight.bold,
                          color: context.theme.colorScheme.onSurface,
                        ),
                        maxLines: 2,
                        minLines: 1,
                        inputFormatters: <TextInputFormatter>[
                          FilteringTextInputFormatter.allow(
                            RegExp(r'^[\d,]+\.?[\d,]{0,9}'),
                          )
                        ], // Only
                        controller: sendAssetsAmountController,
                        onChanged: (val) {
                          final String text = val;
                          final double? amount = tryParseInputDecimal(text);
                          final BigInt? amountBI = convertInputDoubleToBI(
                            amount,
                            selectedToken.decimals,
                          );
                          ref.read(amountInputProvider.notifier).state =
                              amountBI;
                        },
                        keyboardType: const TextInputType.numberWithOptions(
                          signed: false,
                          decimal: true,
                        ),
                        decoration: InputDecoration(
                          hintText: "0",
                          hintStyle: TextStyle(
                            fontSize: 50,
                            fontWeight: FontWeight.bold,
                            color: context.theme.colorScheme.onSurface
                                .withOpacity(.25),
                          ),
                          border: InputBorder.none,
                          enabledBorder: InputBorder.none,
                          focusedBorder: InputBorder.none,
                          contentPadding: const EdgeInsets.only(
                            top: 16,
                            bottom: 32,
                          ),
                        ),
                      ),
                      ActionChip(
                        onPressed: () {
                          final balance =
                              BigInt.tryParse(selectedToken.balance!);
                          final amount = balance.toAmountEntity(selectedToken);
                          sendAssetsAmountController.text =
                              amount?.displayValue.toString() ?? '';
                          ref.read(amountInputProvider.notifier).state =
                              balance;
                        },
                        label: Text(
                          'max',
                          style: TextStyle(
                              color: context.theme.colorScheme.primary,
                              fontWeight: FontWeight.bold),
                        ),
                        backgroundColor: context.theme.cardColor,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(50),
                          side: BorderSide(
                              color: context.theme.colorScheme.primary),
                        ),
                      )
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(height: 20),
            address.when(
              data: (data) {
                return AddressText(address: data);
              },
              error: (error, stackTrace) {
                return Text(error.toString());
              },
              loading: () => const CircularProgressIndicator.adaptive(),
            )
          ],
        ),
      ),
      bottomNavigationBar: Padding(
          padding: const EdgeInsets.all(20),
          child: address.when(
            data: (data) {
              return ContinueButton(
                  room: room, selectedToken: selectedToken, address: data);
            },
            error: (error, stackTrace) {
              return Container(
                  width: double.infinity,
                  height: 50,
                  decoration: BoxDecoration(
                    color: context.theme.disabledColor,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Center(
                      child: Text(
                    error.toString(),
                    style: TextStyle(
                        fontSize: 20,
                        color: context.theme.colorScheme.onSurface
                            .withOpacity(.75)),
                  )));
            },
            loading: () => const CircularProgressIndicator.adaptive(),
          )),
    );
  }
}
