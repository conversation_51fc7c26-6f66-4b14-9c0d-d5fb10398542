import 'package:decentralized_messenger/pages/user_bottom_sheet/user_bottom_sheet.dart';
import 'package:decentralized_messenger/utils/nomo_extensions.dart';
import 'package:decentralized_messenger/widgets/avatar.dart';
import 'package:decentralized_messenger/widgets/mxc_image.dart';
import 'package:flutter/material.dart';
import 'package:matrix/matrix.dart';

import 'package:decentralized_messenger/utils/nomo_share.dart';
import 'package:decentralized_messenger/widgets/nomo_matrix.dart';
import 'package:decentralized_messenger/utils/string_color.dart';
import 'package:nomo_app/common/extension/string.dart';
import 'package:nomo_ui_kit/components/text/nomo_text.dart';

class NomoUserBottomSheet extends StatelessWidget {
  const NomoUserBottomSheet(
      {super.key, required this.controller, required this.outerContext});
  final UserBottomSheetController controller;
  final BuildContext outerContext;

  @override
  Widget build(BuildContext context) {
    final user = controller.widget.user;
    final userId = (user?.id ?? controller.widget.profile?.userId)!;
    final displayname = (user?.calcDisplayname() ??
        controller.widget.profile?.displayName ??
        controller.widget.profile?.userId.localpart)!;
    final avatarUrl = user?.avatarUrl ?? controller.widget.profile?.avatarUrl;
    final client = NomoMatrix.I.client;

    final name =
        userId == client.userID ? '$displayname (${'you'.tr})' : displayname;
    return Dialog(
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: ConstrainedBox(
          constraints: const BoxConstraints(
            maxWidth: 350,
            minWidth: 150,
          ),
          child: IntrinsicHeight(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                AppBar(
                  leading: const SizedBox.shrink(),
                  backgroundColor: Colors.transparent,
                  centerTitle: false,
                  actions: [
                    Padding(
                      padding: const EdgeInsets.only(right: 8.0),
                      child: CloseButton(
                        onPressed:
                            Navigator.of(context, rootNavigator: false).pop,
                      ),
                    ),
                  ],
                ),
                Avatar(
                  mxContent: avatarUrl,
                  size: 100,
                  name: displayname,
                ),
                const SizedBox(
                  height: 10,
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: NomoText(
                    name,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: const TextStyle(
                        fontSize: 22, fontWeight: FontWeight.bold),
                  ),
                ),
                const SizedBox(
                  height: 10,
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: TextButton.icon(
                    onPressed: () => NomoShare.share(
                      'https://nomo.id/${userId.replaceAll(':zeniq.chat', '')}',
                      context,
                      copyOnly: true,
                    ),
                    icon: const Icon(
                      Icons.copy_outlined,
                      size: 14,
                    ),
                    style: TextButton.styleFrom(
                      foregroundColor: Theme.of(context).colorScheme.secondary,
                    ),
                    label: Text(
                      userId.replaceAll(':zeniq.chat', ''),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),
                ),
                if (userId != client.userID) ...[
                  const Spacer(),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: SizedBox(
                      width: double.infinity,
                      height: 50,
                      child: ElevatedButton(
                        style: ButtonStyle(
                          backgroundColor: WidgetStatePropertyAll(
                              context.theme.colorScheme.primary),
                          enableFeedback: true,
                        ),
                        onPressed: () => controller
                            .participantAction(UserBottomSheetAction.message),
                        child: Text(
                          controller.widget.user == null
                              ? "Start conversation"
                              : "Send a message",
                          style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: context.theme.colorScheme.onPrimary),
                        ),
                      ),
                    ),
                  ),
                ],
                const SizedBox(
                  height: 25,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class NomoAvatarContainer extends StatelessWidget {
  const NomoAvatarContainer(
      {super.key,
      required this.height,
      required this.mxContent,
      required this.name});

  final double height;
  final Uri? mxContent;
  final String? name;

  @override
  Widget build(BuildContext context) {
    var fallbackLetters = '@';
    final name = this.name;
    if (name != null) {
      if (name.runes.length >= 2) {
        fallbackLetters = String.fromCharCodes(name.runes, 0, 2);
      } else if (name.runes.length == 1) {
        fallbackLetters = name;
      }
    }
    final noPic = mxContent == null ||
        mxContent.toString().isEmpty ||
        mxContent.toString() == 'null';
    final textWidget = Center(
      child: Text(
        fallbackLetters,
        style: TextStyle(
          color: noPic ? Colors.white : null,
          fontSize: 18,
        ),
      ),
    );
    final color =
        noPic ? name?.lightColorAvatar : Theme.of(context).secondaryHeaderColor;
    return Container(
      height: height,
      width: double.infinity,
      color: color,
      child: noPic
          ? textWidget
          : MxcImage(
              key: Key(mxContent.toString()),
              uri: mxContent,
              fit: BoxFit.cover,
              width: double.infinity,
              height: height,
              placeholder: (_) => textWidget,
              cacheKey: mxContent.toString(),
            ),
    );
  }
}
