import 'package:decentralized_messenger/pages/nomo_widget/shared_media.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:nomo_app/features/webons/manifest/nomo_manifest.dart';
import 'package:nomo_ui_kit/components/dialog/nomo_dialog.dart';
import 'package:nomo_ui_kit/theme/nomo_theme.dart';

class SendWebonDialog extends ConsumerWidget {
  final List<NomoManifest> webons;

  const SendWebonDialog({super.key, required this.webons});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Material(
      color: Colors.transparent,
      child: NomoDialog(
        title: "Select a Webon to send!",
        titleStyle: NomoTheme.of(context).typography.h1,
        maxWidth: 400,
        padding: const EdgeInsets.all(16),
        content: webons.isEmpty
            ? const Text("No WebOns installed!")
            : SingleChildScrollView(
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    maxHeight: MediaQuery.of(context).size.height * 0.7,
                  ),
                  child: GridView.builder(
                    shrinkWrap: true,
                    itemCount: webons.length,
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 3,
                      mainAxisSpacing: 8,
                      crossAxisSpacing: 8,
                      childAspectRatio: 1,
                    ),
                    itemBuilder: (context, index) {
                      final webon = webons[index];

                      return GestureDetector(
                        onTap: () {
                          Navigator.pop(context, webon);
                        },
                        child: WebOnIconWithName(
                          manifest: webon,
                        ),
                      );
                    },
                  ),
                ),
              ),
      ),
    );
  }
}
