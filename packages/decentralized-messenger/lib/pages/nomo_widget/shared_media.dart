import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:chewie/chewie.dart';
import 'package:decentralized_messenger/pages/nomo_utils/nomo_extensions.dart';
import 'package:decentralized_messenger/utils/matrix_sdk_extensions/matrix_file_extension.dart';
import 'package:decentralized_messenger/utils/resize_video.dart';
import 'package:decentralized_messenger/widgets/avatar.dart';
import 'package:decentralized_messenger/widgets/hover_builder.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:matrix/matrix.dart';
import 'package:decentralized_messenger/widgets/nomo_matrix.dart';
import 'package:nomo_app/common/services/logger.dart';
import 'package:nomo_app/common/widgets/scaffold/zeniq_scaffold.dart';
import 'package:nomo_app/features/chat/utils/platform_infos.dart';
import 'package:nomo_app/features/webons/api/api.dart';
import 'package:nomo_app/features/webons/api/multi_webons.dart';
import 'package:nomo_app/features/webons/manifest/nomo_manifest.dart';
import 'package:nomo_app/features/webons/webon_icon.dart';
import 'package:nomo_app/main.dart';
import 'package:nomo_ui_kit/components/buttons/primary/nomo_primary_button.dart';
import 'package:nomo_ui_kit/components/input/textInput/nomo_input.dart';
import 'package:nomo_ui_kit/components/text/nomo_text.dart';
import 'package:nomo_ui_kit/theme/nomo_theme.dart';
import 'package:receive_sharing_intent/receive_sharing_intent.dart';
import 'package:flutter_gen/gen_l10n/l10n.dart';
import 'package:decentralized_messenger/utils/matrix_sdk_extensions/matrix_locals.dart';
import 'package:video_player/video_player.dart';
import 'package:mime/mime.dart';
import 'package:cross_file/cross_file.dart';

final sharedFilesProvider = StateProvider<List<SharedMediaFile>?>((ref) {
  return null;
});

class SharedMediaView extends StatefulWidget {
  const SharedMediaView({super.key, required this.sharedFile});

  final SharedMediaFile sharedFile;

  @override
  State<SharedMediaView> createState() => _SharedMediaViewState();
}

class _SharedMediaViewState extends State<SharedMediaView> {
  final pageController = PageController();
  int currentPage = 0;

  void shareFiles({required BuildContext context}) {
    for (final room in selectedRooms) {
      shareFile(file: widget.sharedFile, room: room);
    }

    Navigator.of(context).pop();
  }

  static const int minSizeToCompress = 20 * 1024;

  void shareFile({required SharedMediaFile file, required Room room}) async {
    switch (file.type) {
      case SharedMediaType.image:
        final localFile = File(file.path);
        final matrixFile = MatrixFile(
          bytes: localFile.readAsBytesSync(),
          name: localFile.path,
          mimeType: file.mimeType ?? lookupMimeType(localFile.path),
        );
        room.sendFileEvent(matrixFile);
        break;
      case SharedMediaType.video:
        final xfile = XFile(file.path, mimeType: file.mimeType);
        final length = await xfile.length();
        final mimeType = xfile.mimeType ?? lookupMimeType(xfile.path);
        final MatrixFile matrixFile;
        MatrixImageFile? thumbnail;
        // If file is a video, shrink it!
        if (PlatformInfos.isMobile &&
            mimeType != null &&
            mimeType.startsWith('video') &&
            length > minSizeToCompress) {
          matrixFile = await xfile.resizeVideo();
          thumbnail = await xfile.getVideoThumbnail();
        } else {
          // Else we just create a MatrixFile
          matrixFile = MatrixFile(
            bytes: await xfile.readAsBytes(),
            name: xfile.name,
            mimeType: xfile.mimeType,
          ).detectFileType;
        }
        room.sendFileEvent(
          matrixFile,
          thumbnail: thumbnail,
          shrinkImageMaxDimension: 1600,
        );
        break;
      case SharedMediaType.text:
        room.sendTextEvent(file.path);
        break;
      case SharedMediaType.url:
        room.sendTextEvent(file.path);
        break;
      case SharedMediaType.file:
        final localFile = File(file.path);
        final filename = file.path.split('/').last;
        final matrixFile = MatrixFile(
          bytes: localFile.readAsBytesSync(),
          name: filename,
          mimeType: file.mimeType ?? lookupMimeType(localFile.path),
        ).detectFileType;
        room.sendFileEvent(matrixFile);
        break;
    }
  }

  void selectRoom(Room room) {
    selectedRooms = [...selectedRooms, room];
    setState(() {});
  }

  void deselectRoom(Room room) {
    selectedRooms = [...selectedRooms.where((element) => element != room)];
    setState(() {});
  }

  void toogleRoom(Room room) {
    if (selectedRooms.contains(room)) {
      deselectRoom(room);
    } else {
      selectRoom(room);
    }
  }

  List<Room> selectedRooms = List.empty(growable: true);

  @override
  Widget build(BuildContext context) {
    final rooms = NomoMatrix.I.client.rooms
        .where(
          (room) =>
              room.canSendDefaultMessages && room.membership == Membership.join,
        )
        .toList();
    return ZeniqBackgroundWidget(
      child: Scaffold(
        appBar: AppBar(
          title: Text('Share Media'),
          leading: IconButton(
            icon: AnimatedCrossFade(
              firstChild: const Icon(Icons.close),
              secondChild: const Icon(Icons.arrow_back_ios_new),
              crossFadeState: currentPage == 0
                  ? CrossFadeState.showFirst
                  : CrossFadeState.showSecond,
              duration: const Duration(milliseconds: 300),
            ),
            onPressed: () async {
              if (currentPage == 0) {
                Navigator.of(context).pop();
                await Future.delayed(const Duration(milliseconds: 100));
                $ref.read(sharedFilesProvider.notifier).state = null;
              } else {
                pageController.previousPage(
                  duration: const Duration(
                    milliseconds: 350,
                  ),
                  curve: Curves.easeInOut,
                );
              }
            },
          ),
        ),
        body: PageView(
          physics: const NeverScrollableScrollPhysics(),
          controller: pageController,
          onPageChanged: (value) => setState(() {
            currentPage = value;
          }),
          children: [
            SelectRoomsWidget(
              rooms: rooms,
              selectedRooms: selectedRooms,
              toogleRoom: toogleRoom,
              sharedFile: widget.sharedFile,
            ),
            ShareMediaPreview(sharedFile: widget.sharedFile),
          ],
        ),
        bottomNavigationBar: Builder(
          builder: (context) {
            if (selectedRooms.isEmpty) {
              return const SizedBox.shrink();
            }
            return Padding(
              padding: const EdgeInsets.only(
                left: 20,
                right: 20,
                bottom: 20,
                top: 10,
              ),
              child: PrimaryNomoButton(
                text: currentPage == 0 ? 'Next' : 'Share',
                width: double.infinity,
                height: 50,
                onPressed: () {
                  if (currentPage == 0) {
                    pageController.nextPage(
                      duration: const Duration(
                        milliseconds: 350,
                      ),
                      curve: Curves.easeInOut,
                    );
                  } else {
                    shareFiles(context: context);
                  }
                },
              ),
            );
          },
        ),
      ),
    );
  }
}

class ShareMediaPreview extends StatelessWidget {
  const ShareMediaPreview({super.key, required this.sharedFile});

  final SharedMediaFile sharedFile;

  @override
  Widget build(BuildContext context) {
    switch (sharedFile.type) {
      case SharedMediaType.image:
        return Center(child: Image.file(File(sharedFile.path)));
      case SharedMediaType.video:
        return Center(child: VideoPreview(file: File(sharedFile.path)));
      case SharedMediaType.text:
        return NomoInput(
          initialText: sharedFile.path,
          style: context.typography.b3,
        );
      case SharedMediaType.url:
        return NomoInput(
          initialText: sharedFile.path,
          style: context.typography.b3,
        );
      case SharedMediaType.file:
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Center(child: FilePreview(file: File(sharedFile.path))),
        );
      default:
        return const SizedBox.shrink();
    }
  }
}

class SelectRoomsWidget extends StatefulWidget {
  const SelectRoomsWidget({
    super.key,
    required this.rooms,
    required this.selectedRooms,
    required this.toogleRoom,
    required this.sharedFile,
  });

  final List<Room> rooms;
  final List<Room> selectedRooms;
  final SharedMediaFile sharedFile;
  final Function(Room) toogleRoom;

  @override
  State<SelectRoomsWidget> createState() => _SelectRoomsWidgetState();
}

class _SelectRoomsWidgetState extends State<SelectRoomsWidget>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late List<NomoManifest> webonsWithReadMedia;

  @override
  void initState() {
    super.initState();
    final webons = getInstalledManifests();
    webonsWithReadMedia = webons.where((webon) {
      final rawJson = webon.raw_json;
      if (rawJson == null) {
        return false;
      }
      var accept_sharing = rawJson["accept_sharing"];
      if (accept_sharing == null) {
        return false;
      }
      return true;
    }).toList();

    _tabController =
        TabController(length: webonsWithReadMedia.isEmpty ? 1 : 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (webonsWithReadMedia.isNotEmpty)
          TabBar(
            controller: _tabController,
            tabs: [
              Tab(text: NomoText("Chat").text),
              Tab(text: NomoText("WebOns").text),
            ],
            dividerColor: Colors.transparent,
            indicator: UnderlineTabIndicator(
              borderSide: BorderSide(
                width: 2,
                color: Theme.of(context).colorScheme.primary,
              ),
              insets: EdgeInsets.symmetric(horizontal: -25.0),
            ),
          ),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildRoomsList(),
              if (webonsWithReadMedia.isNotEmpty) _buildWebOnsList(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildRoomsList() {
    return ListView.builder(
      itemCount: widget.rooms.length,
      itemBuilder: (context, index) {
        final room = widget.rooms[index];
        return SelectRoomWidget(
          room: room,
          isSelected: widget.selectedRooms.contains(room),
          onSelect: (room) => widget.toogleRoom(room),
        );
      },
    );
  }

  Widget _buildWebOnsList() {
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        childAspectRatio: 1,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: webonsWithReadMedia.length,
      itemBuilder: (context, index) {
        return webOnList(context, webonsWithReadMedia[index]);
      },
    );
  }

  Widget webOnList(BuildContext context, NomoManifest manifest) {
    return GestureDetector(
      onTap: () async {
        try {
          final file = File(widget.sharedFile.path);
          final bytes = await file.readAsBytes();
          final base64File = base64Encode(bytes);

          final fileData = {
            "data": base64File,
            "type": widget.sharedFile.type.value,
            "mimeType": widget.sharedFile.mimeType,
            "name": widget.sharedFile.path.split('/').last,
          };

          await webOnApi(
            functionName: "nomoSetItem",
            manifest: manifest,
            args: {
              "key": "sharedFile",
              "value": jsonEncode(fileData),
            },
            context: context,
          );

          await webOnApi(
            functionName: "nomoLaunchWebOn",
            manifest: manifest,
            args: {"manifest": manifest.toJson()},
            context: context,
          );
        } catch (e) {
          Logger.logError("Error sharing file: $e");
        }
      },
      child: WebOnIconWithName(
        manifest: manifest,
      ),
    );
  }
}

class WebOnIconWithName extends StatelessWidget {
  final NomoManifest manifest;
  const WebOnIconWithName({
    super.key,
    required this.manifest,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: AlignmentDirectional.bottomCenter,
      children: [
        Center(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: WebOnIcon(
                webOn: manifest,
              ),
            ),
          ),
        ),
        NomoText(
          manifest.webon_name,
          style: NomoTheme.of(context).typography.b1,
          textAlign: TextAlign.center,
          fit: true,
          maxLines: 1,
        ),
      ],
    );
  }
}

class SelectRoomWidget extends StatelessWidget {
  const SelectRoomWidget({
    super.key,
    required this.onSelect,
    required this.room,
    required this.isSelected,
  });

  final Function(Room) onSelect;
  final bool isSelected;
  final Room room;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: 8,
        vertical: 1,
      ),
      child: FutureBuilder(
        future: room.loadHeroUsers(),
        builder: (context, snapshot) => HoverBuilder(
          builder: (context, hovered) => ListTile(
            visualDensity: const VisualDensity(vertical: -0.5),
            contentPadding: const EdgeInsets.symmetric(horizontal: 8),
            leading: Avatar(
              mxContent: room.avatar,
              name: room.getLocalizedDisplayname(),
              presenceUserId: room.directChatMatrixID,
              isDirectChat: room.isDirectChat,
              isNoteToSelfe: room.name == "Note to self",
            ),
            title: Text(
              room.getLocalizedDisplayname(),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              softWrap: false,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Expanded(
                  flex: 5,
                  child: FutureBuilder<String>(
                    future: room.getLastMessage(context),
                    builder: (context, snapshot) {
                      return Opacity(
                        opacity: 0.5,
                        child: Text(
                          room.membership == Membership.invite
                              ? room.isDirectChat
                                  ? L10n.of(context)!.invitePrivateChat
                                  : L10n.of(context)!.inviteGroupChat
                              : snapshot.data ??
                                  room.lastEvent?.calcLocalizedBodyFallback(
                                    MatrixLocals(L10n.of(context)!),
                                    hideReply: true,
                                    hideEdit: true,
                                    plaintextBody: true,
                                    removeMarkdown: true,
                                    withSenderNamePrefix: !room.isDirectChat ||
                                        room.directChatMatrixID !=
                                            room.lastEvent?.senderId,
                                  ) ??
                                  L10n.of(context)!.emptyChat,
                          softWrap: false,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                            color:
                                Theme.of(context).colorScheme.onSurfaceVariant,
                            decoration: room.lastEvent?.redacted == true
                                ? TextDecoration.lineThrough
                                : null,
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
            onTap: () {
              onSelect(room);
            },
            trailing: Checkbox(
              value: isSelected,
              onChanged: (value) => onSelect(room),
            ),
          ),
        ),
      ),
    );
  }
}

class FilePreview extends StatelessWidget {
  final File file;

  const FilePreview({super.key, required this.file});

  @override
  Widget build(BuildContext context) {
    final filename = file.path.split('/').last;
    final filetype = filename.contains('.')
        ? filename.split('.').last.toUpperCase()
        : 'UNKNOWN';
    final sizeString = _formatFileSize(file.lengthSync());

    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border.all(),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Icon(
                  Icons.insert_drive_file,
                  color: Theme.of(context).textTheme.bodyLarge?.color,
                ),
                const SizedBox(width: 16),
                Flexible(
                  child: Text(
                    filename,
                    maxLines: 1,
                    style: TextStyle(
                      color: Theme.of(context).textTheme.bodyLarge?.color,
                      fontWeight: FontWeight.bold,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
          const Divider(height: 1),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8),
            child: Row(
              children: [
                Text(
                  filetype,
                  style: TextStyle(
                    color: Theme.of(context)
                        .textTheme
                        .bodyMedium
                        ?.color
                        ?.withOpacity(0.6),
                  ),
                ),
                const Spacer(),
                Text(
                  sizeString,
                  style: TextStyle(
                    color: Theme.of(context)
                        .textTheme
                        .bodyMedium
                        ?.color
                        ?.withOpacity(0.6),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _formatFileSize(int bytes) {
    if (bytes <= 0) return "0 B";
    const suffixes = ["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
    var i = (log(bytes) / log(1024)).floor();
    return '${(bytes / pow(1024, i)).toStringAsFixed(1)} ${suffixes[i]}';
  }
}

class VideoPreview extends StatefulWidget {
  const VideoPreview({super.key, required this.file});

  final File file;

  @override
  State<VideoPreview> createState() => _VideoPreviewState();
}

class _VideoPreviewState extends State<VideoPreview> {
  late VideoPlayerController _controller;
  late ChewieController _chewieController;

  @override
  void initState() {
    super.initState();
    _controller = VideoPlayerController.file(widget.file)
      ..initialize().then((_) {
        _chewieController = ChewieController(
          videoPlayerController: _controller,
          autoPlay: false,
          autoInitialize: true,
        );
        setState(() {});
      });
  }

  @override
  void dispose() {
    super.dispose();
    _controller.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_controller.value.isInitialized) {
      return AspectRatio(
        aspectRatio: _controller.value.aspectRatio,
        child: Chewie(
          controller: _chewieController,
        ),
      );
    }
    return const SizedBox.shrink();
  }
}
