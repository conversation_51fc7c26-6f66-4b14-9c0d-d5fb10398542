import 'dart:io';
import 'package:decentralized_messenger/pages/chat_details/settings_list_tile.dart';
import 'package:decentralized_messenger/pages/chat_details/settings_list_tile_textfield.dart';
import 'package:decentralized_messenger/pages/nomo_widget/details_container.dart';
import 'package:decentralized_messenger/pages/settings/change_avatar_action_tile.dart';
import 'package:decentralized_messenger/pages/settings/nomo_settings.dart';
import 'package:decentralized_messenger/utils/nomo_share.dart';
import 'package:decentralized_messenger/widgets/fluffy_chat_app.dart';
import 'package:decentralized_messenger/widgets/layouts/max_width_body.dart';
import 'package:file_picker/file_picker.dart';
import 'package:decentralized_messenger/widgets/avatar.dart';
import 'package:decentralized_messenger/widgets/nomo_matrix.dart';
import 'package:decentralized_messenger/widgets/nomo_custom_loading_dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/l10n.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:matrix/matrix.dart';
import 'package:nomo_app/common/platform/platform_info.dart';
import 'package:nomo_app/common/widgets/scaffold/zeniq_scaffold.dart';
import 'package:nomo_ui_kit/components/text/nomo_text.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:nomo_ui_kit/theme/nomo_theme.dart';

final profileProvider = StateNotifierProvider<ProfileNotifier, Profile?>((ref) {
  return ProfileNotifier()..loadProfile();
});

class ProfileNotifier extends StateNotifier<Profile?> {
  ProfileNotifier() : super(null);

  void updateProfile(Profile profile) {
    state = profile;
  }

  Future<void> loadProfile() async {
    final client = NomoMatrix.I.client;
    final profile = await client.getProfileFromUserId(
      client.userID!,
      maxCacheAge: Duration.zero,
    );
    updateProfile(profile);
  }

  Future<void> updateAvatarWithDialog(
      {required BuildContext context,
      required bool remove,
      ImageSource? source}) async {
    await showCustomFutureLoadingDialog(
      context: context,
      future: () => updateAvatar(remove: remove, source: source),
    );
  }

  Future<void> removeAvatar() async {
    final matrix = NomoMatrix.I;
    await matrix.client.setAvatar(null);
    await loadProfile();
  }

  Future<void> removeAvatarWithDialog({required BuildContext context}) async {
    await showCustomFutureLoadingDialog(
      context: context,
      future: () => removeAvatar(),
    );
  }

  Future<void> updateAvatar(
      {required bool remove, required ImageSource? source}) async {
    if (remove) {
      await removeAvatar();
      return;
    }

    if (source == null) {
      throw ('No source provided');
    }

    MatrixFile? file;
    if (PlatformInfo.isMobile) {
      final result = await ImagePicker().pickImage(
        source: source,
        imageQuality: 50,
      );
      if (result == null) return;

      file = MatrixFile(
        bytes: await result.readAsBytes(),
        name: result.path,
      );
    } else if (PlatformInfo.isDesktop) {
      final FilePickerResult? result =
          await FilePicker.platform.pickFiles(type: FileType.image);
      if (result == null) return;
      final File tempFile = File(result.files.single.path!);
      final bytes = await tempFile.readAsBytes();
      file = MatrixFile(
        bytes: bytes,
        name: result.files.single.name,
      );
    }
    final matrix = NomoMatrix.I;
    await matrix.client.setAvatar(file);
    await loadProfile();
  }

  Future<void> updateDisplayname(String newDisplayname) async {
    final client = NomoMatrix.I.client;
    await client.setDisplayName(
      client.userID!,
      newDisplayname,
    );
    await loadProfile();
  }

  void updateDisplaynameWithDialog(
      {required BuildContext context, required String newDisplayname}) async {
    await showCustomFutureLoadingDialog(
        context: context, future: () => updateDisplayname(newDisplayname));
  }
}

class NomoSettingsAccountView extends ConsumerWidget {
  final NomoSettingsAccountController controller;

  const NomoSettingsAccountView(this.controller, {super.key});

  @override
  Widget build(BuildContext context, ref) {
    final groups =
        NomoMatrix.I.client.rooms.where((r) => !r.isDirectChat).toList();

    final client = NomoMatrix.IorNull?.client;
    final identityKey = client?.identityKey;
    final deviceId = client?.deviceID;

    final profile = ref.watch(profileProvider);
    return ZeniqScaffold(
      title: NomoText("my_profile").text,
      leading: const BackButton(),
      body: MaxWidthBody(
        withScrolling: false,
        child: ListView(
          physics: const BouncingScrollPhysics(
              parent: AlwaysScrollableScrollPhysics()),
          shrinkWrap: true,
          children: [
            const SizedBox(height: 20),
            Center(
              child: Stack(
                children: [
                  Avatar(
                    mxContent: profile?.avatarUrl,
                    name: profile?.displayName,
                    size: Avatar.defaultSize * 2.5,
                    fontSize: 18 * 2.5,
                  ),
                  Positioned(
                    bottom: 0,
                    right: 0,
                    child: FloatingActionButton.small(
                      onPressed: () async {
                        await showModalBottomSheet(
                          shape: const RoundedRectangleBorder(
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(
                                  8), // Adjust the radius as needed
                              topRight: Radius.circular(
                                  8), // Adjust the radius as needed
                            ),
                          ),
                          context: context,
                          builder: (context) => avatarBottomSheet(context, ref),
                        );
                      },
                      heroTag: null,
                      child: const Icon(
                        Icons.camera_alt_outlined,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 20),
            DetailsContainer(
              children: [
                DisplaynameTextfield(displayName: profile?.displayName),
                SettingsListTile(
                  leadingIcon: Icon(Icons.copy_outlined),
                  onTap: () {
                    NomoShare.share(
                      'https://nomo.id/${NomoMatrix.I.client.userID!.replaceAll(':zeniq.chat', '')}',
                      context,
                      copyOnly: true,
                    );
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(L10n.of(context)!.copiedToClipboard),
                      ),
                    );
                  },
                  title: "Messenger Address",
                  subtitle: 'https://nomo.id/${NomoMatrix.I.client.userID!}'
                      .replaceAll('@', '')
                      .replaceAll(':zeniq.chat', ''),
                ),
                SettingsListTile(
                  leadingIcon: Icon(Icons.key_sharp),
                  title: "Identity Pubkey",
                  subtitle: (identityKey ?? ""),
                ),
                SettingsListTile(
                  leadingIcon: Icon(Icons.device_hub),
                  title: "Device ID",
                  subtitle: (deviceId ?? ""),
                ),
              ],
            ),
            const SizedBox(height: 40),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: NomoText(
                L10n.of(context)!.settings,
                style: context.typography.b3,
                opacity: 0.5,
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            DetailsContainer(
              children: [
                //TODO
                // SettingsListTile(
                //   title: L10n.of(context)!.chat,
                //   leadingIcon: Icon(
                //     Icons.forum_outlined,
                //   ),
                //   onTap: () {
                //     context.go('/rooms/settings/chat');
                //   },
                //   trailingIcon: Icon(Icons.chevron_right_outlined),
                // ),
                // SettingsListTile(
                //   title: L10n.of(context)!.notifications,
                //   leadingIcon: Icon(
                //     Icons.notifications_outlined,
                //   ),
                //   onTap: () {
                //     context.go('/rooms/settings/notifications');
                //   },
                //   trailingIcon: Icon(Icons.chevron_right_outlined),
                // ),
                SettingsListTile(
                  title: "connected_devices",
                  leadingIcon: Icon(
                    Icons.devices_rounded,
                  ),
                  onTap: () {
                    context.go('/rooms/settings/devices');
                  },
                  trailingIcon: Icon(Icons.chevron_right_outlined),
                ),
                SettingsListTile(
                  title: L10n.of(context)!.blockedUsers,
                  leadingIcon: Icon(
                    Icons.block_sharp,
                  ),
                  onTap: () {
                    context.go('/rooms/settings/ignorelist');
                  },
                  trailingIcon: Icon(Icons.chevron_right_outlined),
                ),
              ],
            ),
            if (groups.isNotEmpty) ...[
              const SizedBox(height: 40),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: NomoText(
                  "my_groups",
                  style: context.typography.b3,
                  opacity: 0.5,
                ),
              ),
              const SizedBox(
                height: 10,
              ),
              DetailsContainer(
                children: [
                  for (final group in groups)
                    FutureBuilder(
                      future: group.loadHeroUsers(),
                      builder: (context, snapshot) {
                        final isMuted =
                            group.pushRuleState != PushRuleState.notify;
                        return SettingsListTile(
                          title: group.name,
                          leadingIcon: Avatar(
                            mxContent: group.avatar,
                            name: group.name,
                            isDirectChat: false,
                          ),
                          trailingIcon: isMuted
                              ? Icon(
                                  Icons.volume_off_outlined,
                                  size: 20,
                                  color: context.theme.colors.foreground1
                                      .withOpacity(.5),
                                )
                              : null,
                          onTap: () => context.push('/rooms/${group.id}'),
                        );
                      },
                    ),
                ],
              ),
              const SizedBox(height: 100),
            ],
          ],
        ),
      ),
    );
  }

  Widget avatarBottomSheet(BuildContext context, WidgetRef ref) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        const SizedBox(
          height: 12,
        ),
        NomoText(
          "change_avatar",
          style: context.theme.typography.h3,
        ),
        const SizedBox(
          height: 18,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            if (PlatformInfo.isMobile)
              ChangeAvatarActionTile(
                title: "camera",
                icon: Icon(Icons.camera_alt_outlined),
                onPressed: () {
                  ref.read(profileProvider.notifier).updateAvatarWithDialog(
                      context: context,
                      remove: false,
                      source: ImageSource.camera);
                  Navigator.of(context).pop();
                },
              ),
            ChangeAvatarActionTile(
              title: "gallery",
              icon: Icon(Icons.image_outlined),
              onPressed: () {
                ref.read(profileProvider.notifier).updateAvatarWithDialog(
                    context: context,
                    remove: false,
                    source: ImageSource.gallery);
                Navigator.of(context).pop();
              },
            ),
            ChangeAvatarActionTile(
              title: "remove",
              icon: Icon(
                Icons.delete_outline_rounded,
                color: Colors.red,
              ),
              onPressed: () async {
                ref
                    .read(profileProvider.notifier)
                    .updateAvatarWithDialog(context: context, remove: true);
                Navigator.pop(context);
              },
            )
          ],
        ),
        const SizedBox(
          height: 12,
        )
      ],
    );
  }
}

class DisplaynameTextfield extends StatefulWidget {
  const DisplaynameTextfield({
    super.key,
    required this.displayName,
  });

  final String? displayName;

  @override
  State<DisplaynameTextfield> createState() => _DisplaynameTextfieldState();
}

class _DisplaynameTextfieldState extends State<DisplaynameTextfield> {
  String? input;
  late FocusNode focusNode;

  @override
  void initState() {
    focusNode = FocusNode();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.displayName == null) {
      return const SizedBox(
        width: double.infinity,
        height: 50,
        child: Center(child: CircularProgressIndicator.adaptive()),
      );
    }
    input ??= widget.displayName;
    final hasChanged =
        input != widget.displayName && input != null && input!.isNotEmpty;
    return SettingsListTileTextfield(
      focusNode: focusNode,
      leadingIcon: Icon(Icons.person_2_rounded),
      initialText: widget.displayName,
      hintText: L10n.of(context)!.editDisplayname,
      onChanged: (text) {
        setState(() {
          input = text;
        });
      },
      trailingIcon: hasChanged
          ? IconButton(
              onPressed: () {
                if (input == null || input!.isEmpty) return;
                $ref.read(profileProvider.notifier).updateDisplaynameWithDialog(
                    context: context, newDisplayname: input!.trim());
              },
              icon: const Icon(Icons.check_rounded))
          : null,
    );
  }
}
