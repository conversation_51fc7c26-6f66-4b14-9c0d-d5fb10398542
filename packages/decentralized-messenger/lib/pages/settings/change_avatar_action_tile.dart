import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:nomo_ui_kit/components/text/nomo_text.dart';
import 'package:nomo_ui_kit/theme/nomo_theme.dart';

class ChangeAvatarActionTile extends ConsumerWidget {
  final String title;
  final Icon icon;
  final Function() onPressed;
  const ChangeAvatarActionTile(
      {required this.title,
      required this.icon,
      required this.onPressed,
      super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return InkWell(
      borderRadius: BorderRadius.circular(40),
      onTap: onPressed,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            icon,
            const SizedBox(
              height: 4,
            ),
            NomoText(
              title,
              style: NomoTheme.of(context).typography.b2,
              opacity: 0.6,
            ),
          ],
        ),
      ),
    );
  }
}
