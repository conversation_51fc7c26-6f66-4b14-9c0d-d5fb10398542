import 'package:decentralized_messenger/pages/nomo_api/wallet_info.dart';
import 'package:decentralized_messenger/pages/nomo_provider/login_provider.dart';
import 'package:decentralized_messenger/pages/nomo_provider/wallet_addresses_provider.dart';
import 'package:decentralized_messenger/widgets/nomo_matrix.dart';
import 'package:flutter/material.dart';

import 'package:collection/collection.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:matrix/matrix.dart';
import 'package:nomo_app/common/blockchain/network/wallet_info.dart';
//import 'package:nomo_ui_kit/theme/sub/nomo_color_theme.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'package:decentralized_messenger/config/app_config.dart';
import 'package:decentralized_messenger/utils/client_manager.dart';
import 'package:decentralized_messenger/utils/platform_infos.dart';
import 'config/setting_keys.dart';
import 'widgets/fluffy_chat_app.dart';

bool get SEND_ASSETS_ENABLED {
  return true;
}

bool get WALLET_NAVIGATION_ENABLED {
  return false;
}

bool get SEND_ASSETS_VIA_APP {
  return false;
}

void main() async {
  final chatInit = await initChat();

  // Started in foreground mode.
  Logs().i(
    '${AppConfig.applicationName} started in foreground mode. Rendering GUI...',
  );
  if (chatInit != null) {
    await startGui(chatInit.clients, chatInit.prefs);
  }
}

class ChatInitModel {
  final List<Client> clients;
  final SharedPreferences prefs;

  ChatInitModel({required this.clients, required this.prefs});
}

Future<ChatInitModel?> initChat() async {
  Logs().i('Welcome to ${AppConfig.applicationName} <3');

  // Our background push shared isolate accesses flutter-internal things very early in the startup proccess
  // To make sure that the parts of flutter needed are started up already, we need to ensure that the
  // widget bindings are initialized already.
  WidgetsFlutterBinding.ensureInitialized();

  Logs().nativeColors = !PlatformInfos.isIOS;
  final store = await SharedPreferences.getInstance();
  final clients = await ClientManager.getClients(store: store);

  // If the app starts in detached mode, we assume that it is in
  // background fetch mode for processing push notifications. This is
  // currently only supported on Android.
  if (PlatformInfos.isAndroid &&
      AppLifecycleState.detached == WidgetsBinding.instance.lifecycleState) {
    // Do not send online presences when app is in background fetch mode.
    for (final client in clients) {
      client.syncPresence = PresenceType.offline;
    }

    // To start the flutter engine afterwards we add an custom observer.
    WidgetsBinding.instance.addObserver(AppStarter(clients, store));
    Logs().i(
      '${AppConfig.applicationName} started in background-fetch mode. No GUI will be created unless the app is no longer detached.',
    );
    return null;
  }
  // Preload first client
  final firstClient = clients.firstOrNull;
  await firstClient?.roomsLoading;
  await firstClient?.accountDataLoading;
  NomoMatrix.init(clients: clients, store: store);
  return ChatInitModel(clients: clients, prefs: store);
}

/// Fetch the pincode for the applock and start the flutter engine.
Future<void> startGui(List<Client> clients, SharedPreferences store) async {
  // Fetch the pin for the applock if existing for mobile applications.
  String? pin;
  if (PlatformInfos.isMobile) {
    try {
      pin =
          await const FlutterSecureStorage().read(key: SettingKeys.appLockKey);
    } catch (e, s) {
      Logs().d('Unable to read PIN from Secure storage', e, s);
    }
  }

  // Preload first client
  final firstClient = clients.firstOrNull;
  await firstClient?.roomsLoading;
  await firstClient?.accountDataLoading;
  // await initNomoTheme();
  // await WebonKitDart.registerOnWebOnVisible(callBack: initAppState);

  runApp(
    FluffyChatApp(
      pincode: pin,
    ),
  );
}

final invitationLink = StateProvider<String?>((ref) => null);

// Future<void> initNomoTheme() async {
//   final theme = await WebonKitDart.getCurrentAppTheme();
//   $ref.read(themeProvider.notifier).state = theme;
// }

Future<void> initWalletAddresses() async {
  if (NomoMatrix.IorNull != null) {
    await WalletInfo
        .waitForAllAddresses; // wait for UTXO addresses to be available
    final nomoWallet = await nomoInterface.getWalletInfo();
    final chatWallet = ChatWalletInfo.fromJson(nomoWallet["walletAddresses"]);
    $ref.read(walletAddressesProvider.notifier).set(chatWallet);
  }
}

// Future<void> initAppState(mode) async {
//   initNomoTheme();
//   initInviteLink();
//   initWalletAddresses();
// }

/// Watches the lifecycle changes to start the application when it
/// is no longer detached.
class AppStarter with WidgetsBindingObserver {
  final List<Client> clients;
  final SharedPreferences store;
  bool guiStarted = false;

  AppStarter(this.clients, this.store);

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (guiStarted) return;
    if (state == AppLifecycleState.detached) return;

    Logs().i(
      '${AppConfig.applicationName} switches from the detached background-fetch mode to ${state.name} mode. Rendering GUI...',
    );
    // Switching to foreground mode needs to reenable send online sync presence.
    for (final client in clients) {
      client.syncPresence = PresenceType.online;
    }
    startGui(clients, store);
    // We must make sure that the GUI is only started once.
    guiStarted = true;
  }
}
