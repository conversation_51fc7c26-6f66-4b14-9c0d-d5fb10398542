import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:walletkit_dart/walletkit_dart.dart';
import 'package:web3dart/web3dart.dart';

class NomoInterface {
  static NomoInterface? _instance;

  static void init(NomoInterface nomoInterface) {
    if (_instance != null) {
      throw Exception("ChatInterface already initialized");
    }
    _instance = nomoInterface;
    if (kDebugMode) {
      print("ChatInterface successfully initialized");
    }
  }

  static NomoInterface get I {
    if (_instance == null) {
      throw Exception("ChatInterface not initialized");
    }
    return _instance!;
  }

  final Future<Credentials> Function() getChatCredentials;
  final String Function(Credentials credentials, String dataToSign) signEvm;
  final Future<dynamic> Function(String key) getLocalStorageItem;
  final void Function(String key, String value) setLocalStorageItem;
  final Future<String?> Function() getWalletName;
  final Future<Map<String, dynamic>> Function({
    required String recipient,
    required CoinEntity token,
  }) sendAssets;
  final Future<CoinEntity?> Function(BuildContext context)
      selectAssetFromDialog;
  final void Function(String symbol, [String? network]) navigateToWallet;
  final void Function(String webonLink) installWebOn;
  final List<Map<String, dynamic>> Function() getInstalledWebOns;
  final Future<Map<String, dynamic>> Function() getWalletInfo;
  final void Function({
    required String title,
    required String body,
    required String channelName,
    int? id,
    String? payload,
  }) showLocalNotification;
  final void Function(int id) cancelLocalNotification;
  final OverlayState? Function() getMainOverlayState;

  NomoInterface({
    required this.getChatCredentials,
    required this.signEvm,
    required this.getLocalStorageItem,
    required this.setLocalStorageItem,
    required this.getWalletName,
    required this.sendAssets,
    required this.navigateToWallet,
    required this.installWebOn,
    required this.getInstalledWebOns,
    required this.getWalletInfo,
    required this.showLocalNotification,
    required this.selectAssetFromDialog,
    required this.cancelLocalNotification,
    required this.getMainOverlayState,
  }) {
    if (kDebugMode) {
      print("ChatInterface instance created");
    }
  }
}
