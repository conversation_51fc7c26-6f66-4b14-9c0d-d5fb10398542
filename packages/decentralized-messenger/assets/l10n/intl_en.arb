{"@@locale": "en", "@@last_modified": "2021-08-14 12:38:37.885451", "repeatPassword": "Repeat password", "@repeatPassword": {}, "notAnImage": "Not an image file.", "remove": "Remove", "importNow": "Import now", "importEmojis": "Import Emojis", "importFromZipFile": "Import from .zip file", "exportEmotePack": "Export Emote pack as .zip", "replace": "Replace", "about": "About", "@about": {"type": "text", "placeholders": {}}, "accept": "Accept", "@accept": {"type": "text", "placeholders": {}}, "acceptedTheInvitation": "👍 {username} accepted the invitation", "@acceptedTheInvitation": {"type": "text", "placeholders": {"username": {}}}, "account": "Account", "@account": {"type": "text", "placeholders": {}}, "activatedEndToEndEncryption": "🔐 {username} activated end to end encryption", "@activatedEndToEndEncryption": {"type": "text", "placeholders": {"username": {}}}, "addEmail": "Add email", "@addEmail": {"type": "text", "placeholders": {}}, "confirmMatrixId": "Please confirm your Matrix ID in order to delete your account.", "@confirmMatrixId": {}, "supposedMxid": "This should be {mxid}", "@supposedMxid": {"type": "text", "placeholders": {"mxid": {}}}, "addChatDescription": "Add a chat description...", "addToSpace": "Add to space", "@addToSpace": {}, "admin": "Admin", "@admin": {"type": "text", "placeholders": {}}, "alias": "alias", "@alias": {"type": "text", "placeholders": {}}, "all": "All", "@all": {"type": "text", "placeholders": {}}, "allChats": "All chats", "@allChats": {"type": "text", "placeholders": {}}, "commandHint_googly": "Send some googly eyes", "commandHint_cuddle": "Send a cuddle", "commandHint_hug": "Send a hug", "googlyEyesContent": "{sender<PERSON>ame} sends you googly eyes", "@googlyEyesContent": {"type": "text", "placeholders": {"senderName": {}}}, "cuddleContent": "{sender<PERSON>ame} cuddles you", "@cuddleContent": {"type": "text", "placeholders": {"senderName": {}}}, "hugContent": "{sender<PERSON><PERSON>} hugs you", "@hugContent": {"type": "text", "placeholders": {"senderName": {}}}, "answeredTheCall": "{sender<PERSON><PERSON>} answered the call", "@answeredTheCall": {"type": "text", "placeholders": {"senderName": {}}}, "anyoneCanJoin": "Anyone can join", "@anyoneCanJoin": {"type": "text", "placeholders": {}}, "appLock": "App lock", "@appLock": {"type": "text", "placeholders": {}}, "archive": "Archive", "@archive": {"type": "text", "placeholders": {}}, "areGuestsAllowedToJoin": "Are guest users allowed to join", "@areGuestsAllowedToJoin": {"type": "text", "placeholders": {}}, "areYouSure": "Are you sure?", "@areYouSure": {"type": "text", "placeholders": {}}, "areYouSureYouWantToLogout": "Are you sure you want to log out?", "@areYouSureYouWantToLogout": {"type": "text", "placeholders": {}}, "askSSSSSign": "To be able to sign the other person, please enter your secure store passphrase or recovery key.", "@askSSSSSign": {"type": "text", "placeholders": {}}, "askVerificationRequest": "Accept this verification request from {username}?", "@askVerificationRequest": {"type": "text", "placeholders": {"username": {}}}, "autoplayImages": "Automatically play animated stickers and emotes", "@autoplayImages": {"type": "text", "placeholder": {}}, "badServerLoginTypesException": "The homeserver supports the login types:\n{serverVersions}\nBut this app supports only:\n{supportedVersions}", "@badServerLoginTypesException": {"type": "text", "placeholders": {"serverVersions": {}, "supportedVersions": {}}}, "sendTypingNotifications": "Send typing notifications", "@sendTypingNotifications": {}, "sendOnEnter": "Send on enter", "@sendOnEnter": {}, "badServerVersionsException": "The homeserver supports the Spec versions:\n{serverVersions}\nBut this app supports only {supportedVersions}", "@badServerVersionsException": {"type": "text", "placeholders": {"serverVersions": {}, "supportedVersions": {}}}, "banFromChat": "Ban from chat", "@banFromChat": {"type": "text", "placeholders": {}}, "banned": "Banned", "@banned": {"type": "text", "placeholders": {}}, "bannedUser": "{username} banned {targetName}", "@bannedUser": {"type": "text", "placeholders": {"username": {}, "targetName": {}}}, "blockDevice": "Block Device", "@blockDevice": {"type": "text", "placeholders": {}}, "blocked": "Blocked", "@blocked": {"type": "text", "placeholders": {}}, "botMessages": "Bot messages", "@botMessages": {"type": "text", "placeholders": {}}, "cancel": "Cancel", "@cancel": {"type": "text", "placeholders": {}}, "cantOpenUri": "Can't open the URI {uri}", "@cantOpenUri": {"type": "text", "placeholders": {"uri": {}}}, "changeDeviceName": "Change device name", "@changeDeviceName": {"type": "text", "placeholders": {}}, "changedTheChatAvatar": "{username} changed the chat avatar", "@changedTheChatAvatar": {"type": "text", "placeholders": {"username": {}}}, "changedTheChatDescriptionTo": "{username} changed the chat description to: '{description}'", "@changedTheChatDescriptionTo": {"type": "text", "placeholders": {"username": {}, "description": {}}}, "changedTheChatNameTo": "{username} changed the chat name to: '{chatname}'", "@changedTheChatNameTo": {"type": "text", "placeholders": {"username": {}, "chatname": {}}}, "changedTheChatPermissions": "{username} changed the chat permissions", "@changedTheChatPermissions": {"type": "text", "placeholders": {"username": {}}}, "changedTheDisplaynameTo": "{username} changed their displayname to: '{displayname}'", "@changedTheDisplaynameTo": {"type": "text", "placeholders": {"username": {}, "displayname": {}}}, "changedTheGuestAccessRules": "{username} changed the guest access rules", "@changedTheGuestAccessRules": {"type": "text", "placeholders": {"username": {}}}, "changedTheGuestAccessRulesTo": "{username} changed the guest access rules to: {rules}", "@changedTheGuestAccessRulesTo": {"type": "text", "placeholders": {"username": {}, "rules": {}}}, "changedTheHistoryVisibility": "{username} changed the history visibility", "@changedTheHistoryVisibility": {"type": "text", "placeholders": {"username": {}}}, "changedTheHistoryVisibilityTo": "{username} changed the history visibility to: {rules}", "@changedTheHistoryVisibilityTo": {"type": "text", "placeholders": {"username": {}, "rules": {}}}, "changedTheJoinRules": "{username} changed the join rules", "@changedTheJoinRules": {"type": "text", "placeholders": {"username": {}}}, "changedTheJoinRulesTo": "{username} changed the join rules to: {joinRules}", "@changedTheJoinRulesTo": {"type": "text", "placeholders": {"username": {}, "joinRules": {}}}, "changedTheProfileAvatar": "{username} changed their avatar", "@changedTheProfileAvatar": {"type": "text", "placeholders": {"username": {}}}, "changedTheRoomAliases": "{username} changed the room aliases", "@changedTheRoomAliases": {"type": "text", "placeholders": {"username": {}}}, "changedTheRoomInvitationLink": "{username} changed the invitation link", "@changedTheRoomInvitationLink": {"type": "text", "placeholders": {"username": {}}}, "changePassword": "Change password", "@changePassword": {"type": "text", "placeholders": {}}, "changeTheHomeserver": "Change the homeserver", "@changeTheHomeserver": {"type": "text", "placeholders": {}}, "changeTheme": "Change your style", "@changeTheme": {"type": "text", "placeholders": {}}, "changeTheNameOfTheGroup": "Change the name of the group", "@changeTheNameOfTheGroup": {"type": "text", "placeholders": {}}, "changeYourAvatar": "Change your avatar", "@changeYourAvatar": {"type": "text", "placeholders": {}}, "channelCorruptedDecryptError": "The encryption has been corrupted", "@channelCorruptedDecryptError": {"type": "text", "placeholders": {}}, "chat": "Cha<PERSON>", "@chat": {"type": "text", "placeholders": {}}, "yourChatBackupHasBeenSetUp": "Your chat backup has been set up.", "@yourChatBackupHasBeenSetUp": {}, "chatBackup": "Chat backup", "@chatBackup": {"type": "text", "placeholders": {}}, "chatBackupDescription": "Your old messages are secured with a recovery key. Please make sure you don't lose it.", "@chatBackupDescription": {"type": "text", "placeholders": {}}, "chatDetails": "Chat details", "@chatDetails": {"type": "text", "placeholders": {}}, "chatHasBeenAddedToThisSpace": "Chat has been added to this space", "@chatHasBeenAddedToThisSpace": {}, "chats": "Chats", "@chats": {"type": "text", "placeholders": {}}, "chooseAStrongPassword": "Choose a strong password", "@chooseAStrongPassword": {"type": "text", "placeholders": {}}, "clearArchive": "Clear archive", "@clearArchive": {}, "close": "Close", "@close": {"type": "text", "placeholders": {}}, "commandHint_markasdm": "<PERSON> as direct message room for the giving Matrix ID", "@commandHint_markasdm": {}, "commandHint_markasgroup": "<PERSON> as group", "@commandHint_markasgroup": {}, "commandHint_ban": "Ban the given user from this room", "@commandHint_ban": {"type": "text", "description": "Usage hint for the command /ban"}, "commandHint_clearcache": "Clear cache", "@commandHint_clearcache": {"type": "text", "description": "Usage hint for the command /clearcache"}, "commandHint_create": "Create an empty group chat\nUse --no-encryption to disable encryption", "@commandHint_create": {"type": "text", "description": "Usage hint for the command /create"}, "commandHint_discardsession": "Discard session", "@commandHint_discardsession": {"type": "text", "description": "Usage hint for the command /discardsession"}, "commandHint_dm": "Start a direct chat\nUse --no-encryption to disable encryption", "@commandHint_dm": {"type": "text", "description": "Usage hint for the command /dm"}, "commandHint_html": "Send HTML-formatted text", "@commandHint_html": {"type": "text", "description": "Usage hint for the command /html"}, "commandHint_invite": "Invite the given user to this room", "@commandHint_invite": {"type": "text", "description": "Usage hint for the command /invite"}, "commandHint_join": "Join the given room", "@commandHint_join": {"type": "text", "description": "Usage hint for the command /join"}, "commandHint_kick": "Remove the given user from this room", "@commandHint_kick": {"type": "text", "description": "Usage hint for the command /kick"}, "commandHint_leave": "Leave this room", "@commandHint_leave": {"type": "text", "description": "Usage hint for the command /leave"}, "commandHint_me": "Describe yourself", "@commandHint_me": {"type": "text", "description": "Usage hint for the command /me"}, "commandHint_myroomavatar": "Set your picture for this room (by mxc-uri)", "@commandHint_myroomavatar": {"type": "text", "description": "Usage hint for the command /myroomavatar"}, "commandHint_myroomnick": "Set your display name for this room", "@commandHint_myroomnick": {"type": "text", "description": "Usage hint for the command /myroomnick"}, "commandHint_op": "Set the given user's power level (default: 50)", "@commandHint_op": {"type": "text", "description": "Usage hint for the command /op"}, "commandHint_plain": "Send unformatted text", "@commandHint_plain": {"type": "text", "description": "Usage hint for the command /plain"}, "commandHint_react": "Send reply as a reaction", "@commandHint_react": {"type": "text", "description": "Usage hint for the command /react"}, "commandHint_send": "Send text", "@commandHint_send": {"type": "text", "description": "Usage hint for the command /send"}, "commandHint_unban": "Unban the given user from this room", "@commandHint_unban": {"type": "text", "description": "Usage hint for the command /unban"}, "commandInvalid": "Command invalid", "@commandInvalid": {"type": "text"}, "commandMissing": "{command} is not a command.", "@commandMissing": {"type": "text", "placeholders": {"command": {}}, "description": "State that {command} is not a valid /command."}, "compareEmojiMatch": "Please compare the emojis", "@compareEmojiMatch": {"type": "text", "placeholders": {}}, "compareNumbersMatch": "Please compare the numbers", "@compareNumbersMatch": {"type": "text", "placeholders": {}}, "configureChat": "Configure chat", "@configureChat": {"type": "text", "placeholders": {}}, "confirm": "Confirm", "@confirm": {"type": "text", "placeholders": {}}, "connect": "Connect", "@connect": {"type": "text", "placeholders": {}}, "contactHasBeenInvitedToTheGroup": "<PERSON> has been invited to the group", "@contactHasBeenInvitedToTheGroup": {"type": "text", "placeholders": {}}, "containsDisplayName": "Contains display name", "@containsDisplayName": {"type": "text", "placeholders": {}}, "containsUserName": "Contains username", "@containsUserName": {"type": "text", "placeholders": {}}, "contentHasBeenReported": "The content has been reported to the server admins", "@contentHasBeenReported": {"type": "text", "placeholders": {}}, "copiedToClipboard": "Copied to clipboard", "@copiedToClipboard": {"type": "text", "placeholders": {}}, "copy": "Copy", "@copy": {"type": "text", "placeholders": {}}, "copyToClipboard": "Copy to clipboard", "@copyToClipboard": {"type": "text", "placeholders": {}}, "couldNotDecryptMessage": "Could not decrypt message: {error}", "@couldNotDecryptMessage": {"type": "text", "placeholders": {"error": {}}}, "countParticipants": "{count} participants", "@countParticipants": {"type": "text", "placeholders": {"count": {}}}, "create": "Create", "@create": {"type": "text", "placeholders": {}}, "createdTheChat": "💬 {username} created the chat", "@createdTheChat": {"type": "text", "placeholders": {"username": {}}}, "createGroup": "Create group", "createNewSpace": "New space", "@createNewSpace": {"type": "text", "placeholders": {}}, "currentlyActive": "Currently active", "@currentlyActive": {"type": "text", "placeholders": {}}, "darkTheme": "Dark", "@darkTheme": {"type": "text", "placeholders": {}}, "dateAndTimeOfDay": "{date}, {timeOfDay}", "@dateAndTimeOfDay": {"type": "text", "placeholders": {"date": {}, "timeOfDay": {}}}, "dateWithoutYear": "{month}-{day}", "@dateWithoutYear": {"type": "text", "placeholders": {"month": {}, "day": {}}}, "dateWithYear": "{year}-{month}-{day}", "@dateWithYear": {"type": "text", "placeholders": {"year": {}, "month": {}, "day": {}}}, "deactivateAccountWarning": "This will deactivate your user account. This can not be undone! Are you sure?", "@deactivateAccountWarning": {"type": "text", "placeholders": {}}, "defaultPermissionLevel": "Default permission level", "@defaultPermissionLevel": {"type": "text", "placeholders": {}}, "delete": "Delete", "@delete": {"type": "text", "placeholders": {}}, "deleteAccount": "Delete account", "@deleteAccount": {"type": "text", "placeholders": {}}, "deleteMessage": "Delete message", "@deleteMessage": {"type": "text", "placeholders": {}}, "device": "<PERSON><PERSON>", "@device": {"type": "text", "placeholders": {}}, "deviceId": "Device ID", "@deviceId": {"type": "text", "placeholders": {}}, "devices": "Devices", "@devices": {"type": "text", "placeholders": {}}, "directChats": "Direct Chats", "@directChats": {"type": "text", "placeholders": {}}, "allRooms": "All Group Chats", "@allRooms": {"type": "text", "placeholders": {}}, "displaynameHasBeenChanged": "Displayname has been changed", "@displaynameHasBeenChanged": {"type": "text", "placeholders": {}}, "downloadFile": "Download file", "@downloadFile": {"type": "text", "placeholders": {}}, "edit": "Edit", "@edit": {"type": "text", "placeholders": {}}, "editBlockedServers": "Edit blocked servers", "@editBlockedServers": {"type": "text", "placeholders": {}}, "chatPermissions": "Chat permissions", "editDisplayname": "Edit displayname", "@editDisplayname": {"type": "text", "placeholders": {}}, "editRoomAliases": "Edit room aliases", "@editRoomAliases": {"type": "text", "placeholders": {}}, "editRoomAvatar": "Edit room avatar", "@editRoomAvatar": {"type": "text", "placeholders": {}}, "emoteExists": "Emote already exists!", "@emoteExists": {"type": "text", "placeholders": {}}, "emoteInvalid": "Invalid emote shortcode!", "@emoteInvalid": {"type": "text", "placeholders": {}}, "emoteKeyboardNoRecents": "Recently-used emotes will appear here...", "@emoteKeyboardNoRecents": {"type": "text", "placeholders": {}}, "emotePacks": "Emote packs for room", "@emotePacks": {"type": "text", "placeholders": {}}, "emoteSettings": "Emote Settings", "@emoteSettings": {"type": "text", "placeholders": {}}, "emoteShortcode": "Emote shortcode", "@emoteShortcode": {"type": "text", "placeholders": {}}, "emoteWarnNeedToPick": "You need to pick an emote shortcode and an image!", "@emoteWarnNeedToPick": {"type": "text", "placeholders": {}}, "emptyChat": "Empty chat", "@emptyChat": {"type": "text", "placeholders": {}}, "enableEmotesGlobally": "Enable emote pack globally", "@enableEmotesGlobally": {"type": "text", "placeholders": {}}, "enableEncryption": "Enable encryption", "@enableEncryption": {"type": "text", "placeholders": {}}, "enableEncryptionWarning": "You won't be able to disable the encryption anymore. Are you sure?", "@enableEncryptionWarning": {"type": "text", "placeholders": {}}, "encrypted": "Encrypted", "@encrypted": {"type": "text", "placeholders": {}}, "encryption": "Encryption", "@encryption": {"type": "text", "placeholders": {}}, "encryptionNotEnabled": "Encryption is not enabled", "@encryptionNotEnabled": {"type": "text", "placeholders": {}}, "endedTheCall": "{sender<PERSON><PERSON>} ended the call", "@endedTheCall": {"type": "text", "placeholders": {"senderName": {}}}, "enterAnEmailAddress": "Enter an email address", "@enterAnEmailAddress": {"type": "text", "placeholders": {}}, "homeserver": "Homeserver", "@homeserver": {}, "enterYourHomeserver": "Enter your homeserver", "@enterYourHomeserver": {"type": "text", "placeholders": {}}, "errorObtainingLocation": "Error obtaining location: {error}", "@errorObtainingLocation": {"type": "text", "placeholders": {"error": {}}}, "everythingReady": "Everything ready!", "@everythingReady": {"type": "text", "placeholders": {}}, "extremeOffensive": "Extremely offensive", "@extremeOffensive": {"type": "text", "placeholders": {}}, "fileName": "File name", "@fileName": {"type": "text", "placeholders": {}}, "fluffychat": "FluffyChat", "@fluffychat": {"type": "text", "placeholders": {}}, "fontSize": "Font size", "@fontSize": {"type": "text", "placeholders": {}}, "forward": "Forward", "@forward": {"type": "text", "placeholders": {}}, "fromJoining": "From joining", "@fromJoining": {"type": "text", "placeholders": {}}, "fromTheInvitation": "From the invitation", "@fromTheInvitation": {"type": "text", "placeholders": {}}, "goToTheNewRoom": "Go to the new room", "@goToTheNewRoom": {"type": "text", "placeholders": {}}, "group": "Group", "@group": {"type": "text", "placeholders": {}}, "chatDescription": "Chat description", "chatDescriptionHasBeenChanged": "Chat description changed", "groupIsPublic": "Group is public", "@groupIsPublic": {"type": "text", "placeholders": {}}, "groups": "Groups", "@groups": {"type": "text", "placeholders": {}}, "groupWith": "Group with {displayname}", "@groupWith": {"type": "text", "placeholders": {"displayname": {}}}, "guestsAreForbidden": "Guests are forbidden", "@guestsAreForbidden": {"type": "text", "placeholders": {}}, "guestsCanJoin": "Guests can join", "@guestsCanJoin": {"type": "text", "placeholders": {}}, "hasWithdrawnTheInvitationFor": "{username} has withdrawn the invitation for {targetName}", "@hasWithdrawnTheInvitationFor": {"type": "text", "placeholders": {"username": {}, "targetName": {}}}, "help": "Help", "@help": {"type": "text", "placeholders": {}}, "hideRedactedEvents": "Hide redacted events", "@hideRedactedEvents": {"type": "text", "placeholders": {}}, "hideUnknownEvents": "Hide unknown events", "@hideUnknownEvents": {"type": "text", "placeholders": {}}, "howOffensiveIsThisContent": "How offensive is this content?", "@howOffensiveIsThisContent": {"type": "text", "placeholders": {}}, "id": "ID", "@id": {"type": "text", "placeholders": {}}, "identity": "Identity", "@identity": {"type": "text", "placeholders": {}}, "block": "block", "blockedUsers": "Blocked users", "blockListDescription": "You can block users who are disturbing you. You won't be able to receive any messages or room invites from the users on your personal block list.", "blockUsername": "Ignore username", "iHaveClickedOnLink": "I have clicked on the link", "@iHaveClickedOnLink": {"type": "text", "placeholders": {}}, "incorrectPassphraseOrKey": "Incorrect passphrase or recovery key", "@incorrectPassphraseOrKey": {"type": "text", "placeholders": {}}, "inoffensive": "Inoffensive", "@inoffensive": {"type": "text", "placeholders": {}}, "inviteContact": "Invite contact", "@inviteContact": {"type": "text", "placeholders": {}}, "inviteContactToGroupQuestion": "Do you want to invite {contact} to the chat \"{groupName}\"?", "@inviteContactToGroup": {"type": "text", "placeholders": {"groupName": {}}}, "inviteContactToGroup": "Invite contact to {groupName}", "noChatDescriptionYet": "No chat description created yet.", "tryAgain": "Try again", "invalidServerName": "Invalid server name", "invited": "Invited", "@invited": {"type": "text", "placeholders": {}}, "redactMessageDescription": "The message will be redacted for all participants in this conversation. This cannot be undone.", "optionalRedactReason": "(Optional) Reason for redacting this message...", "invitedUser": "📩 {username} invited {targetName}", "@invitedUser": {"type": "text", "placeholders": {"username": {}, "targetName": {}}}, "invitedUsersOnly": "Invited users only", "@invitedUsersOnly": {"type": "text", "placeholders": {}}, "inviteForMe": "Invite for me", "@inviteForMe": {"type": "text", "placeholders": {}}, "inviteText": "{username} invited you to FluffyChat.\n1. Visit fluffychat.im and install the app \n2. Sign up or sign in \n3. Open the invite link: \n {link}", "@inviteText": {"type": "text", "placeholders": {"username": {}, "link": {}}}, "isTyping": "is typing…", "@isTyping": {"type": "text", "placeholders": {}}, "joinedTheChat": "👋 {username} joined the chat", "@joinedTheChat": {"type": "text", "placeholders": {"username": {}}}, "joinRoom": "Join room", "@joinRoom": {"type": "text", "placeholders": {}}, "kicked": "👞 {username} kicked {targetName}", "@kicked": {"type": "text", "placeholders": {"username": {}, "targetName": {}}}, "kickedAndBanned": "🙅 {username} kicked and banned {targetName}", "@kickedAndBanned": {"type": "text", "placeholders": {"username": {}, "targetName": {}}}, "kickFromChat": "Kick from chat", "@kickFromChat": {"type": "text", "placeholders": {}}, "lastActiveAgo": "Last active: {localizedTimeShort}", "@lastActiveAgo": {"type": "text", "placeholders": {"localizedTimeShort": {}}}, "leave": "Leave", "@leave": {"type": "text", "placeholders": {}}, "leftTheChat": "Left the chat", "@leftTheChat": {"type": "text", "placeholders": {}}, "license": "License", "@license": {"type": "text", "placeholders": {}}, "lightTheme": "Light", "@lightTheme": {"type": "text", "placeholders": {}}, "loadCountMoreParticipants": "Load {count} more participants", "@loadCountMoreParticipants": {"type": "text", "placeholders": {"count": {}}}, "dehydrate": "Export session and wipe device", "@dehydrate": {}, "dehydrateWarning": "This action cannot be undone. Ensure you safely store the backup file.", "@dehydrateWarning": {}, "dehydrateTor": "TOR Users: Export session", "@dehydrateTor": {}, "dehydrateTorLong": "For TOR users, it is recommended to export the session before closing the window.", "@dehydrateTorLong": {}, "hydrateTor": "TOR Users: Import session export", "@hydrateTor": {}, "hydrateTorLong": "Did you export your session last time on TOR? Quickly import it and continue chatting.", "@hydrateTorLong": {}, "hydrate": "Restore from backup file", "@hydrate": {}, "loadingPleaseWait": "Loading… Please wait.", "@loadingPleaseWait": {"type": "text", "placeholders": {}}, "loadMore": "Load more…", "@loadMore": {"type": "text", "placeholders": {}}, "locationDisabledNotice": "Location services are disabled. Please enable them to be able to share your location.", "@locationDisabledNotice": {"type": "text", "placeholders": {}}, "locationPermissionDeniedNotice": "Location permission denied. Please grant them to be able to share your location.", "@locationPermissionDeniedNotice": {"type": "text", "placeholders": {}}, "login": "<PERSON><PERSON>", "@login": {"type": "text", "placeholders": {}}, "logInTo": "Log in to {homeserver}", "@logInTo": {"type": "text", "placeholders": {"homeserver": {}}}, "logout": "Logout", "@logout": {"type": "text", "placeholders": {}}, "memberChanges": "Member changes", "@memberChanges": {"type": "text", "placeholders": {}}, "mention": "Mention", "@mention": {"type": "text", "placeholders": {}}, "messages": "Messages", "messagesStyle": "Messages:", "@messages": {"type": "text", "placeholders": {}}, "moderator": "Moderator", "@moderator": {"type": "text", "placeholders": {}}, "muteChat": "Mute chat", "@muteChat": {"type": "text", "placeholders": {}}, "needPantalaimonWarning": "Please be aware that you need <PERSON><PERSON><PERSON><PERSON> to use end-to-end encryption for now.", "@needPantalaimonWarning": {"type": "text", "placeholders": {}}, "newChat": "New chat", "@newChat": {"type": "text", "placeholders": {}}, "newMessageInFluffyChat": "💬 New message in FluffyChat", "@newMessageInFluffyChat": {"type": "text", "placeholders": {}}, "newVerificationRequest": "New verification request!", "@newVerificationRequest": {"type": "text", "placeholders": {}}, "next": "Next", "@next": {"type": "text", "placeholders": {}}, "no": "No", "@no": {"type": "text", "placeholders": {}}, "noConnectionToTheServer": "No connection to the server", "@noConnectionToTheServer": {"type": "text", "placeholders": {}}, "noEmotesFound": "No emotes found. 😕", "@noEmotesFound": {"type": "text", "placeholders": {}}, "noEncryptionForPublicRooms": "You can only activate encryption as soon as the room is no longer publicly accessible.", "@noEncryptionForPublicRooms": {"type": "text", "placeholders": {}}, "noGoogleServicesWarning": "Firebase Cloud Messaging doesn't appear to be available on your device. To still receive push notifications, we recommend installing ntfy. With ntfy or another Unified Push provider you can receive push notifications in a data secure way. You can download ntfy from the PlayStore or from F-Droid.", "@noGoogleServicesWarning": {"type": "text", "placeholders": {}}, "noMatrixServer": "{server1} is no matrix server, use {server2} instead?", "@noMatrixServer": {"type": "text", "placeholders": {"server1": {}, "server2": {}}}, "shareInviteLink": "Share invite link", "scanQrCode": "Scan QR code", "@scanQrCode": {}, "none": "None", "@none": {"type": "text", "placeholders": {}}, "noPasswordRecoveryDescription": "You have not added a way to recover your password yet.", "@noPasswordRecoveryDescription": {"type": "text", "placeholders": {}}, "noPermission": "No permission", "@noPermission": {"type": "text", "placeholders": {}}, "noRoomsFound": "No rooms found…", "@noRoomsFound": {"type": "text", "placeholders": {}}, "notifications": "Notifications", "@notifications": {"type": "text", "placeholders": {}}, "notificationsEnabledForThisAccount": "Notifications enabled for this account", "@notificationsEnabledForThisAccount": {"type": "text", "placeholders": {}}, "numUsersTyping": "{count} users are typing…", "@numUsersTyping": {"type": "text", "placeholders": {"count": {}}}, "obtainingLocation": "Obtaining location…", "@obtainingLocation": {"type": "text", "placeholders": {}}, "offensive": "Offensive", "@offensive": {"type": "text", "placeholders": {}}, "offline": "Offline", "@offline": {"type": "text", "placeholders": {}}, "ok": "Ok", "@ok": {"type": "text", "placeholders": {}}, "online": "Online", "@online": {"type": "text", "placeholders": {}}, "onlineKeyBackupEnabled": "Online Key Backup is enabled", "@onlineKeyBackupEnabled": {"type": "text", "placeholders": {}}, "oopsPushError": "Oops! Unfortunately, an error occurred when setting up the push notifications.", "@oopsPushError": {"type": "text", "placeholders": {}}, "oopsSomethingWentWrong": "Oops, something went wrong…", "@oopsSomethingWentWrong": {"type": "text", "placeholders": {}}, "openAppToReadMessages": "Open app to read messages", "@openAppToReadMessages": {"type": "text", "placeholders": {}}, "openCamera": "Open camera", "@openCamera": {"type": "text", "placeholders": {}}, "openVideoCamera": "Open camera for a video", "@openVideoCamera": {"type": "text", "placeholders": {}}, "oneClientLoggedOut": "One of your clients has been logged out", "@oneClientLoggedOut": {}, "addAccount": "Add account", "@addAccount": {}, "editBundlesForAccount": "Edit bundles for this account", "@editBundlesForAccount": {}, "addToBundle": "Add to bundle", "@addToBundle": {}, "removeFromBundle": "Remove from this bundle", "@removeFromBundle": {}, "bundleName": "Bundle name", "@bundleName": {}, "enableMultiAccounts": "(BETA) Enable multi accounts on this device", "@enableMultiAccounts": {}, "openInMaps": "Open in maps", "@openInMaps": {"type": "text", "placeholders": {}}, "link": "Link", "@link": {}, "serverRequiresEmail": "This server needs to validate your email address for registration.", "@serverRequiresEmail": {}, "or": "Or", "@or": {"type": "text", "placeholders": {}}, "participant": "Participant", "@participant": {"type": "text", "placeholders": {}}, "passphraseOrKey": "passphrase or recovery key", "@passphraseOrKey": {"type": "text", "placeholders": {}}, "password": "Password", "@password": {"type": "text", "placeholders": {}}, "passwordForgotten": "Password forgotten", "@passwordForgotten": {"type": "text", "placeholders": {}}, "passwordHasBeenChanged": "Password has been changed", "@passwordHasBeenChanged": {"type": "text", "placeholders": {}}, "passwordRecovery": "Password recovery", "@passwordRecovery": {"type": "text", "placeholders": {}}, "people": "People", "@people": {"type": "text", "placeholders": {}}, "pickImage": "Pick an image", "@pickImage": {"type": "text", "placeholders": {}}, "pin": "<PERSON>n", "@pin": {"type": "text", "placeholders": {}}, "play": "Play {fileName}", "@play": {"type": "text", "placeholders": {"fileName": {}}}, "pleaseChoose": "Please choose", "@pleaseChoose": {"type": "text", "placeholders": {}}, "pleaseChooseAPasscode": "Please choose a pass code", "@pleaseChooseAPasscode": {"type": "text", "placeholders": {}}, "pleaseClickOnLink": "Please click on the link in the email and then proceed.", "@pleaseClickOnLink": {"type": "text", "placeholders": {}}, "pleaseEnter4Digits": "Please enter 4 digits or leave empty to disable app lock.", "@pleaseEnter4Digits": {"type": "text", "placeholders": {}}, "pleaseEnterRecoveryKey": "Please enter your recovery key:", "@pleaseEnterRecoveryKey": {}, "pleaseEnterYourPassword": "Please enter your password", "@pleaseEnterYourPassword": {"type": "text", "placeholders": {}}, "pleaseEnterYourPin": "Please enter your pin", "@pleaseEnterYourPin": {"type": "text", "placeholders": {}}, "pleaseEnterYourUsername": "Please enter your username", "@pleaseEnterYourUsername": {"type": "text", "placeholders": {}}, "pleaseFollowInstructionsOnWeb": "Please follow the instructions on the website and tap on next.", "@pleaseFollowInstructionsOnWeb": {"type": "text", "placeholders": {}}, "privacy": "Privacy", "@privacy": {"type": "text", "placeholders": {}}, "publicRooms": "Public Rooms", "@publicRooms": {"type": "text", "placeholders": {}}, "pushRules": "Push rules", "@pushRules": {"type": "text", "placeholders": {}}, "reason": "Reason", "@reason": {"type": "text", "placeholders": {}}, "recording": "Recording", "@recording": {"type": "text", "placeholders": {}}, "redactedBy": "Redacted by {username}", "@redactedBy": {"type": "text", "placeholders": {"username": {}}}, "directChat": "Direct chat", "redactedByBecause": "Redacted by {username} because: \"{reason}\"", "@redactedByBecause": {"type": "text", "placeholders": {"username": {}, "reason": {}}}, "redactedAnEvent": "{username} redacted an event", "@redactedAnEvent": {"type": "text", "placeholders": {"username": {}}}, "redactMessage": "Redact message", "@redactMessage": {"type": "text", "placeholders": {}}, "register": "Register", "@register": {"type": "text", "placeholders": {}}, "reject": "Reject", "@reject": {"type": "text", "placeholders": {}}, "rejectedTheInvitation": "{username} rejected the invitation", "@rejectedTheInvitation": {"type": "text", "placeholders": {"username": {}}}, "rejoin": "Rejoin", "@rejoin": {"type": "text", "placeholders": {}}, "@remove": {"type": "text", "placeholders": {}}, "removeAllOtherDevices": "Remove all other devices", "@removeAllOtherDevices": {"type": "text", "placeholders": {}}, "removedBy": "Removed by {username}", "@removedBy": {"type": "text", "placeholders": {"username": {}}}, "removeDevice": "Remove device", "@removeDevice": {"type": "text", "placeholders": {}}, "unbanFromChat": "<PERSON><PERSON> from chat", "@unbanFromChat": {"type": "text", "placeholders": {}}, "removeYourAvatar": "Remove your avatar", "@removeYourAvatar": {"type": "text", "placeholders": {}}, "replaceRoomWithNewerVersion": "Replace room with newer version", "@replaceRoomWithNewerVersion": {"type": "text", "placeholders": {}}, "reply": "Reply", "@reply": {"type": "text", "placeholders": {}}, "reportMessage": "Report message", "@reportMessage": {"type": "text", "placeholders": {}}, "requestPermission": "Request permission", "@requestPermission": {"type": "text", "placeholders": {}}, "roomHasBeenUpgraded": "Room has been upgraded", "@roomHasBeenUpgraded": {"type": "text", "placeholders": {}}, "roomVersion": "Room version", "@roomVersion": {"type": "text", "placeholders": {}}, "saveFile": "Save file", "@saveFile": {"type": "text", "placeholders": {}}, "search": "Search", "@search": {"type": "text", "placeholders": {}}, "security": "Security", "@security": {"type": "text", "placeholders": {}}, "recoveryKey": "Recovery key", "@recoveryKey": {}, "recoveryKeyLost": "Recovery key lost?", "@recoveryKeyLost": {}, "seenByUser": "Seen by {username}", "@seenByUser": {"type": "text", "placeholders": {"username": {}}}, "send": "Send", "@send": {"type": "text", "placeholders": {}}, "sendAMessage": "Send a message", "@sendAMessage": {"type": "text", "placeholders": {}}, "sendAsText": "Send as text", "@sendAsText": {"type": "text"}, "sendAudio": "Send audio", "@sendAudio": {"type": "text", "placeholders": {}}, "sendFile": "Send file", "@sendFile": {"type": "text", "placeholders": {}}, "sendImage": "Send image", "@sendImage": {"type": "text", "placeholders": {}}, "sendMessages": "Send messages", "@sendMessages": {"type": "text", "placeholders": {}}, "sendOriginal": "Send original", "@sendOriginal": {"type": "text", "placeholders": {}}, "sendSticker": "Send sticker", "@sendSticker": {"type": "text", "placeholders": {}}, "sendVideo": "Send video", "@sendVideo": {"type": "text", "placeholders": {}}, "sentAFile": "📁 {username} sent a file", "@sentAFile": {"type": "text", "placeholders": {"username": {}}}, "sentAnAudio": "🎤 {username} sent an audio", "@sentAnAudio": {"type": "text", "placeholders": {"username": {}}}, "sentAPicture": "🖼️ {username} sent a picture", "@sentAPicture": {"type": "text", "placeholders": {"username": {}}}, "sentASticker": "😊 {username} sent a sticker", "@sentASticker": {"type": "text", "placeholders": {"username": {}}}, "sentAVideo": "🎥 {username} sent a video", "@sentAVideo": {"type": "text", "placeholders": {"username": {}}}, "sentCallInformations": "{senderName} sent call information", "@sentCallInformations": {"type": "text", "placeholders": {"senderName": {}}}, "separateChatTypes": "Separate Direct Chats and Groups", "@separateChatTypes": {"type": "text", "placeholders": {}}, "setAsCanonicalAlias": "Set as main alias", "@setAsCanonicalAlias": {"type": "text", "placeholders": {}}, "setCustomEmotes": "Set custom emotes", "@setCustomEmotes": {"type": "text", "placeholders": {}}, "setChatDescription": "Set chat description", "setInvitationLink": "Set invitation link", "@setInvitationLink": {"type": "text", "placeholders": {}}, "setPermissionsLevel": "Set permissions level", "@setPermissionsLevel": {"type": "text", "placeholders": {}}, "setStatus": "Set status", "@setStatus": {"type": "text", "placeholders": {}}, "settings": "Settings", "@settings": {"type": "text", "placeholders": {}}, "share": "Share", "@share": {"type": "text", "placeholders": {}}, "sharedTheLocation": "{username} shared their location", "@sharedTheLocation": {"type": "text", "placeholders": {"username": {}}}, "shareLocation": "Share location", "@shareLocation": {"type": "text", "placeholders": {}}, "showPassword": "Show password", "@showPassword": {"type": "text", "placeholders": {}}, "singlesignon": "Single Sign on", "@singlesignon": {"type": "text", "placeholders": {}}, "skip": "<PERSON><PERSON>", "@skip": {"type": "text", "placeholders": {}}, "sourceCode": "Source code", "@sourceCode": {"type": "text", "placeholders": {}}, "spaceIsPublic": "Space is public", "@spaceIsPublic": {"type": "text", "placeholders": {}}, "spaceName": "Space name", "@spaceName": {"type": "text", "placeholders": {}}, "startedACall": "{sender<PERSON>ame} started a call", "@startedACall": {"type": "text", "placeholders": {"senderName": {}}}, "startFirstChat": "Start your first chat", "status": "Status", "@status": {"type": "text", "placeholders": {}}, "statusExampleMessage": "How are you today?", "@statusExampleMessage": {"type": "text", "placeholders": {}}, "submit": "Submit", "@submit": {"type": "text", "placeholders": {}}, "synchronizingPleaseWait": "Synchronizing… Please wait.", "@synchronizingPleaseWait": {"type": "text", "placeholders": {}}, "systemTheme": "System", "@systemTheme": {"type": "text", "placeholders": {}}, "theyDontMatch": "They Don't Match", "@theyDontMatch": {"type": "text", "placeholders": {}}, "theyMatch": "They Match", "@theyMatch": {"type": "text", "placeholders": {}}, "title": "FluffyChat", "@title": {"description": "Title for the application", "type": "text", "placeholders": {}}, "toggleFavorite": "Toggle Favorite", "@toggleFavorite": {"type": "text", "placeholders": {}}, "toggleMuted": "Toggle <PERSON>ted", "@toggleMuted": {"type": "text", "placeholders": {}}, "toggleUnread": "<PERSON>/Unread", "@toggleUnread": {"type": "text", "placeholders": {}}, "tooManyRequestsWarning": "Too many requests. Please try again later!", "@tooManyRequestsWarning": {"type": "text", "placeholders": {}}, "transferFromAnotherDevice": "Transfer from another device", "@transferFromAnotherDevice": {"type": "text", "placeholders": {}}, "tryToSendAgain": "Try to send again", "@tryToSendAgain": {"type": "text", "placeholders": {}}, "unavailable": "Unavailable", "@unavailable": {"type": "text", "placeholders": {}}, "unbannedUser": "{username} unbanned {targetName}", "@unbannedUser": {"type": "text", "placeholders": {"username": {}, "targetName": {}}}, "unblockDevice": "Unblock Device", "@unblockDevice": {"type": "text", "placeholders": {}}, "unknownDevice": "Unknown device", "@unknownDevice": {"type": "text", "placeholders": {}}, "unknownEncryptionAlgorithm": "Unknown encryption algorithm", "@unknownEncryptionAlgorithm": {"type": "text", "placeholders": {}}, "unknownEvent": "Unknown event '{type}'", "@unknownEvent": {"type": "text", "placeholders": {"type": {}}}, "unmuteChat": "Unmute chat", "@unmuteChat": {"type": "text", "placeholders": {}}, "unpin": "Unpin", "@unpin": {"type": "text", "placeholders": {}}, "unreadChats": "{unreadCount, plural, =1{1 unread chat} other{{unreadCount} unread chats}}", "@unreadChats": {"type": "text", "placeholders": {"unreadCount": {}}}, "userAndOthersAreTyping": "{username} and {count} others are typing…", "@userAndOthersAreTyping": {"type": "text", "placeholders": {"username": {}, "count": {}}}, "userAndUserAreTyping": "{username} and {username2} are typing…", "@userAndUserAreTyping": {"type": "text", "placeholders": {"username": {}, "username2": {}}}, "userIsTyping": "{username} is typing…", "@userIsTyping": {"type": "text", "placeholders": {"username": {}}}, "userLeftTheChat": "🚪 {username} left the chat", "@userLeftTheChat": {"type": "text", "placeholders": {"username": {}}}, "username": "Username", "@username": {"type": "text", "placeholders": {}}, "userSentUnknownEvent": "{username} sent a {type} event", "@userSentUnknownEvent": {"type": "text", "placeholders": {"username": {}, "type": {}}}, "unverified": "Unverified", "@unverified": {}, "verified": "Verified", "@verified": {"type": "text", "placeholders": {}}, "verify": "Verify", "@verify": {"type": "text", "placeholders": {}}, "verifyStart": "Start Verification", "@verifyStart": {"type": "text", "placeholders": {}}, "verifySuccess": "You successfully verified!", "@verifySuccess": {"type": "text", "placeholders": {}}, "verifyTitle": "Verifying other account", "@verifyTitle": {"type": "text", "placeholders": {}}, "videoCall": "Video call", "@videoCall": {"type": "text", "placeholders": {}}, "visibilityOfTheChatHistory": "Visibility of the chat history", "@visibilityOfTheChatHistory": {"type": "text", "placeholders": {}}, "visibleForAllParticipants": "Visible for all participants", "@visibleForAllParticipants": {"type": "text", "placeholders": {}}, "visibleForEveryone": "Visible for everyone", "@visibleForEveryone": {"type": "text", "placeholders": {}}, "voiceMessage": "Voice message", "@voiceMessage": {"type": "text", "placeholders": {}}, "waitingPartnerAcceptRequest": "Waiting for partner to accept the request…", "@waitingPartnerAcceptRequest": {"type": "text", "placeholders": {}}, "waitingPartnerEmoji": "Waiting for partner to accept the emoji…", "@waitingPartnerEmoji": {"type": "text", "placeholders": {}}, "waitingPartnerNumbers": "Waiting for partner to accept the numbers…", "@waitingPartnerNumbers": {"type": "text", "placeholders": {}}, "wallpaper": "Wallpaper:", "@wallpaper": {"type": "text", "placeholders": {}}, "warning": "Warning!", "@warning": {"type": "text", "placeholders": {}}, "weSentYouAnEmail": "We sent you an email", "@weSentYouAnEmail": {"type": "text", "placeholders": {}}, "whoCanPerformWhichAction": "Who can perform which action", "@whoCanPerformWhichAction": {"type": "text", "placeholders": {}}, "whoIsAllowedToJoinThisGroup": "Who is allowed to join this group", "@whoIsAllowedToJoinThisGroup": {"type": "text", "placeholders": {}}, "whyDoYouWantToReportThis": "Why do you want to report this?", "@whyDoYouWantToReportThis": {"type": "text", "placeholders": {}}, "wipeChatBackup": "Wipe your chat backup to create a new recovery key?", "@wipeChatBackup": {"type": "text", "placeholders": {}}, "withTheseAddressesRecoveryDescription": "With these addresses you can recover your password.", "@withTheseAddressesRecoveryDescription": {"type": "text", "placeholders": {}}, "writeAMessage": "Write a message…", "@writeAMessage": {"type": "text", "placeholders": {}}, "yes": "Yes", "@yes": {"type": "text", "placeholders": {}}, "you": "You", "@you": {"type": "text", "placeholders": {}}, "youAreNoLongerParticipatingInThisChat": "You are no longer participating in this chat", "@youAreNoLongerParticipatingInThisChat": {"type": "text", "placeholders": {}}, "youHaveBeenBannedFromThisChat": "You have been banned from this chat", "@youHaveBeenBannedFromThisChat": {"type": "text", "placeholders": {}}, "yourPublicKey": "Your public key", "@yourPublicKey": {"type": "text", "placeholders": {}}, "messageInfo": "Message info", "@messageInfo": {}, "time": "Time", "@time": {}, "messageType": "Message Type", "@messageType": {}, "sender": "Sender", "@sender": {}, "openGallery": "Open gallery", "@openGallery": {}, "removeFromSpace": "Remove from space", "@removeFromSpace": {}, "addToSpaceDescription": "Select a space to add this chat to it.", "@addToSpaceDescription": {}, "start": "Start", "@start": {}, "pleaseEnterRecoveryKeyDescription": "To unlock your old messages, please enter your recovery key that has been generated in a previous session. Your recovery key is NOT your password.", "@pleaseEnterRecoveryKeyDescription": {}, "publish": "Publish", "@publish": {}, "videoWithSize": "Video ({size})", "@videoWithSize": {"type": "text", "placeholders": {"size": {}}}, "openChat": "Open Chat", "@openChat": {}, "markAsRead": "<PERSON> as read", "@markAsRead": {}, "reportUser": "Report user", "@reportUser": {}, "dismiss": "<PERSON><PERSON><PERSON>", "@dismiss": {}, "reactedWith": "{sender} reacted with {reaction}", "@reactedWith": {"type": "text", "placeholders": {"sender": {}, "reaction": {}}}, "pinMessage": "Pin to room", "@pinMessage": {}, "confirmEventUnpin": "Are you sure to permanently unpin the event?", "@confirmEventUnpin": {}, "emojis": "Emojis", "@emojis": {}, "placeCall": "Place call", "@placeCall": {}, "voiceCall": "Voice call", "@voiceCall": {}, "unsupportedAndroidVersion": "Unsupported Android version", "@unsupportedAndroidVersion": {}, "unsupportedAndroidVersionLong": "This feature requires a newer Android version. Please check for updates or Lineage OS support.", "@unsupportedAndroidVersionLong": {}, "videoCallsBetaWarning": "Please note that video calls are currently in beta. They might not work as expected or work at all on all platforms.", "@videoCallsBetaWarning": {}, "experimentalVideoCalls": "Experimental video calls", "@experimentalVideoCalls": {}, "emailOrUsername": "Email or username", "@emailOrUsername": {}, "indexedDbErrorTitle": "Private mode issues", "@indexedDbErrorTitle": {}, "indexedDbErrorLong": "The message storage is unfortunately not enabled in private mode by default.\nPlease visit\n - about:config\n - set dom.indexedDB.privateBrowsing.enabled to true\nOtherwise, it is not possible to run FluffyChat.", "@indexedDbErrorLong": {}, "switchToAccount": "Switch to account {number}", "@switchToAccount": {"type": "number", "placeholders": {"number": {}}}, "nextAccount": "Next account", "@nextAccount": {}, "previousAccount": "Previous account", "@previousAccount": {}, "addWidget": "Add widget", "@addWidget": {}, "widgetVideo": "Video", "@widgetVideo": {}, "widgetEtherpad": "Text note", "@widgetEtherpad": {}, "widgetJitsi": "Jitsi Meet", "@widgetJitsi": {}, "widgetCustom": "Custom", "@widgetCustom": {}, "widgetName": "Name", "@widgetName": {}, "widgetUrlError": "This is not a valid URL.", "@widgetUrlError": {}, "widgetNameError": "Please provide a display name.", "@widgetNameError": {}, "errorAddingWidget": "Error adding the widget.", "@errorAddingWidget": {}, "youRejectedTheInvitation": "You rejected the invitation", "@youRejectedTheInvitation": {}, "youJoinedTheChat": "You joined the chat", "@youJoinedTheChat": {}, "youAcceptedTheInvitation": "👍 You accepted the invitation", "@youAcceptedTheInvitation": {}, "youBannedUser": "You banned {user}", "@youBannedUser": {"placeholders": {"user": {}}}, "youHaveWithdrawnTheInvitationFor": "You have withdrawn the invitation for {user}", "@youHaveWithdrawnTheInvitationFor": {"placeholders": {"user": {}}}, "invitedBy": "📩 You have been invited by {user}", "@youInvitedBy": {"placeholders": {"user": {}}}, "youInvitedUser": "📩 You invited {user}", "@youInvitedUser": {"placeholders": {"user": {}}}, "youKicked": "👞 You kicked {user}", "@youKicked": {"placeholders": {"user": {}}}, "youKickedAndBanned": "🙅 You kicked and banned {user}", "@youKickedAndBanned": {"placeholders": {"user": {}}}, "youUnbannedUser": "You unbanned {user}", "@youUnbannedUser": {"placeholders": {"user": {}}}, "hasKnocked": "{user} has knocked", "@hasKnocked": {"placeholders": {"user": {}}}, "users": "Users", "@users": {}, "unlockOldMessages": "Unlock old messages", "@unlockOldMessages": {}, "storeInSecureStorageDescription": "Store the recovery key in the secure storage of this device.", "@storeInSecureStorageDescription": {}, "saveKeyManuallyDescription": "Save this key manually by triggering the system share dialog or clipboard.", "@saveKeyManuallyDescription": {}, "storeInAndroidKeystore": "Store in Android KeyStore", "@storeInAndroidKeystore": {}, "storeInAppleKeyChain": "Store in Apple KeyChain", "@storeInAppleKeyChain": {}, "storeSecurlyOnThisDevice": "Store securely on this device", "@storeSecurlyOnThisDevice": {}, "countFiles": "{count} files", "@countFiles": {"placeholders": {"count": {}}}, "user": "User", "@user": {}, "custom": "Custom", "@custom": {}, "foregroundServiceRunning": "This notification appears when the foreground service is running.", "@foregroundServiceRunning": {}, "screenSharingTitle": "screen sharing", "@screenSharingTitle": {}, "screenSharingDetail": "You are sharing your screen in FuffyChat", "@screenSharingDetail": {}, "callingPermissions": "Calling permissions", "@callingPermissions": {}, "callingAccount": "Calling account", "@callingAccount": {}, "callingAccountDetails": "Allows FluffyChat to use the native android dialer app.", "@callingAccountDetails": {}, "appearOnTop": "Appear on top", "@appearOnTop": {}, "appearOnTopDetails": "Allows the app to appear on top (not needed if you already have Fluffychat setup as a calling account)", "@appearOnTopDetails": {}, "otherCallingPermissions": "Microphone, camera and other FluffyChat permissions", "@otherCallingPermissions": {}, "whyIsThisMessageEncrypted": "Why is this message unreadable?", "@whyIsThisMessageEncrypted": {}, "noKeyForThisMessage": "This can happen if the message was sent before you have signed in to your account at this device.\n\nIt is also possible that the sender has blocked your device or something went wrong with the internet connection.\n\nAre you able to read the message on another session? Then you can transfer the message from it! Go to Settings > Devices and make sure that your devices have verified each other. When you open the room the next time and both sessions are in the foreground, the keys will be transmitted automatically.\n\nDo you not want to lose the keys when logging out or switching devices? Make sure that you have enabled the chat backup in the settings.", "@noKeyForThisMessage": {}, "newGroup": "New group", "@newGroup": {}, "newSpace": "New space", "@newSpace": {}, "enterSpace": "Enter space", "@enterSpace": {}, "enterRoom": "Enter room", "@enterRoom": {}, "allSpaces": "All spaces", "@allSpaces": {}, "numChats": "{number} chats", "@numChats": {"type": "number", "placeholders": {"number": {}}}, "hideUnimportantStateEvents": "Hide unimportant state events", "doNotShowAgain": "Do not show again", "wasDirectChatDisplayName": "Empty chat (was {oldDisplayName})", "@wasDirectChatDisplayName": {"type": "text", "placeholders": {"oldDisplayName": {}}}, "newSpaceDescription": "Spaces allows you to consolidate your chats and build private or public communities.", "encryptThisChat": "Encrypt this chat", "disableEncryptionWarning": "For security reasons you can not disable encryption in a chat, where it has been enabled before.", "sorryThatsNotPossible": "Sorry... that is not possible", "deviceKeys": "Device keys:", "reopenChat": "Reopen chat", "noBackupWarning": "Warning! Without enabling chat backup, you will lose access to your encrypted messages. It is highly recommended to enable the chat backup first before logging out.", "noOtherDevicesFound": "No other devices found", "fileIsTooBigForServer": "The server reports that the file is too large to be sent.", "fileHasBeenSavedAt": "File has been saved at {path}", "@fileHasBeenSavedAt": {"type": "text", "placeholders": {"path": {}}}, "jumpToLastReadMessage": "Jump to last read message", "readUpToHere": "Read up to here", "jump": "Jump", "openLinkInBrowser": "Open link in browser", "reportErrorDescription": "😭 Oh no. Something went wrong. If you want, you can report this bug to the developers.", "report": "report", "signInWithPassword": "Sign in with password", "pleaseTryAgainLaterOrChooseDifferentServer": "Please try again later or choose a different server.", "signInWith": "Sign in with {provider}", "@signInWith": {"type": "text", "placeholders": {"provider": {}}}, "profileNotFound": "The user could not be found on the server. Maybe there is a connection problem or the user doesn't exist.", "setTheme": "Set theme:", "setColorTheme": "Set color theme:", "invite": "Invite", "inviteGroupChat": "📨 Invite group chat", "invitePrivateChat": "📨 Invite private chat", "invalidInput": "Invalid input!", "wrongPinEntered": "Wrong pin entered! Try again in {seconds} seconds...", "@wrongPinEntered": {"type": "text", "placeholders": {"seconds": {}}}, "pleaseEnterANumber": "Please enter a number greater than 0", "archiveRoomDescription": "The chat will be moved to the archive. Other users will be able to see that you have left the chat.", "roomUpgradeDescription": "The chat will then be recreated with the new room version. All participants will be notified that they need to switch to the new chat. You can find out more about room versions at https://spec.matrix.org/latest/rooms/", "removeDevicesDescription": "You will be logged out of this device and will no longer be able to receive messages.", "banUserDescription": "The user will be banned from the chat and will not be able to enter the chat again until they are unbanned.", "unbanUserDescription": "The user will be able to enter the chat again if they try.", "kickUserDescription": "The user is kicked out of the chat but not banned. In public chats, the user can rejoin at any time.", "makeAdminDescription": "Once you make this user admin, you may not be able to undo this as they will then have the same permissions as you.", "pushNotificationsNotAvailable": "Push notifications not available", "learnMore": "Learn more", "yourGlobalUserIdIs": "Your global user-ID is: ", "noUsersFoundWithQuery": "Unfortunately no user could be found with \"{query}\". Please check whether you made a typo.", "@noUsersFoundWithQuery": {"type": "text", "placeholders": {"query": {}}}, "searchChatsRooms": "Search for #chats, @users...", "nothingFound": "Nothing found...", "groupName": "Group name", "createGroupAndInviteUsers": "Create a group and invite users", "groupCanBeFoundViaSearch": "Group can be found via search", "wrongRecoveryKey": "Sorry... this does not seem to be the correct recovery key.", "startConversation": "Start conversation", "commandHint_sendraw": "Send raw json", "databaseMigrationTitle": "Database is optimized", "databaseMigrationBody": "Please wait. This may take a moment.", "leaveEmptyToClearStatus": "Leave empty to clear your status.", "select": "Select", "searchForUsers": "Search for @users...", "pleaseEnterYourCurrentPassword": "Please enter your current password", "newPassword": "New password", "pleaseChooseAStrongPassword": "Please choose a strong password", "passwordsDoNotMatch": "Passwords do not match", "passwordIsWrong": "Your entered password is wrong", "publicLink": "Public link", "joinSpace": "Join space", "publicSpaces": "Public spaces", "addChatOrSubSpace": "Add chat or sub space", "subspace": "Subspace", "decline": "Decline", "thisDevice": "This device:", "initAppError": "An error occured while init the app", "databaseBuildErrorBody": "Unable to build the SQlite database. The app tries to use the legacy database for now. Please report this error to the developers at {url}. The error message is: {error}", "@databaseBuildErrorBody": {"type": "text", "placeholders": {"url": {}, "error": {}}}, "sessionLostBody": "Your session is lost. Please report this error to the developers at {url}. The error message is: {error}", "@sessionLostBody": {"type": "text", "placeholders": {"url": {}, "error": {}}}, "restoreSessionBody": "The app now tries to restore your session from the backup. Please report this error to the developers at {url}. The error message is: {error}", "@restoreSessionBody": {"type": "text", "placeholders": {"url": {}, "error": {}}}, "forwardMessageTo": "Forward message to {roomName}?", "@forwardMessageTo": {"type": "text", "placeholders": {"roomName": {}}}, "sendReadReceipts": "Send read receipts", "sendTypingNotificationsDescription": "Other participants in a chat can see when you are typing a new message.", "sendReadReceiptsDescription": "<PERSON><PERSON> Teilnehmer in einem Cha<PERSON> können sehen, ob du eine Nachricht gelesen hast.", "formattedMessages": "Formatted messages", "formattedMessagesDescription": "Display rich message content like bold text using markdown.", "verifyOtherUser": "🔐 Verify other user", "verifyOtherUserDescription": "If you verify another user, you can be sure that you know who you are really writing to. 💪\n\nWhen you start a verification, you and the other user will see a popup in the app. There you will then see a series of emojis or numbers that you have to compare with each other.\n\nThe best way to do this is to meet up or start a video call. 👭", "verifyOtherDevice": "🔐 Verify other device", "verifyOtherDeviceDescription": "When you verify another device, those devices can exchange keys, increasing your overall security. 💪 When you start a verification, a popup will appear in the app on both devices. There you will then see a series of emojis or numbers that you have to compare with each other. It's best to have both devices handy before you start the verification. 🤳", "acceptedKeyVerification": "{sender} accepted key verification", "@acceptedKeyVerification": {"type": "text", "placeholders": {"sender": {}}}, "canceledKeyVerification": "{sender} canceled key verification", "@canceledKeyVerification": {"type": "text", "placeholders": {"sender": {}}}, "completedKeyVerification": "{sender} completed key verification", "@completedKeyVerification": {"type": "text", "placeholders": {"sender": {}}}, "isReadyForKeyVerification": "{sender} is ready for key verification", "@isReadyForKeyVerification": {"type": "text", "placeholders": {"sender": {}}}, "requestedKeyVerification": "{sender} requested key verification", "@requestedKeyVerification": {"type": "text", "placeholders": {"sender": {}}}, "startedKeyVerification": "{sender} started key verification", "@startedKeyVerification": {"type": "text", "placeholders": {"sender": {}}}, "transparent": "Transparent", "myProfile": "My profile", "connectedDevices": "Connected devices"}