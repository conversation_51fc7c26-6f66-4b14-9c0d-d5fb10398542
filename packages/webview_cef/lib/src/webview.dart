import 'dart:async';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';

import 'webview_events_listener.dart';
import 'webview_javascript.dart';
import 'webview_auth_dialog.dart';

const MethodChannel _pluginChannel = MethodChannel("webview_cef");

// we need to hold references of all the controllers
// to execute the right callbacks
final Map<String, WebViewControllerD> registeredControllers = <String, WebViewControllerD>{};

class WebViewControllerD extends ValueNotifier<bool> {
  late Completer<void> _creatingCompleter;
  int _textureId = 0;
  bool _isDisposed = false;
  final showAuthDialog = ValueNotifier<bool>(false);
  WebviewEventsListener? _listener;
  //bool _focusEditable = false;

  final Map<String, JavascriptChannel> _javascriptChannels = <String, JavascriptChannel>{};

  WebViewControllerD() : super(false) {
    _creatingCompleter = Completer<void>();
  }
  Future<void> get ready => _creatingCompleter.future;

  int get TextureId => _textureId;

  /// Initializes the underlying platform view.
  Future<void> initialize({String? initUrl, String? cachePath}) async {
    if (_isDisposed) {
      return Future<void>.value();
    }
    if (!_creatingCompleter.isCompleted) {
      try {
        _textureId = await _pluginChannel
                .invokeMethod<int>('init', [(initUrl ??= "about:blank"), (cachePath ??= "")]) ??
            0;

        _pluginChannel.setMethodCallHandler(_methodCallhandler);
        registeredControllers[_textureId.toString()] = this;
      } on PlatformException catch (e) {
        _creatingCompleter.completeError(e);
      }
    }
    return _creatingCompleter.future;
  }

  // we must expand this function cause we have just one MethodChannel
  // but many browsers
  Future<dynamic> _methodCallhandler(MethodCall call) async {
    String texId = call.arguments['textureId'];
    int textureId = int.parse(texId);
    switch (call.method) {
      case "urlChanged":
        String url = call.arguments['url'];
        registeredControllers[texId]?._listener?.onUrlChanged?.call(url);
        return [textureId];
      case "titleChanged":
        String title = call.arguments['title'];
        registeredControllers[texId]?._listener?.onTitleChanged?.call(title);
        return [textureId];
      case "allCookiesVisited":
        registeredControllers[texId]
            ?._listener
            ?.onAllCookiesVisited
            ?.call(Map.from(call.arguments));
        return [textureId];
      case "urlCookiesVisited":
        registeredControllers[texId]
            ?._listener
            ?.onUrlCookiesVisited
            ?.call(Map.from(call.arguments));
        return [textureId];
      case "javascriptChannelMessage":
        _handleJavascriptChannelMessage(call.arguments['textureId'], call.arguments['channel'],
            call.arguments['message'], call.arguments['callbackId'], call.arguments['frameId']);
        return [textureId];
      case "pageLoadStart":
        String texId = call.arguments['textureId'];
        String url = call.arguments['url'];
        if (registeredControllers.containsKey(texId)) {
          registeredControllers[texId]?._listener?.onPageLoadStart?.call(url);
        }
        return [textureId];
      case "pageLoadEnd":
        String texId = call.arguments['textureId'];
        String url = call.arguments['url'];
        if (registeredControllers.containsKey(texId)) {
          registeredControllers[texId]?._listener?.onPageFinished?.call(url);
        }
        return [textureId];
      case "browserInited":
        String texId = call.arguments['textureId'];
        if (registeredControllers.containsKey(texId)) {
          registeredControllers[texId]?.value = true;
          registeredControllers[texId]?._creatingCompleter.complete();
          registeredControllers[texId]?._listener?.onPageFinished?.call(call.arguments);
          debugPrint("browserInited: id: $texId");
        } else {
          debugPrint("no controller for $texId ($_textureId)");
        }
        return [textureId];
      case "showAuthDialog":
        String texId = call.arguments['textureId'];
        if (registeredControllers.containsKey(texId)) {
          registeredControllers[texId]?.showAuthDialog.value = true;
        }
        return [textureId];
      case "onNotHandledNavigationRequest":
        String texId = call.arguments['textureId'];
        String url = call.arguments['requestUrl'];
        if (registeredControllers.containsKey(texId)) {
          final c = registeredControllers[texId];
          c?._listener?.onNotHandledNavigationRequest?.call(url);
        }
        return [textureId];
      default:
        debugPrint("Callback not registered: ${call.method}");
    }
  }

  setWebviewListener(WebviewEventsListener listener) {
    _listener = listener;
  }

  @override
  Future<void> dispose() async {
    await _creatingCompleter.future;
    if (!_isDisposed) {
      _isDisposed = true;
      registeredControllers.remove(_textureId.toString());
      _javascriptChannels.clear();
      await _pluginChannel.invokeMethod('dispose', [_textureId]);
    }
    super.dispose();
  }

  /// Loads the given [url].
  Future<void> loadUrl(String url) async {
    if (_isDisposed) {
      return;
    }
    assert(value);
    return _pluginChannel.invokeMethod('loadUrl', [_textureId, url]);
  }

  /// Reloads the current document.
  Future<void> reload() async {
    if (_isDisposed) {
      return;
    }
    assert(value);
    return _pluginChannel.invokeMethod('reload', [_textureId]);
  }

  Future<void> goForward() async {
    if (_isDisposed) {
      return;
    }
    assert(value);
    return _pluginChannel.invokeMethod('goForward', [_textureId]);
  }

  Future<void> goBack() async {
    if (_isDisposed) {
      return;
    }
    assert(value);
    return _pluginChannel.invokeMethod('goBack', [_textureId]);
  }

  /// if devtools are already open it closes them
  Future<void> openDevTools() async {
    if (_isDisposed) {
      return;
    }
    assert(value);
    return _pluginChannel.invokeMethod('openDevTools', [_textureId]);
  }

  Future<void> setClientFocus(bool focus) async {
    if (_isDisposed) {
      return;
    }
    assert(value);
    return _pluginChannel.invokeMethod('setClientFocus', [_textureId, focus]);
  }

  Future<void> setCookie(String domain, String key, String val) async {
    if (_isDisposed) {
      return;
    }
    assert(value);
    return _pluginChannel.invokeMethod('setCookie', [_textureId, domain, key, val]);
  }

  Future<void> deleteCookie(String domain, String key) async {
    if (_isDisposed) {
      return;
    }
    assert(value);
    return _pluginChannel.invokeMethod('deleteCookie', [_textureId, domain, key]);
  }

  Future<void> visitAllCookies() async {
    if (_isDisposed) {
      return;
    }
    assert(value);
    return _pluginChannel.invokeMethod('visitAllCookies', [_textureId]);
  }

  Future<void> visitUrlCookies(String domain, bool isHttpOnly) async {
    if (_isDisposed) {
      return;
    }
    assert(value);
    return _pluginChannel.invokeMethod('visitUrlCookies', [_textureId, domain, isHttpOnly]);
  }

  Future<void> setJavaScriptChannels(Set<JavascriptChannel> channels) async {
    if (_isDisposed) {
      return;
    }
    assert(value);
    _assertJavascriptChannelNamesAreUnique(channels);
    Map<String, List<String>?> channelMap = <String, List<String>?>{};
    for (JavascriptChannel channel in channels) {
      _javascriptChannels[channel.name] = channel;
      channelMap[channel.name] = channel.channelFunctions?.toList();
    }

    return _pluginChannel.invokeMethod('setJavaScriptChannels', [_textureId, channelMap]);
  }

  Future<void> sendJavaScriptChannelCallBack(
      bool error, String result, String callbackId, String frameId) async {
    if (_isDisposed) {
      return;
    }
    assert(value);
    return _pluginChannel.invokeMethod(
        'sendJavaScriptChannelCallBack', [_textureId, error, result, callbackId, frameId]);
  }

  Future<void> executeJavaScript(String code) async {
    if (_isDisposed) {
      return;
    }
    assert(value);
    return _pluginChannel.invokeMethod('executeJavaScript', [_textureId, code]);
  }

  Future<void> basicAuth(bool? canceled, String? username, String? password) async {
    if (_isDisposed) {
      return;
    }
    assert(value);
    return _pluginChannel.invokeMethod('basicAuth', [_textureId, canceled, username, password]);
  }

  /// Moves the virtual cursor to [position].
  Future<void> _cursorMove(Offset position) async {
    if (_isDisposed) {
      return;
    }
    assert(value);
    return _pluginChannel
        .invokeMethod('cursorMove', [_textureId, position.dx.round(), position.dy.round()]);
  }

  Future<void> _cursorDragging(Offset position) async {
    if (_isDisposed) {
      return;
    }
    assert(value);
    return _pluginChannel
        .invokeMethod('cursorDragging', [_textureId, position.dx.round(), position.dy.round()]);
  }

  Future<void> _cursorClickDown(Offset position) async {
    if (_isDisposed) {
      return;
    }
    assert(value);
    return _pluginChannel
        .invokeMethod('cursorClickDown', [_textureId, position.dx.round(), position.dy.round()]);
  }

  Future<void> _cursorClickUp(Offset position) async {
    if (_isDisposed) {
      return;
    }
    assert(value);
    return _pluginChannel
        .invokeMethod('cursorClickUp', [_textureId, position.dx.round(), position.dy.round()]);
  }

  /// Sets the horizontal and vertical scroll delta.
  Future<void> _setScrollDelta(Offset position, int dx, int dy) async {
    if (_isDisposed) {
      return;
    }
    assert(value);
    return _pluginChannel.invokeMethod(
        'setScrollDelta', [_textureId, position.dx.round(), position.dy.round(), dx, dy]);
  }

  /// Sets the surface size to the provided [size].
  Future<void> _setSize(double dpi, Size size) async {
    if (_isDisposed) {
      return;
    }
    assert(value);
    return _pluginChannel.invokeMethod('setSize', [_textureId, dpi, size.width, size.height]);
  }

  Future<void> clearCache({bool fullCache = false}) async {
    if (_isDisposed) {
      return;
    }
    assert(value);
    return _pluginChannel.invokeMethod('clearCache', [_textureId, fullCache]);
  }

  static Future<void> clearAllCaches() async {
    //if (_isDisposed) {
    //  return;
    //}
    //assert(value);
    return _pluginChannel.invokeMethod('clearCache', [0, true]);
  }

  Set<String> _extractJavascriptChannelNames(Set<JavascriptChannel> channels) {
    final Set<String> channelNames =
        channels.map((JavascriptChannel channel) => channel.name).toSet();
    return channelNames;
  }

  void _handleJavascriptChannelMessage(final String textureId, final String channelName,
      final String message, final String callbackId, final String frameId) {
    if (registeredControllers.containsKey(textureId)) {
      if (registeredControllers[textureId]!._javascriptChannels.containsKey(channelName)) {
        registeredControllers[textureId]!
            ._javascriptChannels[channelName]!
            .onMessageReceived(JavascriptMessage(message, callbackId, frameId));
      } else {
        debugPrint('Channel "$channelName" does not exist for texture "$textureId"');
      }
    } else {
      debugPrint('no Channels registered for texture "$textureId"');
    }
  }

  void _assertJavascriptChannelNamesAreUnique(final Set<JavascriptChannel>? channels) {
    if (channels == null || channels.isEmpty) {
      return;
    }
    assert(_extractJavascriptChannelNames(channels).length == channels.length);
  }
}

class WebView extends StatefulWidget {
  final WebViewControllerD controller;

  const WebView(this.controller, {super.key});

  @override
  WebViewState createState() => WebViewState();
}

class WebViewState extends State<WebView> {
  final GlobalKey _key = GlobalKey();
  late final _focusNode = FocusNode();
  WebViewControllerD get _controller => widget.controller;

  @override
  void initState() {
    super.initState();
    // Report initial surface size
    WidgetsBinding.instance.addPostFrameCallback((_) => _reportSurfaceSize(context));
  }

  @override
  void dispose() {
    super.dispose();
    //debugPrint("dispose...take focus from ${_controller._textureId}");
    _controller.setClientFocus(false);
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<bool>(
        valueListenable: _controller.showAuthDialog,
        builder: (context, value, _) => Stack(
              children: [
                Focus(
                  autofocus: true,
                  focusNode: _focusNode,
                  canRequestFocus: true,
                  debugLabel: "webview_cef",
                  onFocusChange: (focused) {
                    //debugPrint("set focus to ${focused} for ${_controller._textureId}");
                    _controller.setClientFocus(focused);
                  },
                  child: SizedBox.expand(key: _key, child: _buildInner()),
                ),
                if (value) AuthDialog(_controller),
              ],
            ));
  }

  Widget _buildInner() {
    return NotificationListener<SizeChangedLayoutNotification>(
      onNotification: (notification) {
        _reportSurfaceSize(context);
        return true;
      },
      child: SizeChangedLayoutNotifier(
        child: Listener(
          onPointerHover: (ev) {
            _controller._cursorMove(ev.localPosition);
          },
          onPointerDown: (ev) {
            if (!_focusNode.hasFocus) {
              _focusNode.requestFocus();
              Future.delayed(const Duration(milliseconds: 50), () {
                if (!_focusNode.hasFocus) {
                  _focusNode.requestFocus();
                }
              });
            }
            _controller._cursorClickDown(ev.localPosition);
          },
          onPointerUp: (ev) {
            _controller._cursorClickUp(ev.localPosition);
          },
          onPointerMove: (ev) {
            _controller._cursorDragging(ev.localPosition);
          },
          onPointerSignal: (signal) {
            if (signal is PointerScrollEvent) {
              _controller._setScrollDelta(signal.localPosition, signal.scrollDelta.dx.round(),
                  signal.scrollDelta.dy.round());
            }
          },
          onPointerPanZoomUpdate: (event) {
            _controller._setScrollDelta(
                event.localPosition, event.panDelta.dx.round(), event.panDelta.dy.round());
          },
          child: Texture(textureId: _controller._textureId),
        ),
      ),
    );
  }

  void _reportSurfaceSize(BuildContext context) async {
    double dpi = MediaQuery.of(context).devicePixelRatio;
    final box = _key.currentContext?.findRenderObject() as RenderBox?;
    if (box != null) {
      await _controller.ready;
      unawaited(_controller._setSize(dpi, Size(box.size.width, box.size.height)));
    }
  }
}
