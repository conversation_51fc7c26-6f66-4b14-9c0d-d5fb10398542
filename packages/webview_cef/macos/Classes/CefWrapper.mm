//
//  CefWrapper.m
//  Pods-Runner
//
//  Created by <PERSON><PERSON> on 2022/8/18.
//

#import "CefWrapper.h"
#import "CefTexture.h"
#import <Foundation/Foundation.h>
#import "include/wrapper/cef_library_loader.h"
#import "include/cef_app.h"
#import "../../common/webview_app.h"
#import "../../common/webview_handler.h"
#import "../../common/webview_cookieVisitor.h"
#import "../../common/webview_js_handler.h"
#import "../../common/webview_data.h"

#include <map> //?
#include <thread>

CefRefPtr<WebviewHandler> handler(new WebviewHandler());
CefRefPtr<WebviewApp> app(new WebviewApp(handler));
int argc = 2;
char argv[][32] = {"--disable-audio-input", "--disable-audio-output"};
char * test = &argv[0][0];
CefMainArgs main_args = CefMainArgs();//(argc, &test);

NSObject<FlutterTextureRegistry>* tr;
CGFloat scaleFactor = 0.0;

static NSTimer* _timer;
static id _keyDownMonitor;
static id _keyUpMonitor;

NSMutableDictionary *textureMap = [NSMutableDictionary dictionary];

//dispatch_semaphore_t lock = dispatch_semaphore_create(1);

FlutterMethodChannel* f_channel;

bool init = false;

@implementation CefWrapper

+ (void)init {
    if(!init) {
    CefScopedLibraryLoader loader;
    
    if(!loader.LoadInMain()) {
        printf("CEF: load cef err");
    }
    CefExecuteProcess(main_args, nullptr, nullptr);
    }
} 

+ (void)doMessageLoopWork {
    CefDoMessageLoopWork();
}

+ (bool)NavRequest: (int64_t)textureId url:(NSString*)url {
        /*WebviewData* view = webview_cef::getWebviews()->at(textureId).get();
        NSString* host = [NSString stringWithCString:view->domain.c_str() encoding:NSUTF8StringEncoding];
        if(![host containsString:url] && ![url containsString:host]) {
        	// handle no nav
        	NSMutableDictionary * dict = [NSMutableDictionary dictionary];
            dict[@"textureId"] = [NSString stringWithFormat:@"%lld", textureId];
            dict[@"requestUrl"] = url;
        	[f_channel invokeMethod:@"onNotHandledNavigationRequest" arguments:dict];
        	return false;
        }*/
        return true;
}

+ (int64_t)startCef: (NSString*)initUrl cachePath:(NSString*)cachePath {
    CefTexture *texture = [[CefTexture alloc] init];
    int64_t textureId = [tr registerTexture:texture];
    //NSNumber *texId = @(textureId);
    NSString *key = [NSString stringWithFormat:@"%lld", textureId];
    [textureMap setObject:texture forKey:key];

    std::function<void(const void*, int32_t width, int32_t height)> callback = 
        [=] (const void* buffer, int32_t width, int32_t height) {

        NSDictionary* dic = @{
            (__bridge NSString*)kCVPixelBufferPixelFormatTypeKey: @(kCVPixelFormatType_32BGRA),
            (__bridge NSString*)kCVPixelBufferIOSurfacePropertiesKey : @{},
            (__bridge NSString*)kCVPixelBufferOpenGLCompatibilityKey : @YES,
            (__bridge NSString*)kCVPixelBufferMetalCompatibilityKey : @YES,
        };

        CVPixelBufferRef buf = NULL;
        CVPixelBufferCreate(kCFAllocatorDefault,  width,
                            height, kCVPixelFormatType_32BGRA,
                            (__bridge CFDictionaryRef)dic, &buf);
        
        //copy data
        CVPixelBufferLockBaseAddress(buf, 0);
        char *copyBaseAddress = (char *) CVPixelBufferGetBaseAddress(buf);
        
        //MUST align pixel to pixelBuffer. Otherwise cause render issue. see https://www.codeprintr.com/thread/6563066.html about 16 bytes align
        size_t bytesPerRow = CVPixelBufferGetBytesPerRowOfPlane(buf, 0);
        char* src = (char*) buffer;
        int actureRowSize = width * 4;
        for(int line = 0; line < height; line++) {
            memcpy(copyBaseAddress, src, actureRowSize);
            src += actureRowSize;
            copyBaseAddress += bytesPerRow;
        }
        CVPixelBufferUnlockBaseAddress(buf, 0);

        dispatch_semaphore_wait(lock, DISPATCH_TIME_FOREVER);
        if(texture.buf_cache) {
            CVPixelBufferRelease(texture.buf_cache);
        }
        texture.buf_cache = buf;
        dispatch_semaphore_signal(lock);

        [tr textureFrameAvailable:textureId];
    };

    webview_cef::getWebviews()->insert(std::make_pair(textureId, std::unique_ptr<WebviewData>(new WebviewData)));
    webview_cef::getWebviews()->at(textureId).get()->textureId = textureId;
    webview_cef::getWebviews()->at(textureId).get()->paintCallback = callback;
    webview_cef::getWebviews()->at(textureId).get()->initUrl = std::string([initUrl UTF8String]);
    webview_cef::getWebviews()->at(textureId).get()->setDomain();

    if(cachePath != @"") {
		webview_cef::setGlobalCachePath(std::string([cachePath UTF8String]));
	}

    if (!init){
        printf("CEF: init message loop\n");
        //kill timer cb
        handler.get()->onKillMessageLoop = []() {
            [NSEvent removeMonitor:_keyDownMonitor];
            [NSEvent removeMonitor:_keyUpMonitor];
            _keyDownMonitor = nil;
            _keyUpMonitor = nil;
            [_timer invalidate];
            _timer = nil; // Optional: Set the variable to nil to release the reference
        };
        //url change cb
        handler.get()->onUrlChangedCb = [](std::string textureId, std::string url) {

            NSMutableDictionary * dict = [NSMutableDictionary dictionary];
            dict[@"textureId"] = [NSString stringWithCString:textureId.c_str() encoding:NSUTF8StringEncoding];
            dict[@"url"] = [NSString stringWithCString:url.c_str() encoding:NSUTF8StringEncoding];
            [f_channel invokeMethod:@"urlChanged" arguments:dict];
        };
        //title change cb
        handler.get()->onTitleChangedCb = [](std::string textureId, std::string title) {
            NSMutableDictionary * dict = [NSMutableDictionary dictionary];
            dict[@"textureId"] = [NSString stringWithCString:textureId.c_str() encoding:NSUTF8StringEncoding];
            dict[@"title"] = [NSString stringWithCString:title.c_str() encoding:NSUTF8StringEncoding];
            [f_channel invokeMethod:@"titleChanged" arguments:dict];
        };
        //allcookie visited cb
        handler.get()->onAllCookieVisitedCb = [](std::map<std::string, std::map<std::string, std::string>> cookies) {
            NSMutableDictionary * dict = [NSMutableDictionary dictionary];
            for(auto &cookie : cookies)
            {
                NSString * domain = [NSString stringWithCString:cookie.first.c_str() encoding:NSUTF8StringEncoding];
                NSMutableDictionary * tempdict = [NSMutableDictionary dictionary];
                for(auto &c : cookie.second)
                {
                    NSString * key = [NSString stringWithCString:c.first.c_str() encoding:NSUTF8StringEncoding];
                    NSString * val = [NSString stringWithCString:c.second.c_str() encoding:NSUTF8StringEncoding];
                    tempdict[key] = val;
                }
                dict[domain] = tempdict;
            }
            [f_channel invokeMethod:@"allCookiesVisited" arguments:dict];
        };
        
        //urlcookie visited cb
        handler.get()->onUrlCookieVisitedCb = [](std::map<std::string, std::map<std::string, std::string>> cookies) {
            NSMutableDictionary * dict = [NSMutableDictionary dictionary];
            for(auto &cookie : cookies)
            {
                NSString * domain = [NSString stringWithCString:cookie.first.c_str() encoding:NSUTF8StringEncoding];
                NSMutableDictionary * tempdict = [NSMutableDictionary dictionary];
                for(auto &c : cookie.second)
                {
                    NSString * key = [NSString stringWithCString:c.first.c_str() encoding:NSUTF8StringEncoding];
                    NSString * val = [NSString stringWithCString:c.second.c_str() encoding:NSUTF8StringEncoding];
                    tempdict[key] = val;
                }
                dict[domain] = tempdict;
            }
            [f_channel invokeMethod:@"urlCookiesVisited" arguments:dict];
        };

        //JavaScriptChannel called
        handler.get()->onJavaScriptChannelMessage = [](std::string textureId, std::string channelName, std::string message, std::string callbackId, std::string frameId) {
            NSMutableDictionary * dict = [NSMutableDictionary dictionary];
            dict[@"textureId"] = [NSString stringWithCString:textureId.c_str() encoding:NSUTF8StringEncoding];
            dict[@"channel"] = [NSString stringWithCString:channelName.c_str() encoding:NSUTF8StringEncoding];
            dict[@"message"]  = [NSString stringWithCString:message.c_str() encoding:NSUTF8StringEncoding];
            dict[@"callbackId"]  = [NSString stringWithCString:callbackId.c_str() encoding:NSUTF8StringEncoding];
            dict[@"frameId"]  = [NSString stringWithCString:frameId.c_str() encoding:NSUTF8StringEncoding];
            [f_channel invokeMethod:@"javascriptChannelMessage" arguments:dict];
        };

        handler.get()->onPageLoadStart = [](std::string textureId, std::string url)
		{
			NSMutableDictionary * dict = [NSMutableDictionary dictionary];
            dict[@"textureId"] = [NSString stringWithCString:textureId.c_str() encoding:NSUTF8StringEncoding];
            dict[@"url"] = [NSString stringWithCString:url.c_str() encoding:NSUTF8StringEncoding];
            [f_channel invokeMethod:@"pageLoadStart" arguments:dict];
		};

        handler.get()->onPageLoadEnd = [](std::string textureId, std::string url)
		{
			NSMutableDictionary * dict = [NSMutableDictionary dictionary];
            dict[@"textureId"] = [NSString stringWithCString:textureId.c_str() encoding:NSUTF8StringEncoding];
            dict[@"url"] = [NSString stringWithCString:url.c_str() encoding:NSUTF8StringEncoding];
            [f_channel invokeMethod:@"pageLoadEnd" arguments:dict];
		};

        handler.get()->onBrowserCreated = [](std::string textureId) {
			NSMutableDictionary * dict = [NSMutableDictionary dictionary];
            dict[@"textureId"] = [NSString stringWithCString:textureId.c_str() encoding:NSUTF8StringEncoding];
            [f_channel invokeMethod:@"browserInited" arguments:dict];
		};

		handler.get()->m_RequestHandler.get()->onShowAuthDialog = [](int64_t textureId){
			NSMutableDictionary * dict = [NSMutableDictionary dictionary];
            dict[@"textureId"] = [NSString stringWithFormat:@"%lld", textureId];
            [f_channel invokeMethod:@"showAuthDialog" arguments:dict];
        };
        handler.get()->m_RequestHandler.get()->onNavigationRequest = [](int64_t textureId, std::string url)->bool{
            return [CefWrapper NavRequest:textureId url:[NSString stringWithCString:url.c_str() encoding:NSUTF8StringEncoding]];
        };

        CefSettings settings;
        settings.windowless_rendering_enabled = true;
        settings.external_message_pump = true;
        std::string cache_path = webview_cef::getGlobalCachePath();
        if(!cache_path.empty()) {
            cache_path.append("/cef_cache");
        } else {
            cache_path = std::string(std::getenv("HOME"));
            cache_path.append("/Library/Caches/cef_cache");
		}
        webview_cef::setGlobalCachePath(cache_path);
        CefString(&settings.cache_path).FromASCII(cache_path.c_str());
        CefString(&settings.browser_subprocess_path) = "/Library/Caches";
        settings.no_sandbox = false;
        settings.log_severity = LOGSEVERITY_DISABLE;

        CefInitialize(main_args, settings, app.get(), nullptr);
        _timer = [NSTimer timerWithTimeInterval:0.016f target:self selector:@selector(doMessageLoopWork) userInfo:nil repeats:YES];
        [[NSRunLoop mainRunLoop] addTimer: _timer forMode:NSRunLoopCommonModes];

        _keyDownMonitor = [NSEvent addLocalMonitorForEventsMatchingMask:NSEventMaskKeyDown handler:^NSEvent * _Nullable(NSEvent * _Nonnull event) {
            [self processKeyboardEvent:event];
            return event;
        }];
        
        _keyUpMonitor = [NSEvent addLocalMonitorForEventsMatchingMask:NSEventMaskKeyUp handler:^NSEvent * _Nullable(NSEvent * _Nonnull event) {
            [self processKeyboardEvent:event];
            return event;
        }];
        init = true;
    }
    app.get()->CreateNewBrowser(textureId, std::string([initUrl cStringUsingEncoding:NSUTF8StringEncoding]));
    return textureId;
}

+ (void)processKeyboardEvent: (NSEvent*) event {

NSEvent* ns_event = (NSEvent*)event;

    if (([ns_event type] == NSEventTypeKeyDown) || ([ns_event type] == NSEventTypeKeyUp))
    {
        CefKeyEvent keyEvent;

        NSString *c = [ns_event characters];
        if ([c length] > 0)
        {
            keyEvent.character = [c characterAtIndex:0];
        }

        NSString *cim = [ns_event charactersIgnoringModifiers];
        if ([cim length] > 0)
        {
            keyEvent.unmodified_character = [cim characterAtIndex:0];
        }

        keyEvent.native_key_code = [ns_event keyCode];
        keyEvent.is_system_key = false;
        keyEvent.modifiers = [self getModifiersForEvent:event];



        if ([ns_event type] == NSEventTypeKeyDown)
        {
            // hotfix for mac to not going into fullscreen and so on
            
            if(!([ns_event keyCode] == 2/*d*/ || [ns_event keyCode] == 3/*f*/ || [ns_event keyCode] == 14/*e*/)) {
                keyEvent.type =  KEYEVENT_KEYDOWN;
                handler.get()->sendKeyEvent(keyEvent);
            }

            keyEvent.type =  KEYEVENT_CHAR;
            handler.get()->sendKeyEvent(keyEvent);
        }
        else
        if ([ns_event type] == NSEventTypeKeyUp)
        {
            keyEvent.type =  KEYEVENT_KEYUP;
            handler.get()->sendKeyEvent(keyEvent);
        }
    }
}

+ (int)getModifiersForEvent:(NSEvent*)event {
    int modifiers = 0;
    
    if ([event modifierFlags] & NSEventModifierFlagControl)
        modifiers |= EVENTFLAG_CONTROL_DOWN;
    if ([event modifierFlags] & NSEventModifierFlagShift)
        modifiers |= EVENTFLAG_SHIFT_DOWN;
    if ([event modifierFlags] & NSEventModifierFlagOption)
        modifiers |= EVENTFLAG_ALT_DOWN;
    if ([event modifierFlags] & NSEventModifierFlagCommand)
        modifiers |= EVENTFLAG_COMMAND_DOWN;
    if ([event modifierFlags] & NSEventModifierFlagCapsLock)
        modifiers |= EVENTFLAG_CAPS_LOCK_ON;
    
    if ([event type] == NSEventTypeKeyUp || [event type] == NSEventTypeKeyDown ||
        [event type] == NSEventTypeFlagsChanged) {
        // Only perform this check for key events
        //    if ([self isKeyPadEvent:event])
        //      modifiers |= EVENTFLAG_IS_KEY_PAD;
    }
    
    // OS X does not have a modifier for NumLock, so I'm not entirely sure how to
    // set EVENTFLAG_NUM_LOCK_ON;
    //
    // There is no EVENTFLAG for the function key either.
    
    // Mouse buttons
    switch ([event type]) {
        case NSEventTypeLeftMouseDragged:
        case NSEventTypeLeftMouseDown:
        case NSEventTypeLeftMouseUp:
            modifiers |= EVENTFLAG_LEFT_MOUSE_BUTTON;
            break;
        case NSEventTypeRightMouseDragged:
        case NSEventTypeRightMouseDown:
        case NSEventTypeRightMouseUp:
            modifiers |= EVENTFLAG_RIGHT_MOUSE_BUTTON;
            break;
        case NSEventTypeOtherMouseDragged:
        case NSEventTypeOtherMouseDown:
        case NSEventTypeOtherMouseUp:
            modifiers |= EVENTFLAG_MIDDLE_MOUSE_BUTTON;
            break;
        default:
            break;
    }
    
    return modifiers;
}

+(void)sendScrollEvent:(int)x y:(int)y deltaX:(int)deltaX deltaY:(int)deltaY {
    handler.get()->sendScrollEvent(x, y, -deltaX, -deltaY);
}

+ (void)cursorClickUp:(int)x y:(int)y {
    handler.get()->cursorClick(x, y, true);
}

+ (void)cursorClickDown:(int)x y:(int)y {
    handler.get()->cursorClick(x, y, false);
}

+ (void)cursorMove:(int)x y:(int)y dragging:(bool)dragging {
    handler.get()->cursorMove(x, y, dragging);
}

+ (void)sizeChanged: (int64_t)textureId dpi:(float)dpi width:(int)width height:(int)height {
    handler.get()->changeSize(textureId, dpi, width, height);
}

+ (void)loadUrl: (int64_t)textureId url:(NSString*)url {
    handler.get()->loadUrl(textureId, std::string([url cStringUsingEncoding:NSUTF8StringEncoding]));
}

+ (void) setClientFocus: (int64_t)textureId focused:(bool)focused{
	handler.get()->setClientFocus(textureId, focused);
}

+ (void)goForward: (int64_t)textureId {
    handler.get()->goForward(textureId);
}

+ (void)goBack: (int64_t)textureId {
    handler.get()->goBack(textureId);
}

+ (void)reload: (int64_t)textureId {
    handler.get()->reload(textureId);
}

+ (void)openDevTools: (int64_t)textureId {
    handler.get()->openDevTools(textureId);
}
/*
- (CVPixelBufferRef _Nullable)copyPixelBuffer {
    dispatch_semaphore_wait(lock, DISPATCH_TIME_FOREVER);
    buf_tmp = buf_cache;
    CVPixelBufferRetain(buf_tmp);
    dispatch_semaphore_signal(lock);
    return buf_tmp;
}*/

+ (void)setMethodChannel: (FlutterMethodChannel*)channel {
    f_channel = channel;
}

+ (void)setCookie: (NSString *)domain key:(NSString *) key value:(NSString *)value {
    handler.get()->setCookie(std::string([domain cStringUsingEncoding:NSUTF8StringEncoding]), std::string([key cStringUsingEncoding:NSUTF8StringEncoding]), std::string([value cStringUsingEncoding:NSUTF8StringEncoding]));
}

+ (void)deleteCookie: (NSString *)domain key:(NSString *) key {
    handler.get()->deleteCookie(std::string([domain cStringUsingEncoding:NSUTF8StringEncoding]), std::string([key cStringUsingEncoding:NSUTF8StringEncoding]));
}

+ (void)visitAllCookies {
    handler.get()->visitAllCookies();
}

+ (void)visitUrlCookies: (NSString *)domain isHttpOnly:(bool)isHttpOnly {
    handler.get()->visitUrlCookies(std::string([domain cStringUsingEncoding:NSUTF8StringEncoding]), isHttpOnly);
}

+ (void) setJavaScriptChannels: (int64_t)textureId channels:(NSDictionary *)channels {
    std::map<std::string, std::vector<std::string>> JSChannels;
    NSEnumerator * enum1 = [channels keyEnumerator];
    NSString * value;
    while (value = [enum1 nextObject]) {
        std::string name = std::string([value cStringUsingEncoding:NSUTF8StringEncoding]);
        std::vector<std::string> JSfunctions;
        NSArray * functions = [channels valueForKey:value];
        if(functions) {
            NSEnumerator * enum2 = [functions objectEnumerator];
            NSString * function;
            while (function = [enum2 nextObject]) {
                JSfunctions.push_back(std::string([function cStringUsingEncoding:NSUTF8StringEncoding]));
            }
        }
        JSChannels.insert(std::make_pair(name, JSfunctions));
    }
    webview_cef::getWebviews()->at(textureId)->JSChannels = JSChannels;
    handler.get()->setJavaScriptChannels(textureId, JSChannels);
}

+ (void) sendJavaScriptChannelCallBack: (bool)error  result:(NSString *)result callbackId:(NSString *)callbackId frameId:(NSString *)frameId {
    handler.get()->sendJavaScriptChannelCallBack(error, std::string([result cStringUsingEncoding:NSUTF8StringEncoding]), 
        std::string([callbackId cStringUsingEncoding:NSUTF8StringEncoding]), std::string([frameId cStringUsingEncoding:NSUTF8StringEncoding]));
}

+ (void) executeJavaScript: (int64_t)textureId code:(NSString *)code {
    handler.get()->executeJavaScript(textureId, std::string([code cStringUsingEncoding:NSUTF8StringEncoding]));
}

+ (void) clearCache: (int64_t)textureId fullCache:(bool)fullCache {
    handler.get()->clearCache(textureId, fullCache);
}

+ (void) dispose: (int64_t)textureId {
    handler.get()->closeBrowser(textureId);
    [tr unregisterTexture:textureId];
	webview_cef::getWebviews()->erase(textureId);
	if(webview_cef::getWebviews()->empty()) {
		init = false;
		/*handler->onPaintCallback = NULL;
		handler->onUrlChangedCb = NULL;
		handler->onTitleChangedCb = NULL;
		handler->onAllCookieVisitedCb = NULL;
		handler->onUrlCookieVisitedCb = NULL;
		handler->onJavaScriptChannelMessage = NULL;
		handler->onKillMessageLoop = NULL;
		handler->onBrowserCreated = NULL;*/
	}
}

+ (void) basicAuth: (int64_t)textureId canceled:(bool)canceled username:(NSString *)username password:(NSString *)password {
    std::string user = username != [NSNull null] ? std::string([username cStringUsingEncoding:NSUTF8StringEncoding]) : "";
    std::string pass = password != [NSNull null] ? std::string([password cStringUsingEncoding:NSUTF8StringEncoding]) : "";
	handler.get()->m_RequestHandler.get()->Continue(canceled, user, pass);
}

@end
