// Copyright (c) 2013 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.

#include "webview_handler.h"

#include <sstream>
#include <string>
#include <iostream>
#include <filesystem>
#include <vector>
#include <cstdint>

#include "include/base/cef_callback.h"
#include "include/cef_app.h"
#include "include/cef_parser.h"
#include "include/views/cef_browser_view.h"
#include "include/views/cef_window.h"
#include "include/wrapper/cef_closure_task.h"
#include "include/wrapper/cef_helpers.h"

#include "webview_js_handler.h"

#include "webview_data.h"

namespace fs = std::filesystem;

namespace {
WebviewHandler* g_instance = nullptr;

// Returns a data: URI with the specified contents.
std::string GetDataURI(const std::string& data, const std::string& mime_type) {
    return "data:" + mime_type + ";base64," +
    CefURIEncode(CefBase64Encode(data.data(), data.size()), false)
        .ToString();
}

}  // namespace



WebviewHandler::WebviewHandler() {
    DCHECK(!g_instance);
    g_instance = this;
    m_RequestHandler = new RequestHandler();
}

WebviewHandler::~WebviewHandler() {
    g_instance = nullptr;
    m_RequestHandler = nullptr;
}

// static
WebviewHandler* WebviewHandler::GetInstance() {
    return g_instance;
}

bool WebviewHandler::OnProcessMessageReceived(
    CefRefPtr<CefBrowser> browser, CefRefPtr<CefFrame> frame,
     CefProcessId source_process, CefRefPtr<CefProcessMessage> message)
{
	std::string message_name = message->GetName();
    if(message_name == kJSCallCppFunctionMessage)
    {
        CefString fun_name = message->GetArgumentList()->GetString(0);
		CefString param = message->GetArgumentList()->GetString(1);
		int js_callback_id = message->GetArgumentList()->GetInt(2);

        if (fun_name.empty() || !(browser.get())) {
		    return false;
	    }

        std::string texId("");
        webview_cef::WebviewMap::iterator wit = webview_cef::getWebviews()->begin();
        for (;  wit != webview_cef::getWebviews()->end(); ++wit) {
            WebviewData *view = (*wit).second.get()->isWebviewForBrowser(browser.get()->GetIdentifier());
            if (view) {
                texId = std::to_string(view->textureId);
                break;
            }
        }
        if (!texId.empty()) {
            onJavaScriptChannelMessage(texId,
                fun_name,param,std::to_string(js_callback_id),std::to_string(frame->GetIdentifier()));
        } else {
            printf("CEF: could not find WebView for browser\n");
        }
    }
    return false;
}

void WebviewHandler::OnTitleChange(CefRefPtr<CefBrowser> browser,
                                  const CefString& title) {
    //todo: title change
    if(onTitleChangedCb) {
        webview_cef::WebviewMap::iterator wit = webview_cef::getWebviews()->begin();
        for (;  wit != webview_cef::getWebviews()->end(); ++wit) {
            WebviewData *view = (*wit).second.get()->isWebviewForBrowser(browser.get()->GetIdentifier());
            if (view) {
                onTitleChangedCb(std::to_string(view->textureId), title);
                break;
            }
        }
    }
}

void WebviewHandler::OnAddressChange(CefRefPtr<CefBrowser> browser,
                             CefRefPtr<CefFrame> frame,
                     const CefString& url) {
    if(onUrlChangedCb) {
        webview_cef::WebviewMap::iterator wit = webview_cef::getWebviews()->begin();
        for (;  wit != webview_cef::getWebviews()->end(); ++wit) {
            WebviewData *view = (*wit).second.get()->isWebviewForBrowser(browser.get()->GetIdentifier());
            if (view) {
                onUrlChangedCb(std::to_string(view->textureId), url);
                break;
            }
        }
    }

    if (browser.get()->GetMainFrame().get()->GetURL().ToString().compare("devtools://devtools/devtools_app.html") == 0 && !devToolsBrowser_) {
        devToolsBrowser_ = browser;
        if(!tmpBrowserList.empty()) {
            std::vector<CefRefPtr<CefBrowser>>::iterator bit = tmpBrowserList.begin();
            for(;bit != tmpBrowserList.end(); ++bit) {
                if((*bit)->IsSame(devToolsBrowser_)) {
                    tmpBrowserList.erase(bit);
                    printf("CEF: Dev Browser created %s\n", tmpBrowserList.empty() ? "0" : "x");
                    break;
                }
            }
        }   
    }
}

void WebviewHandler::onBrowserCreatedCB(int64_t browserId, int64_t map_key) {
   
    if (!CefCurrentlyOn(TID_UI)) {
        // Execute on the UI thread.
        CefPostTask(TID_UI, base::BindOnce(&WebviewHandler::onBrowserCreatedCB, this,
                                               browserId, map_key));
        return;
    }

    webview_cef::WebviewMap * webviews = webview_cef::getWebviews();
    if(webviews->at(map_key) && webviews->at(map_key)->browserId < 0) {
        std::vector<CefRefPtr<CefBrowser>>::iterator bit = tmpBrowserList.begin();
        for(;bit != tmpBrowserList.end(); ++bit) {
            if((*bit).get()->GetIdentifier() == browserId) {
                browser_list_.insert(std::make_pair(map_key, (*bit)));
                webviews->at(map_key)->browserId = (*bit).get()->GetIdentifier();
                std::string texId = std::to_string(map_key);
                tmpBrowserList.erase(bit);
                onBrowserCreated(texId);
                break;
            }
        }
    } else {
        printf("CEF: could not find view\n");
    }
}

void WebviewHandler::OnAfterCreated(CefRefPtr<CefBrowser> browser) {
    CEF_REQUIRE_UI_THREAD();

    this->devRegistration_ = browser->GetHost()->AddDevToolsMessageObserver(this);

    // the browser address from the render thread is not the same
    // as the one from the ui thread, so we have to store the address
    tmpBrowserList.push_back(browser);
}

bool WebviewHandler::DoClose(CefRefPtr<CefBrowser> browser) {
    CEF_REQUIRE_UI_THREAD();    
    // Allow the close. For windowed browsers this will result in the OS close
    // event being sent.
    return false;
}

void WebviewHandler::OnBeforeClose(CefRefPtr<CefBrowser> browser) {
    CEF_REQUIRE_UI_THREAD();

    printf("CEF: closing browser %d\n", browser.get()->GetIdentifier());

    BrowserList::iterator bit = browser_list_.begin();
    for (; bit != browser_list_.end(); ++bit) {
        if ((*bit).second.get()->IsSame(browser)) {
            CefBrowser* br = (*bit).second.release();
            br->Release();
            browser_list_.erase(bit);
            break;
        }
    }

    BrowserList::iterator ait = active_browser_list_.begin();
    for (; ait != active_browser_list_.end(); ++ait) {
        if ((*ait).second.get()->IsSame(browser)) {
            CefBrowser* br = (*ait).second.release();
            br->Release();
            active_browser_list_.erase(ait);
            break;
        }
    }

    if (devToolsBrowser_ && browser.get()->IsSame(devToolsBrowser_)) {
        devToolsBrowser_.reset();
    }

    if (browser.get()->HasAtLeastOneRef()) {
        browser.release()->Release();
    }

    if (browser_list_.empty()) {
        printf("CEF: Quit Message Loop\n");
        // All browser windows have closed. Quit the application message loop.
        onKillMessageLoop();
        CefQuitMessageLoop();
        //PostQuitMessage(0);
    }
}

bool WebviewHandler::OnBeforePopup(CefRefPtr<CefBrowser> browser,
                                  CefRefPtr<CefFrame> frame,
                                  const CefString& target_url,
                                  const CefString& target_frame_name,
                                  WindowOpenDisposition target_disposition,
                                  bool user_gesture,
                                  const CefPopupFeatures& popupFeatures,
                                  CefWindowInfo& windowInfo,
                                  CefRefPtr<CefClient>& client,
                                  CefBrowserSettings& settings,
                                  CefRefPtr<CefDictionaryValue>& extra_info,
                                  bool* no_javascript_access) {

    webview_cef::WebviewMap::iterator wit = webview_cef::getWebviews()->begin();
    for (;  wit != webview_cef::getWebviews()->end(); ++wit) {
        WebviewData *view = (*wit).second.get()->isWebviewForBrowser(browser.get()->GetIdentifier());
        if (view) {
            loadUrl(view->textureId, target_url);
        }
    }
    return true;
}

void WebviewHandler::OnLoadingStateChange(CefRefPtr<CefBrowser> browser,
                                    bool isLoading,
                                    bool canGoBack,
                                    bool canGoForward) {
    
}

void WebviewHandler::OnLoadStart(CefRefPtr<CefBrowser> browser,
                           CefRefPtr<CefFrame> frame,
                           TransitionType transition_type) {

    webview_cef::WebviewMap::iterator wit = webview_cef::getWebviews()->begin();
    for (;  wit != webview_cef::getWebviews()->end(); ++wit) {
        WebviewData *view = (*wit).second.get()->isWebviewForBrowser(browser.get()->GetIdentifier());
        if (view) {
            onPageLoadStart(std::to_string(view->textureId), frame->GetURL());
            if (!view->JSChannels.empty()) {
                setJavaScriptChannels(view->textureId, view->JSChannels);
            }
            break;
        }
    }
}

void WebviewHandler::OnLoadEnd(CefRefPtr<CefBrowser> browser,
                             CefRefPtr<CefFrame> frame,
                             int httpStatusCode) {
    if(onPageLoadEnd) {
        webview_cef::WebviewMap::iterator wit = webview_cef::getWebviews()->begin();
        for (;  wit != webview_cef::getWebviews()->end(); ++wit) {
            WebviewData *view = (*wit).second.get()->isWebviewForBrowser(browser.get()->GetIdentifier());
            if (view) {
                onPageLoadEnd(std::to_string(view->textureId), frame->GetURL());
                break;
            }
        }
    }
}

void WebviewHandler::OnLoadError(CefRefPtr<CefBrowser> browser,
                                CefRefPtr<CefFrame> frame,
                                ErrorCode errorCode,
                                const CefString& errorText,
                                const CefString& failedUrl) {
    CEF_REQUIRE_UI_THREAD();
    
    // Allow Chrome to show the error page.
    if (IsChromeRuntimeEnabled())
        return;
    
    // Don't display an error for downloaded files.
    if (errorCode == ERR_ABORTED)
        return;
    
    // Display a load error message using a data: URI.
    std::stringstream ss;
    ss << "<html><body bgcolor=\"white\">"
    "<h2>Failed to load URL "
    << std::string(failedUrl) << " with error " << std::string(errorText)
    << " (" << errorCode << ").</h2></body></html>";
    
    browser.get()->GetFrame(frame.get()->GetName()).get()->LoadURL(GetDataURI(ss.str(), "text/html"));
}

void WebviewHandler::OnBeforeDownload(
      CefRefPtr<CefBrowser> browser,
      CefRefPtr<CefDownloadItem> download_item,
      const CefString& suggested_name,
      CefRefPtr<CefBeforeDownloadCallback> callback) {

    std::cout << "CEF: Download " << suggested_name.ToString() <<" ?" << std::endl;
    callback.get()->Continue(download_item.get()->GetSuggestedFileName(), true);
}

void WebviewHandler::OnDownloadUpdated(CefRefPtr<CefBrowser> browser,
                                 CefRefPtr<CefDownloadItem> download_item,
                                 CefRefPtr<CefDownloadItemCallback> callback) {

    if (download_item.get()->IsComplete()) {
        std::cout << "CEF: Finished downloading " << download_item.get()->GetSuggestedFileName() << " !" << std::endl;
    }
}

void WebviewHandler::OnTakeFocus(CefRefPtr<CefBrowser> browser, bool next){
    CEF_REQUIRE_UI_THREAD();
    // this gets never called ?
}

bool WebviewHandler::OnSetFocus(CefRefPtr<CefBrowser> browser, CefFocusHandler::FocusSource source){
    CEF_REQUIRE_UI_THREAD();
    return false;
}

void WebviewHandler::OnGotFocus(CefRefPtr<CefBrowser> browser){
    CEF_REQUIRE_UI_THREAD();
    CefFocusHandler::OnGotFocus(browser);
}

void WebviewHandler::closeBrowser(int64_t map_key) {
    //CEF_REQUIRE_UI_THREAD();

    browser_list_[map_key].get()->GetHost().get()->CloseBrowser(false);
}

void WebviewHandler::CloseAllBrowsers(bool force_close) {
//#ifdef OS_MAC
    //macos seems not to find the bind symbol
    //but since we are running single threaded this should be no problem
    //CEF_REQUIRE_UI_THREAD();
//#else
    if (!CefCurrentlyOn(TID_UI)) {
        // Execute on the UI thread.
        CefPostTask(TID_UI, base::BindOnce(&WebviewHandler::CloseAllBrowsers, this,
                                               force_close));
        return;
    }
//#endif
    
    if (browser_list_.empty())
        return;

    if (devToolsBrowser_) {
        devToolsBrowser_.reset();
    }

    BrowserList::const_iterator it = browser_list_.begin();
    for (; it != browser_list_.end(); it++) {
        (*it).second.get()->GetHost()->TryCloseBrowser();// CloseBrowser(force_close);
    }

    browser_list_.clear(); // ?? probably should not do that
}

// static
bool WebviewHandler::IsChromeRuntimeEnabled() {
    static int value = -1;
    if (value == -1) {
        CefRefPtr<CefCommandLine> command_line =
        CefCommandLine::GetGlobalCommandLine();
        value = command_line->HasSwitch("enable-chrome-runtime") ? 1 : 0;
    }
    return value == 1;
}

void WebviewHandler::sendScrollEvent(int x, int y, int deltaX, int deltaY) {
    BrowserList::const_iterator it = active_browser_list_.begin();
    if (it != active_browser_list_.end()) {
        CefMouseEvent ev;
        ev.x = x;
        ev.y = y;

#ifndef __APPLE__
        // The scrolling direction on Windows and Linux is different from MacOS
        deltaY = -deltaY;
        // Flutter scrolls too slowly, it looks more normal by 10x default speed.
        (*it).second->GetHost()->SendMouseWheelEvent(ev, deltaX * 10, deltaY * 10);
#else
        (*it).second->GetHost()->SendMouseWheelEvent(ev, deltaX, deltaY);
#endif


    }
}

void WebviewHandler::changeSize(int64_t map_key, float a_dpi, int w, int h)
{
    webview_cef::getWebviews()->at(map_key)->dpi = a_dpi;
    webview_cef::getWebviews()->at(map_key)->width = w;
    webview_cef::getWebviews()->at(map_key)->height = h;
    browser_list_[map_key].get()->GetHost().get()->WasResized();
}

void WebviewHandler::cursorClick(int x, int y, bool up)
{
    BrowserList::const_iterator it = active_browser_list_.begin();
    if (it != active_browser_list_.end()) {
        CefMouseEvent ev;
        ev.x = x;
        ev.y = y;
        ev.modifiers = EVENTFLAG_LEFT_MOUSE_BUTTON;
        if(up && is_dragging) {
            (*it).second->GetHost()->DragTargetDrop(ev);
            (*it).second->GetHost()->DragSourceSystemDragEnded();
            is_dragging = false;
        } else {
            (*it).second->GetHost()->SendMouseClickEvent(ev, CefBrowserHost::MouseButtonType::MBT_LEFT, up, 1);
        }
    }
}

void WebviewHandler::cursorMove(int x , int y, bool dragging)
{
    BrowserList::const_iterator it = active_browser_list_.begin();
    if (it != active_browser_list_.end()) {
        CefMouseEvent ev;
        ev.x = x;
        ev.y = y;
        if(dragging) {
            ev.modifiers = EVENTFLAG_LEFT_MOUSE_BUTTON;
        }
        if(is_dragging && dragging) {
            (*it).second->GetHost()->DragTargetDragOver(ev, DRAG_OPERATION_EVERY);
        } else {
            (*it).second->GetHost()->SendMouseMoveEvent(ev, false);
        }
    }
}

bool WebviewHandler::StartDragging(CefRefPtr<CefBrowser> browser,
                                  CefRefPtr<CefDragData> drag_data,
                                  DragOperationsMask allowed_ops,
                                  int x,
                                  int y){
    BrowserList::const_iterator it = active_browser_list_.begin();
    if (it != active_browser_list_.end()) {
        CefMouseEvent ev;
        ev.x = x;
        ev.y = y;
        ev.modifiers = EVENTFLAG_LEFT_MOUSE_BUTTON;
        (*it).second->GetHost()->DragTargetDragEnter(drag_data, ev, DRAG_OPERATION_EVERY);
        is_dragging = true;
    }
    return true;
}

void WebviewHandler::sendKeyEvent(CefKeyEvent& ev)
{
    BrowserList::const_iterator it = active_browser_list_.begin();
    if (it != active_browser_list_.end()) {
#ifdef OS_MAC
        if(ev.modifiers & EVENTFLAG_COMMAND_DOWN && ev.type == KEYEVENT_CHAR) {
            switch(ev.unmodified_character) {
                case 'a':
                (*it).second->GetMainFrame()->SelectAll();
                return;
                case 'c':
                (*it).second->GetMainFrame()->Copy();
                return;
                case 'v':
                (*it).second->GetMainFrame()->Paste();
                return;
                case 'x':
                (*it).second->GetMainFrame()->Cut();
                return;
                case 'y':
                (*it).second->GetMainFrame()->Redo();
                return;
                case 'z':
                (*it).second->GetMainFrame()->Undo();
                return;
            }
        }
#endif //OS_MAC
        (*it).second->GetHost()->SendKeyEvent(ev);
    }
}

void WebviewHandler::loadUrl(int64_t map_key, std::string url) {
        browser_list_[map_key]->GetMainFrame()->LoadURL(url);
}

void WebviewHandler::goForward(int64_t map_key) {
        browser_list_[map_key]->GetMainFrame()->GetBrowser()->GoForward();
}

void WebviewHandler::goBack(int64_t map_key) {
        browser_list_[map_key]->GetMainFrame()->GetBrowser()->GoBack();
}

void WebviewHandler::reload(int64_t map_key) {
    browser_list_[map_key]->GetMainFrame()->GetBrowser()->Reload();
}

void WebviewHandler::openDevTools(int64_t map_key) {
        CefWindowInfo windowInfo;
#ifdef _WIN32
        windowInfo.SetAsPopup(nullptr, "DevTools");
#endif
        if (devToolsBrowser_.get() != nullptr) {
            devToolsBrowser_.get()->GetHost()->CloseBrowser(false);
        } else {
            browser_list_[map_key]->GetHost()->ShowDevTools(windowInfo, this, CefBrowserSettings(), CefPoint());
        }
}

void WebviewHandler::setCookie(const std::string& domain, const std::string& key, const std::string& value){
    CefRefPtr<CefCookieManager> manager = CefCookieManager::GetGlobalManager(nullptr);
    if(manager){
        CefCookie cookie;
		CefString(&cookie.path).FromASCII("/");
		CefString(&cookie.name).FromString(key.c_str());
		CefString(&cookie.value).FromString(value.c_str());

		if (!domain.empty()) {
			CefString(&cookie.domain).FromString(domain.c_str());
		}

		cookie.httponly = true;
		cookie.secure = false;
		std::string httpDomain = "https://" + domain + "/cookiestorage";
		manager->SetCookie(httpDomain, cookie, nullptr);
    }
}

void WebviewHandler::deleteCookie(const std::string& domain, const std::string& key)
{
    CefRefPtr<CefCookieManager> manager = CefCookieManager::GetGlobalManager(nullptr);
    if (manager) {
        std::string httpDomain = "https://" + domain + "/cookiestorage";
        manager->DeleteCookies(httpDomain, key, nullptr);
    }
}

bool WebviewHandler::getCookieVisitor(){
    if(!m_CookieVisitor.get())
    {
        m_CookieVisitor = new WebviewCookieVisitor();
        m_CookieVisitor->setOnVisitComplete([=](std::map<std::string, std::map<std::string, std::string>> cookies){
            if(cookies.size() == 1){
                if(onUrlCookieVisitedCb){
                    onUrlCookieVisitedCb(cookies);
                }
            }else if(cookies.size() > 1){
                if(onAllCookieVisitedCb){
                    onAllCookieVisitedCb(cookies);
                }
            }
        });
        if (!m_CookieVisitor.get())
		{
			return false;
		}
    }
    return true;
}

bool WebviewHandler::visitAllCookies(){
    CefRefPtr<CefCookieManager> manager = CefCookieManager::GetGlobalManager(nullptr);
    if (!manager || !getCookieVisitor())
	{
		return false;
	}

    return manager->VisitAllCookies(m_CookieVisitor);
}

bool WebviewHandler::visitUrlCookies(const std::string& domain, const bool& isHttpOnly){
    CefRefPtr<CefCookieManager> manager = CefCookieManager::GetGlobalManager(nullptr);
    if (!manager || !getCookieVisitor())
	{
		return false;
	}

    std::string httpDomain = "https://" + domain + "/cookiestorage";

    return manager->VisitUrlCookies(httpDomain, isHttpOnly, m_CookieVisitor);
}

bool WebviewHandler::setJavaScriptChannels(int64_t map_key, const std::map<std::string, std::vector<std::string>> channels)
{
    std::string extensionCode = "";
    for(auto& channel : channels)
    {
        extensionCode += channel.first;
        extensionCode += " = (e,r) => {external.JavaScriptChannel('";
        extensionCode += channel.first;
        extensionCode += "',e,r)};";
        for(auto& func : channel.second) {
            extensionCode += channel.first;
            extensionCode += ".";
            extensionCode += func;
        }
    }

    return executeJavaScript(map_key, extensionCode);
}

bool WebviewHandler::sendJavaScriptChannelCallBack(const bool error, const std::string result, const std::string callbackId, const std::string frameId)
{
    CefRefPtr<CefProcessMessage> message = CefProcessMessage::Create(kExecuteJsCallbackMessage);
    CefRefPtr<CefListValue> args = message->GetArgumentList();
    args->SetInt(0, atoi(callbackId.c_str()));
    args->SetBool(1, error);
    args->SetString(2, result);
    BrowserList::iterator bit = browser_list_.begin();
    for (; bit != browser_list_.end(); ++bit) {
        CefRefPtr<CefFrame> frame = (*bit).second->GetMainFrame();
        if (frame->GetIdentifier() == atoll(frameId.c_str()))
        {
            frame->SendProcessMessage(PID_RENDERER, message);
            return true;
        }
    }
    return false;
}

bool WebviewHandler::executeJavaScript(int64_t map_key, const std::string code)
{
    CefRefPtr<CefBrowser> browser = browser_list_[map_key];
    if(!code.empty())
    {
        if(browser) {
            CefRefPtr<CefFrame> frame = browser.get()->GetMainFrame();
            if (frame) {
                frame->ExecuteJavaScript(code, frame->GetURL(), 0);
                return true;
            }
        }
    }
    return false;
}

void WebviewHandler::GetViewRect(CefRefPtr<CefBrowser> browser, CefRect &rect) {
    CEF_REQUIRE_UI_THREAD();
    
    rect.x = rect.y = 0;
    rect.width = rect.height = 1;
    bool found = false;

    // TODO: we need to somehow get rid of this iteration
    webview_cef::WebviewMap::iterator wit = webview_cef::getWebviews()->begin();
    for (;  wit != webview_cef::getWebviews()->end(); ++wit) {
        WebviewData *view = (*wit).second.get()->isWebviewForBrowser(browser.get()->GetIdentifier());
        if (view) {
            if (view->width >= 1) {
                rect.width = view->width;
            }

            if (view->height >= 1) {
                rect.height = view->height;
            }
            found = true;
        }
    }
    if(!found) {
        printf("CEF: no view found for browser %d\n", browser->GetIdentifier());
    }
}

bool WebviewHandler::GetScreenInfo(CefRefPtr<CefBrowser> browser, CefScreenInfo& screen_info) {
    //todo: hi dpi support
    screen_info.device_scale_factor  = this->dpi;
    return false;
}

void WebviewHandler::OnPaint(CefRefPtr<CefBrowser> browser, CefRenderHandler::PaintElementType type,
                            const CefRenderHandler::RectList &dirtyRects, const void *buffer, int w, int h) {
    webview_cef::WebviewMap::iterator wit = webview_cef::getWebviews()->begin();
    for (;  wit != webview_cef::getWebviews()->end(); ++wit) {
        WebviewData *view = (*wit).second.get()->isWebviewForBrowser(browser.get()->GetIdentifier());
        if (view) {
            view->paintCallback(buffer, w, h);
            break;
        }
    }
    //onPaintCallback(buffer, w, h);
}

void WebviewHandler::setClientFocus(int64_t map_key, bool focus) {
    
    CefRefPtr<CefBrowser> browser = browser_list_[map_key];

    if(focus) {
        active_browser_list_.insert(std::make_pair(map_key, browser_list_[map_key]));
        browser_list_[map_key]->GetHost()->SetFocus(true);
        browser_list_[map_key]->GetHost()->WasHidden(false);
    } else {
        active_browser_list_.erase(map_key);
        browser_list_[map_key]->GetHost()->SetFocus(false);
        browser_list_[map_key]->GetHost()->WasHidden(true);
    }

    //printf("active browsers:\n");
    //BrowserList::const_iterator it = active_browser_list_.begin();
    //for(;it != active_browser_list_.end(); it++) {
    //    printf("id: %d\n", (*it).second->GetIdentifier());
    //}
}

void deleteFolderContents(const std::string& folderPath, const std::vector<std::string>& includeList) {
    if(!fs::exists(folderPath) || !fs::is_directory(folderPath)) {
        return;
    }
    for (const auto& entry : fs::directory_iterator(folderPath)) {
        std::string fileName = entry.path().filename().string();

        // Check if the file/folder should be includeList
        if (!includeList.empty() && std::find(includeList.begin(), includeList.end(), fileName) == includeList.end()) {
        } else {
            if (fs::is_regular_file(entry.path()) || fs::is_symlink(entry.path())) {
                std::error_code ec; //windows will most likely fail so ignore error
                fs::remove(entry.path(), ec);  // Delete file or symbolic link
                std::cout << ec.message() << std::endl;
            } else if (fs::is_directory(entry.path())) {
                deleteFolderContents(entry.path().filename().string(), {});
                std::error_code ec; //windows will most likely fail so ignore error
                fs::remove_all(entry.path(), ec);  // Delete entire directory
                std::cout << ec.message() << std::endl;
            }
        }
    }
}

void WebviewHandler::clearAllCaches(std::string cachePath, int64_t map_key) {
    if(!map_key) {
        std::vector<std::string> include = {}; //{"Cache", "Code Cache", "DawnCache", "GPUCache"};
        deleteFolderContents(cachePath, include);
    } else {
        clearCache(map_key, true);
    }
}

void WebviewHandler::clearCache(int64_t map_key, bool fullCache) {
    if(!map_key) {
        clearAllCaches(webview_cef::getGlobalCachePath(), 0);
        return;
    }
    CefRefPtr<CefBrowser> browser = browser_list_[map_key];
    WebviewData *view = webview_cef::getWebviews()->at(map_key).get();
    if(view->isWebviewForBrowser(browser.get()->GetIdentifier())) {
        std::string initUrl = view->initUrl;
        if(!initUrl.empty()) {
            if(initUrl.at(initUrl.length()-1) != '/') {
                initUrl.append("/");
            }
            CefString url = CefString(initUrl);
            CefRefPtr<CefDictionaryValue> dict = CefDictionaryValue::Create();
            
            printf("CEF: clear Cache for %s\n", url.ToString().c_str());
            dict->SetString("storageKey", url);
            if(fullCache) {
                dict->SetString("storageTypes", "appcache,cache_storage,cookies,indexeddb,local_storage,service_workers,websql");
            } else {
                dict->SetString("storageTypes", "appcache,cache_storage");
            }
            browser->GetHost()->ExecuteDevToolsMethod(0, "Storage.clearDataForStorageKey", dict);
        }
    }
}
