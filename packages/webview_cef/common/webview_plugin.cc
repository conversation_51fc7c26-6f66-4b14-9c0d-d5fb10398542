#include "webview_plugin.h"

#include "webview_app.h"

#include <math.h>
#include <memory>
#include <thread>
#include <iostream>
#include <optional>
#include <chrono>

#include "webview_data.h"
#include "webview_request_handler.h"

// #include <mutex>
using namespace std::chrono_literals;
namespace webview_cef {
	bool init = false;
	bool isFocused = false;
	std::function<void(std::string, PluginValue*)> invokeFunc;

	std::string cachePath = "";
    
    CefRefPtr<WebviewHandler> handler(new WebviewHandler());
    CefRefPtr<WebviewApp> app(new WebviewApp(handler));
    CefMainArgs mainArgs;

	static const std::optional<std::pair<int, int>> GetPointFromArgs(
		const PluginValue *args) {
		const PluginValueList* list = std::get_if<PluginValueList>(args);
		if (!list || list->size() < 3) {
			return std::nullopt;
		}
		auto x = (*list)[1].LongValue();
		auto y = (*list)[2].LongValue();
		if (!x && !y) {
			return std::nullopt;
		}
		return std::make_pair((int)x, (int)y);
	}

    void initCEFProcesses(CefMainArgs args){
		mainArgs = args;
	    CefExecuteProcess(mainArgs, app, nullptr);
    }

	void exitCEFProcesses() {
		if (init) {
			handler.get()->CloseAllBrowsers(true);
			//webview_cef::killTimer();
			CefShutdown();
			init = false;
		}
	}

    void sendKeyEvent(CefKeyEvent& ev)
    {
        handler.get()->sendKeyEvent(ev);
    }

	void startCEF() {
		CefSettings cefs;
		if(!getGlobalCachePath().empty()) {
#ifdef OS_WIN
		std::string cache(getGlobalCachePath());
		cache.append("\\cef_cache");
		CefString(&cefs.cache_path).FromASCII(cache.c_str());
#else
		std::string cache(getGlobalCachePath());
		cache.append("/cef_cache");
		CefString(&cefs.cache_path).FromASCII(cache.c_str());
#endif
		} else {
#ifdef OS_WIN
		TCHAR lAppDat[100];
		TCHAR cachepath[100];
		GetEnvironmentVariable(L"LOCALAPPDATA", lAppDat, 100);
		wsprintf(cachepath, L"%s\\.cef_cache", lAppDat);
		CefString(&cefs.cache_path).FromWString(cachepath);
		CefString cefPath = CefString(&cefs.cache_path);
		std::string path(cefPath.ToString());
		setGlobalCachePath(path);
#else
		std::string home(std::getenv("HOME"));
		home.append("/.cache/cef_cache");
		CefString(&cefs.cache_path).FromASCII(home.c_str());
		setGlobalCachePath(home);
#endif
		}
		cefs.windowless_rendering_enabled = true;
		cefs.no_sandbox = false;
		cefs.log_severity = LOGSEVERITY_DISABLE;

		CefInitialize(mainArgs, cefs, app.get(), nullptr);
#if defined(OS_WIN)
		//std::thread(CefRunMessageLoop());
		//CefShutdown();
#endif
	}

    void doMessageLoopWork()
    {
		CefDoMessageLoopWork();
    }

	void createNewBrowser(int64_t textureId, std::string initUrl) {
		app.get()->CreateNewBrowser(textureId, initUrl);
	}

	void destroyBrowser(int64_t textureId){
		handler.get()->closeBrowser(textureId);
		webview_cef::getWebviews()->erase(textureId);
		if(webview_cef::getWebviews()->empty()) {
			init = false;
			/*handler->onPaintCallback = NULL;
			handler->onUrlChangedCb = NULL;
			handler->onTitleChangedCb = NULL;
			handler->onAllCookieVisitedCb = NULL;
			handler->onUrlCookieVisitedCb = NULL;
			handler->onJavaScriptChannelMessage = NULL;
			handler->onKillMessageLoop = NULL;
			handler->onBrowserCreated = NULL;*/
		}
	}

    int HandleMethodCall(std::string name, PluginValue* values, PluginValue* response) {
        int result = -1;
		if (name.compare("loadUrl") == 0) {
			const PluginValueList* list = std::get_if<PluginValueList>(values);
			const int64_t textureId = *std::get_if<int64_t>(&(*list)[0]);
			if (const auto url = std::get_if<std::string>(&(*list)[1])) {
				handler.get()->loadUrl(textureId, *url);
				result = 1;
			}
		}
		else if (name.compare("clearAllCaches") == 0) {
            const PluginValueList* list = std::get_if<PluginValueList>(values);
			const int64_t textureId = *std::get_if<int64_t>(&(*list)[0]);
			handler.get()->clearAllCaches(cachePath);
			result = 1;
			(void)textureId;
		}
		else if (name.compare("setSize") == 0) {
            const PluginValueList* list = std::get_if<PluginValueList>(values);
			const int64_t textureId = *std::get_if<int64_t>(&(*list)[0]);
			const auto dpi = *std::get_if<double>(&(*list)[1]);
			const auto width = *std::get_if<double>(&(*list)[2]);
			const auto height = *std::get_if<double>(&(*list)[3]);
			handler.get()->changeSize(textureId, (float)dpi, (int)std::round(width), (int)std::round(height));
			result = 1;
		}
		else if (name.compare("cursorClickDown") == 0) {
			const PluginValueList* list = std::get_if<PluginValueList>(values);
			const int64_t textureId = *std::get_if<int64_t>(&(*list)[0]);
			const auto point = GetPointFromArgs(values);
			handler.get()->cursorClick(point->first, point->second, false);
			result = 1;
			(void)textureId;
		}
		else if (name.compare("cursorClickUp") == 0) {
			const PluginValueList* list = std::get_if<PluginValueList>(values);
			const int64_t textureId = *std::get_if<int64_t>(&(*list)[0]);
			const auto point = GetPointFromArgs(values);
			handler.get()->cursorClick(point->first, point->second, true);
			result = 1;
			(void)textureId;
		}
		else if (name.compare("cursorMove") == 0) {
			const PluginValueList* list = std::get_if<PluginValueList>(values);
			const int64_t textureId = *std::get_if<int64_t>(&(*list)[0]);
			const auto point = GetPointFromArgs(values);
			handler.get()->cursorMove(point->first, point->second, false);
			result = 1;
			(void)textureId;
		}
		else if (name.compare("cursorDragging") == 0) {
			const PluginValueList* list = std::get_if<PluginValueList>(values);
			const int64_t textureId = *std::get_if<int64_t>(&(*list)[0]);
			const auto point = GetPointFromArgs(values);
			handler.get()->cursorMove(point->first, point->second, true);
			result = 1;
			(void)textureId;
		}
		else if (name.compare("setScrollDelta") == 0) {
			const PluginValueList* list = std::get_if<PluginValueList>(values);
			const int64_t textureId = *std::get_if<int64_t>(&(*list)[0]);
			auto x = (*list)[1].LongValue();
			auto y = (*list)[2].LongValue();
			auto deltaX = (*list)[3].LongValue();
			auto deltaY = (*list)[4].LongValue();
			handler.get()->sendScrollEvent((int)x, (int)y, (int)deltaX, (int)deltaY);
			result = 1;
			(void)textureId;
		}
		else if (name.compare("goForward") == 0) {
			const PluginValueList* list = std::get_if<PluginValueList>(values);
			const int64_t textureId = *std::get_if<int64_t>(&(*list)[0]);
			handler.get()->goForward(textureId);
			result = 1;
		}
		else if (name.compare("goBack") == 0) {
			const PluginValueList* list = std::get_if<PluginValueList>(values);
			const int64_t textureId = *std::get_if<int64_t>(&(*list)[0]);
			handler.get()->goBack(textureId);
			result = 1;
		}
		else if (name.compare("reload") == 0) {
			const PluginValueList* list = std::get_if<PluginValueList>(values);
			const int64_t textureId = *std::get_if<int64_t>(&(*list)[0]);
			handler.get()->reload(textureId);
			result = 1;
		}
		else if (name.compare("openDevTools") == 0) {
			const PluginValueList* list = std::get_if<PluginValueList>(values);
			const int64_t textureId = *std::get_if<int64_t>(&(*list)[0]);
			handler.get()->openDevTools(textureId);
			result = 1;
		}
		else if (name.compare("setClientFocus") == 0) { // just for linux and windows ?
			const PluginValueList* list = std::get_if<PluginValueList>(values);
			const int64_t textureId = *std::get_if<int64_t>(&(*list)[0]);
			bool focused = *std::get_if<bool>(&(*list)[1]);
			isFocused = focused;
			handler.get()->setClientFocus(textureId, focused);
			result = 1;
		}
		else if (name.compare("setCookie") == 0) {
			const PluginValueList* list = std::get_if<PluginValueList>(values);
			const int64_t textureId = *std::get_if<int64_t>(&(*list)[0]);
			const auto domain = *std::get_if<std::string>(&(*list)[1]);
			const auto key = *std::get_if<std::string>(&(*list)[2]);
			const auto value = *std::get_if<std::string>(&(*list)[3]);
			handler.get()->setCookie(domain, key, value);
			result = 1;
			(void)textureId;
		}
		else if (name.compare("deleteCookie") == 0) {
			const PluginValueList* list = std::get_if<PluginValueList>(values);
			const int64_t textureId = *std::get_if<int64_t>(&(*list)[0]);
			const auto domain = *std::get_if<std::string>(&(*list)[1]);
			const auto key = *std::get_if<std::string>(&(*list)[2]);
			handler.get()->deleteCookie(domain, key);
			result = 1;
			(void)textureId;
		}
		else if (name.compare("visitAllCookies") == 0) {
			const PluginValueList* list = std::get_if<PluginValueList>(values);
			const int64_t textureId = *std::get_if<int64_t>(&(*list)[0]);
			handler.get()->visitAllCookies();
			result = 1;
			(void)textureId;
		}
		else if (name.compare("visitUrlCookies") == 0) {
			const PluginValueList* list = std::get_if<PluginValueList>(values);
			const int64_t textureId = *std::get_if<int64_t>(&(*list)[0]);
			const auto domain = *std::get_if<std::string>(&(*list)[1]);
			const auto isHttpOnly = *std::get_if<bool>(&(*list)[2]);
			handler.get()->visitUrlCookies(domain, isHttpOnly);
			result = 1;
			(void)textureId;
		}
		else if (name.compare("setJavaScriptChannels") == 0) {
			const PluginValueList* list = std::get_if<PluginValueList>(values);
			const int64_t textureId = *std::get_if<int64_t>(&(*list)[0]);
			const auto jsChannels = *std::get_if<PluginValueMap>(&(*list)[1]);
			std::map<std::string, std::vector<std::string>> channels;
			for (auto& jsChannel : jsChannels) {
				std::string channelName = *std::get_if<std::string>(&(jsChannel.first));
				std::vector<std::string> JSfunctions;
				auto functions = *std::get_if<PluginValueList>(&(jsChannel.second));
				for (auto& function : functions) {
					JSfunctions.push_back(*std::get_if<std::string>(&function));
				}
				channels.insert(std::make_pair(channelName, JSfunctions));
			}
			webview_cef::getWebviews()->at(textureId)->JSChannels = channels;
			handler.get()->setJavaScriptChannels(textureId, channels);
			result = 1;
		}
		else if (name.compare("sendJavaScriptChannelCallBack") == 0) {
			const PluginValueList* list = std::get_if<PluginValueList>(values);
			const int64_t textureId = *std::get_if<int64_t>(&(*list)[0]);
			const auto error = *std::get_if<bool>(&(*list)[1]);
			const auto ret = *std::get_if<std::string>(&(*list)[2]);
			const auto callbackId = *std::get_if<std::string>(&(*list)[3]);
			const auto frameId = *std::get_if<std::string>(&(*list)[4]);
			handler.get()->sendJavaScriptChannelCallBack(error, ret, callbackId, frameId);
			result = 1;
			(void)textureId;
		}
		else if (name.compare("executeJavaScript") == 0) {
			const PluginValueList* list = std::get_if<PluginValueList>(values);
			const int64_t textureId = *std::get_if<int64_t>(&(*list)[0]);
			const auto code = *std::get_if<std::string>(&(*list)[1]);
			handler.get()->executeJavaScript(textureId, code);
			result = 1;
		}
		else if (name.compare("basicAuth") == 0) {
			const PluginValueList* list = std::get_if<PluginValueList>(values);
			const int64_t textureId = *std::get_if<int64_t>(&(*list)[0]);
			const bool canceled = *std::get_if<bool>(&(*list)[1]);
			const auto username = std::get_if<std::string>(&(*list)[2]);
			const auto password = std::get_if<std::string>(&(*list)[3]);
			handler.get()->m_RequestHandler.get()->Continue(canceled, username ? *username : "", password ? *password : "");
			result = 1;
			(void)textureId;
		}
		else if (name.compare("clearCache") == 0) {
			const PluginValueList* list = std::get_if<PluginValueList>(values);
			int64_t textureId = 0;
			if (std::holds_alternative<int32_t>((*list)[0])) {
				textureId = *std::get_if<int32_t>(&(*list)[0]);
			}
			else {
				textureId = *std::get_if<int64_t>(&(*list)[0]);
			}
			const bool full = *std::get_if<bool>(&(*list)[1]);		
			handler.get()->clearCache(textureId, full);
			result = 1;
		}
		/*else if (name.compare("navigationResponse") == 0) {
			const PluginValueList* list = std::get_if<PluginValueList>(values);
			const int64_t textureId = *std::get_if<int64_t>(&(*list)[0]);
			const bool response = *std::get_if<bool>(&(*list)[1]);
			(void)textureId;
			(void)response;
		}*/
		else {
			result = 0;
		}
		if(response == nullptr){
			response = new PluginValue(nullptr);
		}
        return result;
	}

	void SwapBufferFromBgraToRgba(void* _dest, const void* _src, int width, int height) {
		int32_t* dest = (int32_t*)_dest;
		int32_t* src = (int32_t*)_src;
		int32_t rgba;
		int32_t bgra;
		int length = width * height;
		for (int i = 0; i < length; i++) {
			bgra = src[i];
			// BGRA in hex = 0xAARRGGBB.
			rgba = (bgra & 0x00ff0000) >> 16 // Red >> Blue.
				| (bgra & 0xff00ff00) // Green Alpha.
				| (bgra & 0x000000ff) << 16; // Blue >> Red.
			dest[i] = rgba;
		}
	}

	/*bool waitForResponse(int64_t textureId) {
		WebviewData* view = getWebviews()->at(textureId).get();
		if (!view->wait_for_response) {
			const PluginValueList* list = std::get_if<PluginValueList>(&view->encode_response);
			const int64_t texId = *std::get_if<int64_t>(&(*list)[0]);
			const bool response = *std::get_if<bool>(&(*list)[1]);
			view->encode_response = nullptr;
			(void)texId;
			return response;
		} else {
			return true;
		}
		return true;
	}*/

	bool NavRequest(int64_t textureId, std::string url) {
	/*
		WebviewData* view = getWebviews()->at(textureId).get();
		std::string host = view->domain;
		if(host.find(url) == std::string::npos && url.find(host) == std::string::npos) {
			std::cout << "CEF: prevent navigation to " << url << std::endl;
			// handle no nav
			PluginValueMap retMap;
			retMap[PluginValue("textureId")] = PluginValue(std::to_string(textureId));
			retMap[PluginValue("requestUrl")] = PluginValue(url);
			invokeFunc("onNotHandledNavigationRequest", new PluginValue(retMap));
			return false;
		}*/
		return true;
	}
	
	void setCallBacks(std::function<void()> killMessageLoop)	{
		if (!init)
		{
			handler.get()->onKillMessageLoop = killMessageLoop;
			handler.get()->onUrlChangedCb = [](std::string textureId, std::string url)
			{
				if (invokeFunc)
				{
					PluginValueMap retMap;
					retMap[PluginValue("textureId")] = PluginValue(textureId);
					retMap[PluginValue("url")] = PluginValue(url);

					invokeFunc("urlChanged", new PluginValue(retMap));
				}
			};

			handler.get()->onTitleChangedCb = [](std::string textureId, std::string title)
			{
				if (invokeFunc)
				{
					PluginValueMap retMap;
					retMap[PluginValue("textureId")] = PluginValue(textureId);
					retMap[PluginValue("title")] = PluginValue(title);
					invokeFunc("titleChanged", new PluginValue(retMap));
				}
			};
			handler.get()->onAllCookieVisitedCb = [](std::map<std::string, std::map<std::string, std::string>> cookies)
			{
				if (invokeFunc)
				{
					PluginValueMap retMap;
					for (auto &cookie : cookies)
					{
						PluginValueMap tempMap;
						for (auto &c : cookie.second)
						{
							tempMap[PluginValue(c.first)] = PluginValue(c.second);
						}
						retMap[PluginValue(cookie.first)] = PluginValue(tempMap);
					}
					invokeFunc("allCookiesVisited", new PluginValue(retMap));
				}
			};

			handler.get()->onUrlCookieVisitedCb = [](std::map<std::string, std::map<std::string, std::string>> cookies)
			{
				if (invokeFunc)
				{
					PluginValueMap retMap;
					for (auto &cookie : cookies)
					{
						PluginValueMap tempMap;
						for (auto &c : cookie.second)
						{
							tempMap[PluginValue(c.first)] = PluginValue(c.second);
						}
						retMap[PluginValue(cookie.first)] = PluginValue(tempMap);
					}
					invokeFunc("urlCookiesVisited", new PluginValue(retMap));
				}
			};

			handler.get()->onJavaScriptChannelMessage = [](std::string textureId, std::string channelName, std::string message, std::string callbackId, std::string frameId)
			{
				if (invokeFunc)
				{
					PluginValueMap retMap;
					retMap[PluginValue("textureId")] = PluginValue(textureId);
					retMap[PluginValue("channel")] = PluginValue(channelName);
					retMap[PluginValue("message")] = PluginValue(message);
					retMap[PluginValue("callbackId")] = PluginValue(callbackId);
					retMap[PluginValue("frameId")] = PluginValue(frameId);
					invokeFunc("javascriptChannelMessage", new PluginValue(retMap));
				}
			};

			handler.get()->onPageLoadStart = [](std::string textureId, std::string url)
			{
				if(invokeFunc)
				{
					PluginValueMap retMap;
					retMap[PluginValue("textureId")] = PluginValue(textureId);
					retMap[PluginValue("url")] = PluginValue(url);
					invokeFunc("pageLoadStart", new PluginValue(retMap));
				}
			};
			handler.get()->onPageLoadEnd = [](std::string textureId, std::string url)
			{
				if(invokeFunc)
				{
					PluginValueMap retMap;
					retMap[PluginValue("textureId")] = PluginValue(textureId);
					retMap[PluginValue("url")] = PluginValue(url);
					invokeFunc("pageLoadEnd", new PluginValue(retMap));
				}
			};
			handler.get()->onBrowserCreated = [](std::string textureId)
			{
				if (invokeFunc)
				{
					PluginValueMap retMap;
					retMap[PluginValue("textureId")] = PluginValue(textureId);
					invokeFunc("browserInited", new PluginValue(retMap));
				}
			};
			handler.get()->m_RequestHandler.get()->onShowAuthDialog = [](int64_t textureId){
				if (invokeFunc)
				{
					PluginValueMap retMap;
					retMap[PluginValue("textureId")] = PluginValue(std::to_string(textureId));
					invokeFunc("showAuthDialog", new PluginValue(retMap));
				}
			};
			handler.get()->m_RequestHandler.get()->onNavigationRequest = [](int64_t textureId, std::string url)->bool{
				if (invokeFunc) {
					return NavRequest(textureId, url);
				}
				return true;
			};

			startCEF();
			init = true;
		}
    }

	void setInvokeMethodFunc(std::function<void(std::string, PluginValue*)> func){
		invokeFunc = func;
	}

	bool getPluginIsFocused() {
		return isFocused;
	}
}