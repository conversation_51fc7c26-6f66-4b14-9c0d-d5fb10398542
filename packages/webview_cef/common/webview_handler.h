// Copyright (c) 2013 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.

#ifndef CEF_TESTS_CEFSIMPLE_SIMPLE_HANDLER_H_
#define CEF_TESTS_CEFSIMPLE_SIMPLE_HANDLER_H_

#include "include/cef_client.h"

#include <functional>
#include <list>
#include <iostream>

#include "webview_cookieVisitor.h"
#include "webview_request_handler.h"

class WebviewHandler : public CefClient,
public CefDisplayHandler,
public CefLifeSpanHandler,
public CefLoadHandler,
public CefRenderHandler,
public CefFocusHandler,
public CefKeyboardHandler, 
public CefDevToolsMessageObserver,
public CefDownloadHandler {
public:
    std::function<void(const void*, int32_t width, int32_t height)> onPaintCallback;
    std::function<void(std::string, std::string url)> onUrlChangedCb;
    std::function<void(std::string, std::string title)> onTitleChangedCb;
    std::function<void(std::map<std::string, std::map<std::string, std::string>>)> onAllCookieVisitedCb;
    std::function<void(std::map<std::string, std::map<std::string, std::string>>)> onUrlCookieVisitedCb;
    std::function<void(std::string, std::string, std::string, std::string, std::string)> onJavaScriptChannelMessage;
    std::function<void()> onKillMessageLoop;
    std::function<void(std::string, std::string)> onPageLoadStart;
    std::function<void(std::string, std::string)> onPageLoadEnd;
    std::function<void(std::string)> onBrowserCreated;

    explicit WebviewHandler();
    ~WebviewHandler();
    
    // Provide access to the single global instance of this object.
    static WebviewHandler* GetInstance();
    
    // CefClient methods:
    virtual CefRefPtr<CefDisplayHandler> GetDisplayHandler() override {
        return this;
    }
    virtual CefRefPtr<CefLifeSpanHandler> GetLifeSpanHandler() override {
        return this;
    }
    virtual CefRefPtr<CefLoadHandler> GetLoadHandler() override { return this; }
    virtual CefRefPtr<CefRenderHandler> GetRenderHandler() override { return this; }
    virtual CefRefPtr<CefFocusHandler> GetFocusHandler() override { return this; }
    virtual CefRefPtr<CefDownloadHandler> GetDownloadHandler() override { return this; }
    virtual CefRefPtr<CefRequestHandler> GetRequestHandler() override {return m_RequestHandler; }

	bool OnProcessMessageReceived(
        CefRefPtr<CefBrowser> browser,
		CefRefPtr<CefFrame> frame,
		CefProcessId source_process,
		CefRefPtr<CefProcessMessage> message) override;

    
    // CefDisplayHandler methods:
    virtual void OnTitleChange(CefRefPtr<CefBrowser> browser,
                               const CefString& title) override;
    virtual void OnAddressChange(CefRefPtr<CefBrowser> browser,
                                 CefRefPtr<CefFrame> frame,
                                 const CefString& url) override;
    
    // CefLifeSpanHandler methods:
    virtual void OnAfterCreated(CefRefPtr<CefBrowser> browser) override;
    virtual bool DoClose(CefRefPtr<CefBrowser> browser) override;
    virtual void OnBeforeClose(CefRefPtr<CefBrowser> browser) override;
    virtual bool OnBeforePopup(CefRefPtr<CefBrowser> browser,
                               CefRefPtr<CefFrame> frame,
                               const CefString& target_url,
                               const CefString& target_frame_name,
                               WindowOpenDisposition target_disposition,
                               bool user_gesture,
                               const CefPopupFeatures& popupFeatures,
                               CefWindowInfo& windowInfo,
                               CefRefPtr<CefClient>& client,
                               CefBrowserSettings& settings,
                               CefRefPtr<CefDictionaryValue>& extra_info,
                               bool* no_javascript_access) override;
    
    // CefLoadHandler methods:
    virtual void OnLoadingStateChange(CefRefPtr<CefBrowser> browser,
                                    bool isLoading,
                                    bool canGoBack,
                                    bool canGoForward) override;

    virtual void OnLoadStart(CefRefPtr<CefBrowser> browser,
                           CefRefPtr<CefFrame> frame,
                           TransitionType transition_type) override;

    virtual void OnLoadEnd(CefRefPtr<CefBrowser> browser,
                             CefRefPtr<CefFrame> frame,
                             int httpStatusCode) override;

    virtual void OnLoadError(CefRefPtr<CefBrowser> browser,
                             CefRefPtr<CefFrame> frame,
                             ErrorCode errorCode,
                             const CefString& errorText,
                             const CefString& failedUrl) override;

    /*virtual bool CanDownload(CefRefPtr<CefBrowser> browser,
                           const CefString& url,
                           const CefString& request_method) override;*/

    virtual void OnBeforeDownload(
      CefRefPtr<CefBrowser> browser,
      CefRefPtr<CefDownloadItem> download_item,
      const CefString& suggested_name,
      CefRefPtr<CefBeforeDownloadCallback> callback) override;

    virtual void OnDownloadUpdated(CefRefPtr<CefBrowser> browser,
                                 CefRefPtr<CefDownloadItem> download_item,
                                 CefRefPtr<CefDownloadItemCallback> callback) override;

    // CefFocusHandler methods:
    // Called when the browser component is about to loose focus. For instance, if
    // focus was on the last HTML element and the user pressed the TAB key. |next|
    // will be true if the browser is giving focus to the next component and false
    // if the browser is giving focus to the previous component.
    virtual void OnTakeFocus(CefRefPtr<CefBrowser> browser, bool next) override;

    // Called when the browser component is requesting focus. |source| indicates
    // where the focus request is originating from. Return false to allow the
    // focus to be set or true to cancel setting the focus.
    virtual bool OnSetFocus(CefRefPtr<CefBrowser> browser, FocusSource source) override;

    // Called when the browser component has received focus.
    virtual void OnGotFocus(CefRefPtr<CefBrowser> browser) override;
    
    // CefRenderHandler methods:
    virtual void GetViewRect(CefRefPtr<CefBrowser> browser, CefRect& rect) override;
    virtual void OnPaint(CefRefPtr<CefBrowser> browser, PaintElementType type, const RectList& dirtyRects, const void* buffer, int width, int height) override;
    virtual bool GetScreenInfo(CefRefPtr<CefBrowser> browser, CefScreenInfo& screen_info) override;
    virtual bool StartDragging(CefRefPtr<CefBrowser> browser,
                               CefRefPtr<CefDragData> drag_data,
                               DragOperationsMask allowed_ops,
                               int x,
                               int y) override;

    bool OnDevToolsMessage(CefRefPtr<CefBrowser> browser, const void* message, size_t message_size) override
    {
        //uint8_t data[message_size];
        //data = (uint8_t*)message;
        //std::cout << "OnDevToolsMessage+++" << std::endl;
        std::cout << "CEF: Result from CDP:" << std::endl;
        std::cout << (char*)message << std::endl;
        return true;
    }

    void OnDevToolsMethodResult(CefRefPtr<CefBrowser> browser, int message_id, bool success, const void* result, size_t result_size) override
    {
        //std::cout << "OnDevToolsMethodResult+++" << std::endl;
    }

    void OnDevToolsEvent(CefRefPtr<CefBrowser> browser, const CefString& method, const void* params, size_t params_size) override
    {
        //std::cout << "OnDevToolsEvent+++" << std::endl;
    }

    void OnDevToolsAgentAttached(CefRefPtr<CefBrowser> browser) override
    {
        //std::cout << "OnDevToolsAgentAttached+++" << std::endl;
    }

    void OnDevToolsAgentDetached(CefRefPtr<CefBrowser> browser) override
    {
        //std::cout << "OnDevToolsAgentDetached+++" << std::endl;
    }
    
    // Request that all existing browser windows close.
    void CloseAllBrowsers(bool force_close);
    
    // Returns true if the Chrome runtime is enabled.
    static bool IsChromeRuntimeEnabled();
    
    void sendScrollEvent(int x, int y, int deltaX, int deltaY);
    void changeSize(int64_t map_key, float a_dpi, int width, int height);
    void cursorClick(int x, int y, bool up);
    void cursorMove(int x, int y, bool dragging);
    void sendKeyEvent(CefKeyEvent& ev);
    void loadUrl(int64_t map_key, std::string url);
    void goForward(int64_t map_key);
    void goBack(int64_t map_key);
    void reload(int64_t map_key);
    void openDevTools(int64_t map_key);
    
    void setCookie(const std::string& domain, const std::string& key, const std::string& value);
    void deleteCookie(const std::string& domain, const std::string& key);
    bool visitAllCookies();
    bool visitUrlCookies(const std::string& domain, const bool& isHttpOnly);

    bool setJavaScriptChannels(int64_t map_key, const std::map<std::string, std::vector<std::string>> channels);
    bool sendJavaScriptChannelCallBack(const bool error, const std::string result, const std::string callbackId, const std::string frameId);
    bool executeJavaScript(int64_t map_key, const std::string code);

    void setClientFocus(int64_t map_key, bool focus);

    void closeBrowser(int64_t map_key);

    void onBrowserCreatedCB(int64_t browserId, int64_t map_key);

    void clearAllCaches(std::string cachePath, int64_t map_key = 0);
    void clearCache(int64_t map_key, bool fullCache);
    
    CefRefPtr<RequestHandler> m_RequestHandler;
private:
    bool getCookieVisitor();

    //uint32_t width = 1;
    //uint32_t height = 1;
    float dpi = 1.0;
    bool is_dragging = false;
    
    // List of existing browser windows. Only accessed on the CEF UI thread.
    typedef std::map<int64_t, CefRefPtr<CefBrowser>> BrowserList;
    BrowserList browser_list_;
    //
    BrowserList active_browser_list_;

    std::vector<CefRefPtr<CefBrowser>> tmpBrowserList;

    CefRefPtr<CefBrowser> devToolsBrowser_;
    CefRefPtr<CefDevToolsMessageObserver> devObserver_;
    CefRefPtr<CefRegistration> devRegistration_;

    // Include the default reference counting implementation.
    IMPLEMENT_REFCOUNTING(WebviewHandler);

    CefRefPtr<WebviewCookieVisitor> m_CookieVisitor;
};

#endif  // CEF_TESTS_CEFSIMPLE_SIMPLE_HANDLER_H_
