#include "webview_request_handler.h"
#include "webview_data.h"
#include <iostream>

bool RequestHandler::OnBeforeBrowse(CefRefPtr<CefBrowser> browser,
                              CefRefPtr<CefFrame> frame,
                              CefRefPtr<CefRequest> request,
                              bool user_gesture,
                              bool is_redirect) {

    if(frame->IsMain()) {
        webview_cef::WebviewMap::iterator wit = webview_cef::getWebviews()->begin();
        for (;  wit != webview_cef::getWebviews()->end(); ++wit) {
            WebviewData *view = (*wit).second.get()->isWebviewForBrowser(browser.get()->GetIdentifier());
            if (view) {
                return !onNavigationRequest(view->textureId, request->GetURL().ToString().c_str());
                break;
            }
        }
    }
    return false;
}

bool RequestHandler::GetAuthCredentials(CefRefPtr<CefBrowser> browser,
                                  const CefString& origin_url,
                                  bool isProxy,
                                  const CefString& host,
                                  int port,
                                  const CefString& realm,
                                  const CefString& scheme,
                                  CefRefPtr<CefAuthCallback> callback) {

    cb = callback;
    if(scheme.compare("basic") == 0) {
        if(onShowAuthDialog) {
            webview_cef::WebviewMap::iterator wit = webview_cef::getWebviews()->begin();
            for (;  wit != webview_cef::getWebviews()->end(); ++wit) {
                WebviewData *view = (*wit).second.get()->isWebviewForBrowser(browser.get()->GetIdentifier());
                if (view) {
                    if(!CefCurrentlyOn(TID_UI)) {
                        CefPostTask(TID_UI, base::BindOnce(&RequestHandler::ShowAuthDialog, this, view->textureId));
                    } else {
                        onShowAuthDialog(view->textureId);
                    }
                    return true;
                }
            }
        }
    }
    return false;
}

void RequestHandler::Continue(bool canceled, std::string username, std::string password) {

    if(!canceled) {
        cb->Continue(username, password);
        cb = nullptr;
    } else {
        cb->Cancel();
        cb = nullptr;
    }
}