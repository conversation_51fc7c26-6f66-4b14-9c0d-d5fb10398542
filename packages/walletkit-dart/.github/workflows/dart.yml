name: TestSuite

on:
  schedule:
    - cron: "0 0 * * *"
  push:
    branches:
      - main
  pull_request:

env:
  REJECT_SEED: ${{ secrets.REJECT_SEED }}
  TRON_SEED: ${{ secrets.TRON_SEED }}
  ETHERSCAN_API_KEYS: ${{ secrets.ETHERSCAN_API_KEYS }}
  TRONSCAN_API_KEYS: ${{ secrets.TRONSCAN_API_KEYS }}
  DEV_SEED: ${{ secrets.DEV_SEED }}

jobs:
  lint:
    name: Static code analysis and run unit tests
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          submodules: "recursive"

      - name: Create pubspec_overrides.yaml for current package
        run: echo "resolution:" > pubspec_overrides.yaml

      - name: Create pubspec_overrides.yaml for bip39 package
        run: echo "resolution:" > packages/bip39/pubspec_overrides.yaml

      - uses: actions/cache@v3
        with:
          path: |
            ~/.pub-cache
            .dart_tool
            packages/bip39/.dart_tool
          key: ${{ runner.os }}-dart-${{ hashFiles('**/pubspec.lock') }}
          restore-keys: ${{ runner.os }}-dart-

      - name: Setup Dart SDK
        uses: dart-lang/setup-dart@v1
        with:
          sdk: "stable"

      - name: Print Dart version
        run: dart --version

      - name: Get dependencies
        run: |
          dart pub get
          (cd packages/bip39 && dart pub get)

      - name: Dart analyze
        run: dart analyze

  test:
    needs: lint
    name: ${{ matrix.test-path }} tests
    runs-on: ubuntu-latest
    strategy:
      matrix:
        test-path:
          [
            "test/ci/amount",
            "test/ci/derivation",
            "test/ci/evm",
            "test/ci/external_transactions",
            "test/ci/fetching",
            "test/ci/proof_of_payment",
            "test/ci/sending",
            "test/ci/tron",
            "test/ci/rlp",
          ]
    steps:
      - uses: actions/checkout@v3
        with:
          submodules: "recursive"

      - name: Create pubspec_overrides.yaml for current package
        run: echo "resolution:" > pubspec_overrides.yaml

      - name: Create pubspec_overrides.yaml for bip39 package
        run: echo "resolution:" > packages/bip39/pubspec_overrides.yaml

      - uses: actions/cache@v3
        with:
          path: |
            ~/.pub-cache
            .dart_tool
            packages/bip39/.dart_tool
          key: ${{ runner.os }}-dart-${{ hashFiles('**/pubspec.lock') }}
          restore-keys: ${{ runner.os }}-dart-

      - name: Setup Dart SDK
        uses: dart-lang/setup-dart@v1
        with:
          sdk: "stable"

      - name: Get dependencies
        run: |
          dart pub get
          (cd packages/bip39 && dart pub get)

      - name: Run CI tests with coverage
        run: |
          echo "::group::Running tests for ${{ matrix.test-path }}"
          dart pub global activate coverage
          dart test --coverage=coverage ${{ matrix.test-path }}
          dart pub global run coverage:format_coverage --lcov --in=coverage --out=coverage/lcov.info --package=. --report-on=lib
          echo "::endgroup::"

      - name: Set artifact name
        run: echo "ARTIFACT_NAME=coverage-$(echo '${{ matrix.test-path }}' | tr '/' '-')" >> $GITHUB_ENV

      - name: Upload coverage artifacts
        uses: actions/upload-artifact@v4
        with:
          name: ${{ env.ARTIFACT_NAME }}
          path: coverage/lcov.info

  coverage-report:
    if: github.ref == 'refs/heads/main'
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Download coverage reports
        uses: actions/download-artifact@v4
        with:
          path: coverage-artifacts

      - name: Install lcov
        run: sudo apt-get install -y lcov

      - name: Merge coverage reports
        run: |
          mkdir -p coverage
          find coverage-artifacts -name lcov.info -exec echo -a {} \; | xargs lcov -o coverage/lcov.info

      - name: Generate HTML report
        run: genhtml coverage/lcov.info --output-directory coverage/html

      - name: Calculate coverage percentage
        run: |
          COVERAGE=$(lcov --summary coverage/lcov.info | grep "lines......" | cut -d ' ' -f 4 | cut -d '%' -f 1)
          echo "COVERAGE=$COVERAGE" >> $GITHUB_ENV
          mkdir -p coverage/badges
          # Create shields.io compatible JSON endpoint
          echo "{\"schemaVersion\": 1, \"label\": \"coverage\", \"message\": \"${COVERAGE}%\", \"color\": \"$(if [ $(echo "$COVERAGE >= 80" | bc -l) -eq 1 ]; then echo 'brightgreen'; elif [ $(echo "$COVERAGE >= 60" | bc -l) -eq 1 ]; then echo 'yellow'; else echo 'red'; fi)\"}" > coverage/badges/coverage.json

      - name: Upload coverage report
        uses: actions/upload-artifact@v4
        with:
          name: coverage-report
          path: |
            coverage/html
            coverage/badges

  deploy-docs-and-coverage:
    if: github.ref == 'refs/heads/main'
    needs: [coverage-report, lint, test]
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pages: write
      id-token: write

    steps:
      - uses: actions/checkout@v3
        with:
          submodules: "recursive"

      - name: Create pubspec_overrides.yaml for current package
        run: echo "resolution:" > pubspec_overrides.yaml

      - name: Create pubspec_overrides.yaml for bip39 package
        run: echo "resolution:" > packages/bip39/pubspec_overrides.yaml

      - name: Setup Dart SDK
        uses: dart-lang/setup-dart@v1
        with:
          sdk: "stable"

      - name: Get dependencies
        run: |
          cd packages/bip39
          dart pub get
          cd ../..
          dart pub get

      - name: Install dartdoc
        run: dart pub global activate dartdoc

      - name: Generate API docs
        run: dart pub global run dartdoc

      - name: Download coverage report and badge
        uses: actions/download-artifact@v4
        with:
          name: coverage-report
          path: coverage-report

      - name: Prepare docs directory
        run: |
          # Create directory structure
          rm -rf docs
          mkdir -p docs/coverage docs/badges
            
          # Copy API docs
          cp -R doc/api/* docs/
            
          # Copy coverage report and badges
          cp -R coverage-report/html/* docs/coverage/
          cp -R coverage-report/badges/* docs/badges/

      - name: Deploy to Github Pages
        uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./docs
          force_orphan: true
          commit_message: "docs: update documentation and coverage"
          enable_jekyll: false
          full_commit_message: "docs: update API documentation and coverage report"
