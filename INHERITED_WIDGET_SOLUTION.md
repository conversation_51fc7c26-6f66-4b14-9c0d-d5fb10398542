# InheritedWidget Solution for WebOns API Context Management

## Overview

The final solution uses <PERSON><PERSON><PERSON>'s **InheritedWidget** pattern to provide a clean, stateless API with optional WebView context isolation. This approach follows Flutter best practices and provides the flexibility you requested.

## Key Architecture Principles

### 1. **Single Stateless API Instance**
- One `RefactoredWebOnApi` class with static methods
- No global state management
- Clean, predictable API surface

### 2. **InheritedWidget for Context Propagation**
- `WebViewContextProvider` extends `InheritedWidget`
- Provides WebView-specific data down the widget tree
- Nullable WebView context in API calls

### 3. **Flexible Context Resolution**
- API works with or without WebView context
- Automatic context resolution from widget tree
- Graceful fallback to main context

## Implementation Details

### **WebView Context Data**
```dart
class WebViewContextData {
  final String instanceId;
  final String currentUrl;
  final bool isLoading;
  final Map<String, dynamic> metadata;

  const WebViewContextData({
    required this.instanceId,
    required this.currentUrl,
    this.isLoading = false,
    this.metadata = const {},
  });
}
```

### **InheritedWidget Provider**
```dart
class WebViewContextProvider extends InheritedWidget {
  final WebViewContextData? webViewData;

  const WebViewContextProvider({
    Key? key,
    required Widget child,
    this.webViewData,
  }) : super(key: key, child: child);

  static WebViewContextData? maybeOf(BuildContext context) {
    return context.dependOnInheritedWidgetOfExactType<WebViewContextProvider>()?.webViewData;
  }

  @override
  bool updateShouldNotify(WebViewContextProvider oldWidget) {
    return webViewData != oldWidget.webViewData;
  }
}
```

### **Simplified API Context**
```dart
typedef WebOnApiContext = ({
  NomoManifest manifest,
  Map<String, dynamic> args,
  BuildContext context,
  BuildContext? webViewContext, // Nullable WebView-specific context
});
```

### **Stateless API Handler**
```dart
class RefactoredWebOnApi {
  static Future<Map<String, dynamic>> handleCommand({
    required String functionName,
    required NomoManifest manifest,
    required Map<String, dynamic> args,
    required BuildContext context,
  }) async {
    final command = WebOnCommand.fromString(functionName);

    // Get WebView context from InheritedWidget if available
    final webViewData = WebViewContextProvider.maybeOf(context);
    final webViewContext = webViewData != null ? context : null;

    final apiContext = (
      manifest: manifest,
      args: args,
      context: context,
      webViewContext: webViewContext,
    );

    return await command.execute(apiContext);
  }
}
```

## Usage Patterns

### **1. Basic API Call (No WebView Context)**
```dart
final result = await RefactoredWebOnApi.handleCommand(
  functionName: 'nomoGetPlatformInfo',
  manifest: manifest,
  args: {},
  context: context, // Regular BuildContext
);
```

### **2. WebView-Aware API Call**
```dart
// Wrap WebView in provider
WebViewContextProvider(
  webViewData: WebViewContextData(
    instanceId: 'my_webview',
    currentUrl: 'https://example.com',
  ),
  child: MyWebViewWidget(),
)

// API call automatically detects WebView context
final result = await RefactoredWebOnApi.handleCommand(
  functionName: 'nomoShowDialog',
  manifest: manifest,
  args: {'message': 'Hello'},
  context: context, // Context with WebView data
);
```

### **3. WebView-Specific Commands**
```dart
final class ShowWebViewDialogCommand extends WebOnCommand {
  @override
  Future<Map<String, dynamic>> execute(WebOnApiContext context) async {
    final message = context.args['message'] as String? ?? 'No message';
    
    // Use WebView-specific context if available
    final targetContext = context.webViewContext ?? context.context;
    final webViewData = WebViewContextProvider.maybeOf(targetContext);
    
    final result = await showDialog<bool>(
      context: targetContext,
      builder: (ctx) => AlertDialog(
        title: Text('WebView ${webViewData?.instanceId ?? 'Unknown'}'),
        content: Text(message),
      ),
    );
    
    return {'dialogResult': result ?? false};
  }
}
```

### **4. Multiple WebView Instances**
```dart
// Main WebView
WebViewContextProvider(
  webViewData: WebViewContextData(
    instanceId: 'main_webview',
    currentUrl: 'https://main.example.com',
  ),
  child: Column(
    children: [
      MainWebViewWidget(),
      
      // Nested popup WebView
      WebViewContextProvider(
        webViewData: WebViewContextData(
          instanceId: 'popup_webview',
          currentUrl: 'https://popup.example.com',
        ),
        child: PopupWebViewWidget(),
      ),
    ],
  ),
)
```

## Benefits

### **1. Flutter Best Practices**
- Uses InheritedWidget as intended
- Follows Flutter's context propagation patterns
- No global state or singletons

### **2. Stateless API Design**
- Single API instance
- No internal state management
- Predictable behavior

### **3. Flexible Context Handling**
- Works with or without WebView context
- Automatic context resolution
- Graceful degradation

### **4. Type Safety**
- Nullable WebView context prevents assumptions
- Compile-time checking
- Clear API contracts

### **5. Performance**
- InheritedWidget's efficient update mechanism
- No unnecessary rebuilds
- Minimal overhead

### **6. Testability**
- Easy to mock WebView contexts
- Isolated testing of commands
- Clear dependency injection

## Migration Path

### **Phase 1: Update API Calls**
```dart
// Before
webOnApi(functionName: 'test', manifest: manifest, args: {}, context: context);

// After
RefactoredWebOnApi.handleCommand(
  functionName: 'test', 
  manifest: manifest, 
  args: {}, 
  context: context
);
```

### **Phase 2: Add WebView Providers**
```dart
// Wrap WebViews with providers
WebViewContextProvider(
  webViewData: WebViewContextData(
    instanceId: webViewId,
    currentUrl: initialUrl,
  ),
  child: WebViewWidget(),
)
```

### **Phase 3: Update Commands**
```dart
// Commands automatically get WebView context if available
final webViewData = context.webViewContext != null 
    ? WebViewContextProvider.maybeOf(context.webViewContext!)
    : null;
```

## Key Advantages

1. **Single API Instance**: No multiple instances or global state
2. **InheritedWidget Pattern**: Proper Flutter context propagation
3. **Nullable WebView Context**: Explicit handling of WebView-specific operations
4. **Backward Compatible**: Works with existing code
5. **Type Safe**: Compile-time checking of context usage
6. **Performance Optimized**: Efficient context resolution
7. **Easy Testing**: Clear separation of concerns

This solution provides the clean, stateless API you requested while maintaining the flexibility to handle multiple WebView instances through Flutter's established InheritedWidget pattern.
