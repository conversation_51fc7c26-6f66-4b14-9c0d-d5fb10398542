# WebOns Refactoring Guide: Modern Dart Features

This guide outlines comprehensive refactoring suggestions to improve the maintainability of the WebOns codebase using the latest Dart language features.

## 1. Replace Large Switch Statements with Pattern Matching

### Current Problem
The main `webOnApi` function contains 50+ else-if statements, making it hard to maintain and extend.

### Solution: Sealed Classes + Pattern Matching
```dart
// Define command types
sealed class WebOnCommand {
  factory WebOnCommand.fromString(String name) => switch (name) {
    "nomoSignEvmTransaction" => const SignEvmTransactionCommand(),
    "nomoGetBalance" => const GetBalanceCommand(),
    _ => UnknownCommand(name),
  };
}

// Handle with pattern matching
Future<Map<String, dynamic>> handleCommand(WebOnCommand command) async {
  return switch (command) {
    SignEvmTransactionCommand() => await _handleSignTransaction(),
    GetBalanceCommand() => await _handleGetBalance(),
    UnknownCommand(name: final n) => throw WebOnError('Unknown: $n'),
  };
}
```

**Benefits:**
- Exhaustive checking at compile time
- Better IDE support and refactoring
- Easier to add new commands
- Type-safe command handling

## 2. Enhanced Error Handling with Sealed Classes

### Current Problem
Inconsistent error handling across the codebase with mixed exceptions and null returns.

### Solution: Result Types
```dart
sealed class WebOnResult<T> {
  const WebOnResult();
}

final class WebOnSuccess<T> extends WebOnResult<T> {
  final T data;
  const WebOnSuccess(this.data);
}

final class WebOnError<T> extends WebOnResult<T> {
  final String message;
  final Exception? cause;
  const WebOnError(this.message, [this.cause]);
}

// Usage
Future<WebOnResult<String>> getBalance() async {
  try {
    final balance = await fetchBalance();
    return WebOnSuccess(balance);
  } catch (e) {
    return WebOnError('Failed to get balance', e);
  }
}
```

**Benefits:**
- Explicit error handling
- No more forgotten null checks
- Composable error handling
- Better debugging information

## 3. Records for Complex Return Values

### Current Problem
Heavy use of `Map<String, dynamic>` for return values loses type safety.

### Solution: Named Records
```dart
// Instead of Map<String, dynamic>
typedef WalletInfo = ({
  String address,
  String balance,
  String network,
  bool isConnected,
});

typedef AssetInfo = ({
  String symbol,
  String name,
  int decimals,
  String? contractAddress,
});

// Multiple return values
({bool success, String? error, WalletInfo? wallet}) connectWallet() {
  // Implementation
}
```

**Benefits:**
- Type safety
- Named fields
- IDE autocomplete
- Compile-time checking

## 4. Enhanced Enums with Methods

### Current Problem
Simple enums without behavior, requiring external utility functions.

### Solution: Enhanced Enums
```dart
enum NomoPermission {
  camera('nomo.permission.CAMERA', 'Camera access', RiskLevel.low),
  signEvmTransaction('nomo.permission.SIGN_EVM_TRANSACTION', 'Sign transactions', RiskLevel.high);

  const NomoPermission(this.permissionString, this.description, this.riskLevel);
  
  final String permissionString;
  final String description;
  final RiskLevel riskLevel;
  
  bool get requiresUserConfirmation => switch (this) {
    NomoPermission.signEvmTransaction || 
    NomoPermission.sendAssets => true,
    _ => false,
  };
}
```

**Benefits:**
- Behavior attached to data
- Pattern matching support
- Better organization
- Reduced utility functions

## 5. Extension Types for Type Safety

### Current Problem
String-based identifiers can be mixed up (WebOn IDs, contract addresses, etc.).

### Solution: Extension Types
```dart
extension type WebOnId(String value) {
  WebOnId.fromManifest(String id) : value = 'webon_$id';
  bool get isValid => value.startsWith('webon_');
}

extension type ContractAddress(String value) {
  bool get isValid => value.startsWith('0x') && value.length == 42;
}

extension type ChainId(int value) {
  static const ethereum = ChainId(1);
  static const polygon = ChainId(137);
  
  bool get isMainnet => value == 1 || value == 137;
}
```

**Benefits:**
- Zero runtime cost
- Compile-time type safety
- Prevents mixing up similar types
- Clear API contracts

## 6. Functional Programming Patterns

### Current Problem
Imperative code with manual null checks and error handling.

### Solution: Functional Extensions
```dart
extension OptionExtensions<T> on T? {
  Option<T> get option => this != null ? Some(this!) : const None();
}

extension IterableExtensions<T> on Iterable<T> {
  Option<T> get headOption => isEmpty ? const None() : Some(first);
  
  ({List<T> matching, List<T> notMatching}) partition(bool Function(T) test) {
    // Implementation
  }
}

// Usage
final permissions = manifest.permissions
    .map(NomoPermission.fromString)
    .whereType<NomoPermission>()
    .partition((p) => p.requiresUserConfirmation);
```

**Benefits:**
- Chainable operations
- Null-safe by design
- Functional composition
- Reduced boilerplate

## 7. State Management with Sealed Classes

### Current Problem
State represented with multiple nullable fields and boolean flags.

### Solution: Sealed State Classes
```dart
sealed class WebOnState<T> {
  const WebOnState();
}

final class WebOnLoading<T> extends WebOnState<T> {
  const WebOnLoading();
}

final class WebOnSuccess<T> extends WebOnState<T> {
  final T data;
  const WebOnSuccess(this.data);
}

final class WebOnError<T> extends WebOnState<T> {
  final String message;
  const WebOnError(this.message);
}

// UI handling
Widget buildUI(WebOnState<String> state) {
  return switch (state) {
    WebOnLoading() => const CircularProgressIndicator(),
    WebOnSuccess(data: final data) => Text(data),
    WebOnError(message: final msg) => Text('Error: $msg'),
  };
}
```

**Benefits:**
- Impossible invalid states
- Exhaustive handling
- Clear state transitions
- Better testing

## 8. Async Patterns with Modern Dart

### Current Problem
Inconsistent async error handling and resource management.

### Solution: Structured Async Patterns
```dart
// Resource management with async
Future<T> withResource<T, R>(
  Future<R> Function() acquire,
  Future<void> Function(R) release,
  Future<T> Function(R) use,
) async {
  final resource = await acquire();
  try {
    return await use(resource);
  } finally {
    await release(resource);
  }
}

// Timeout with cancellation
Future<T> withTimeout<T>(
  Future<T> future,
  Duration timeout, {
  T Function()? onTimeout,
}) async {
  return await future.timeout(
    timeout,
    onTimeout: onTimeout,
  );
}
```

## Implementation Priority

### Phase 1: Core Infrastructure (High Impact)
1. **Error Handling**: Implement Result types for critical APIs
2. **Command Pattern**: Refactor main API dispatcher
3. **State Management**: Convert WebView states to sealed classes

### Phase 2: Type Safety (Medium Impact)
1. **Extension Types**: Add for WebOnId, ContractAddress, ChainId
2. **Records**: Replace Map returns in asset APIs
3. **Enhanced Enums**: Upgrade permission system

### Phase 3: Developer Experience (Lower Impact)
1. **Functional Extensions**: Add utility methods
2. **Async Patterns**: Standardize resource management
3. **Documentation**: Update with new patterns

## Migration Strategy

1. **Gradual Migration**: Implement new patterns alongside existing code
2. **Deprecation Warnings**: Mark old patterns as deprecated
3. **Testing**: Ensure backward compatibility during transition
4. **Documentation**: Update examples and guides

## Benefits Summary

- **Type Safety**: Catch errors at compile time
- **Maintainability**: Clearer code structure and intent
- **Performance**: Zero-cost abstractions with extension types
- **Developer Experience**: Better IDE support and refactoring
- **Reliability**: Exhaustive pattern matching prevents bugs
- **Testability**: Easier to mock and test sealed classes

This refactoring will significantly improve code quality while maintaining backward compatibility and leveraging Dart's modern language features.
