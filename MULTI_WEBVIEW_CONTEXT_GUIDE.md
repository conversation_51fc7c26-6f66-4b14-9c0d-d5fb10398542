# Multi-WebView Context Management Guide

## The Problem

When multiple WebView instances are running simultaneously, sharing a single BuildContext can lead to:

1. **Context Conflicts**: UI operations targeting the wrong WebView
2. **State Confusion**: Mixed state between different WebView instances  
3. **Navigation Issues**: Dialogs/navigation happening in wrong context
4. **Memory Leaks**: Contexts not properly cleaned up when WebViews are disposed

## The Solution: WebView Instance Isolation

### 1. **WebView Context Provider**

A centralized manager that maintains separate contexts for each WebView instance:

```dart
class WebViewContextProvider {
  static final Map<String, BuildContext> _contexts = {};
  static final Map<String, WebViewState> _states = {};
  
  static void registerWebView({
    required String instanceId,
    required BuildContext context,
    required String initialUrl,
  }) {
    _contexts[instanceId] = context;
    _states[instanceId] = (
      instanceId: instanceId,
      currentUrl: initialUrl,
      isLoading: false,
      metadata: {},
    );
  }
  
  static BuildContext? getContext(String instanceId) {
    return _contexts[instanceId];
  }
}
```

### 2. **Enhanced API Context**

The API context now includes WebView instance information:

```dart
typedef WebOnApiContext = ({
  NomoManifest manifest,
  Map<String, dynamic> args,
  BuildContext context,           // WebView-specific context
  String webViewInstanceId,       // Unique identifier
  WebViewState? webViewState,     // Instance-specific state
});
```

### 3. **WebView-Aware API Handler**

The API handler automatically resolves the correct context:

```dart
static Future<Map<String, dynamic>> handleCommand({
  required String functionName,
  required NomoManifest manifest,
  required Map<String, dynamic> args,
  required BuildContext context,
  required String webViewInstanceId,
}) async {
  // Get WebView-specific context and state
  final webViewContext = WebViewContextProvider.getContext(webViewInstanceId);
  final webViewState = WebViewContextProvider.getState(webViewInstanceId);
  
  // Use WebView-specific context if available
  final effectiveContext = webViewContext ?? context;
  
  final apiContext = (
    manifest: manifest,
    args: args,
    context: effectiveContext,
    webViewInstanceId: webViewInstanceId,
    webViewState: webViewState,
  );

  return await command.execute(apiContext);
}
```

## Implementation Examples

### **WebView Registration**

When creating a WebView:

```dart
class WebViewWidget extends StatefulWidget {
  final String instanceId;
  final String initialUrl;
  
  @override
  _WebViewWidgetState createState() => _WebViewWidgetState();
}

class _WebViewWidgetState extends State<WebViewWidget> {
  @override
  void initState() {
    super.initState();
    
    // Register this WebView instance
    WidgetsBinding.instance.addPostFrameCallback((_) {
      WebViewContextProvider.registerWebView(
        instanceId: widget.instanceId,
        context: context,
        initialUrl: widget.initialUrl,
      );
    });
  }
  
  @override
  void dispose() {
    // Clean up when WebView is disposed
    WebViewContextProvider.unregisterWebView(widget.instanceId);
    super.dispose();
  }
}
```

### **WebView-Specific Commands**

Commands that need WebView-specific behavior:

```dart
final class ShowDialogInWebViewCommand extends WebOnCommand {
  const ShowDialogInWebViewCommand();
  
  @override
  Future<Map<String, dynamic>> execute(WebOnApiContext context) async {
    final message = context.args['message'] as String;
    
    // Use the specific WebView's context for the dialog
    final result = await showDialog<bool>(
      context: context.context, // This is the WebView-specific context
      builder: (ctx) => AlertDialog(
        title: Text('WebView ${context.webViewInstanceId}'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(ctx).pop(true),
            child: Text('OK'),
          ),
        ],
      ),
    );
    
    return {'dialogResult': result ?? false};
  }
}
```

### **Navigation Commands**

WebView-specific navigation:

```dart
final class NavigateWebViewCommand extends WebOnCommand {
  const NavigateWebViewCommand();
  
  @override
  Future<Map<String, dynamic>> execute(WebOnApiContext context) async {
    final route = context.args['route'] as String;
    
    // Navigate within the specific WebView's context
    Navigator.of(context.context).pushNamed(route);
    
    // Update WebView state
    if (context.webViewState != null) {
      WebViewContextProvider.updateState(
        context.webViewInstanceId,
        context.webViewState!.copyWith(currentUrl: route),
      );
    }
    
    return {'navigated': true, 'instanceId': context.webViewInstanceId};
  }
}
```

## Benefits

### **1. Context Isolation**
- Each WebView has its own BuildContext
- UI operations target the correct WebView
- No cross-contamination between instances

### **2. State Management**
- Independent state for each WebView
- Proper lifecycle management
- Clean separation of concerns

### **3. Memory Management**
- Automatic cleanup when WebViews are disposed
- No memory leaks from stale contexts
- Proper resource management

### **4. Debugging**
- Easy to identify which WebView triggered an API call
- Clear traceability of operations
- Better error reporting

## Usage Patterns

### **Multiple WebView Scenario**

```dart
// Main WebView
WebViewContextProvider.registerWebView(
  instanceId: 'main_webview',
  context: mainContext,
  initialUrl: 'https://main.app.com',
);

// Popup WebView
WebViewContextProvider.registerWebView(
  instanceId: 'popup_webview', 
  context: popupContext,
  initialUrl: 'https://popup.app.com',
);

// API calls are isolated per WebView
await RefactoredWebOnApi.handleWebViewCommand(
  functionName: 'nomoShowDialog',
  manifest: manifest,
  args: {'message': 'Hello from main WebView'},
  webViewInstanceId: 'main_webview', // Targets main WebView
);

await RefactoredWebOnApi.handleWebViewCommand(
  functionName: 'nomoShowDialog', 
  manifest: manifest,
  args: {'message': 'Hello from popup WebView'},
  webViewInstanceId: 'popup_webview', // Targets popup WebView
);
```

### **Lifecycle Management**

```dart
class WebViewManager {
  static void createWebView(String id, String url, BuildContext context) {
    WebViewContextProvider.registerWebView(
      instanceId: id,
      context: context,
      initialUrl: url,
    );
  }
  
  static void destroyWebView(String id) {
    WebViewContextProvider.unregisterWebView(id);
  }
  
  static List<String> getActiveWebViews() {
    return WebViewContextProvider.activeInstances;
  }
}
```

## Migration Strategy

### **Phase 1: Add Instance IDs**
1. Add unique identifiers to all WebView instances
2. Update API calls to include `webViewInstanceId`
3. Maintain backward compatibility

### **Phase 2: Context Registration**
1. Implement `WebViewContextProvider`
2. Register WebViews on creation
3. Update API handler to use WebView-specific contexts

### **Phase 3: State Management**
1. Add WebView state tracking
2. Implement state-aware commands
3. Add lifecycle management

This approach ensures that each WebView instance operates in complete isolation, preventing context conflicts and enabling proper multi-WebView support in the WebOns system.
